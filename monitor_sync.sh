#!/bin/bash

# 全量同步监控脚本
# 用法: ./monitor_sync.sh [refresh_interval]

REFRESH_INTERVAL=${1:-30}
SCRIPT_DIR="/var/www/html"

# 数据库连接配置
DB_HOST="***********"
DB_PORT="3306"
DB_DATABASE="Synchronism"
DB_USERNAME="root"
DB_PASSWORD="768594aaa"

# MySQL命令构建
MYSQL_CMD="mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USERNAME} -p${DB_PASSWORD} ${DB_DATABASE}"

echo "🔍 Oracle数据全量同步监控"
echo "📱 刷新间隔: ${REFRESH_INTERVAL}秒"
echo "🛑 按 Ctrl+C 停止监控"
echo "================================================"

cd "$SCRIPT_DIR"

monitor_loop() {
    while true; do
        clear
        echo "🕐 监控时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "================================================"
        
        # 1. 队列健康状况
        echo "📋 队列状态："
        php artisan queue:health-check --no-interaction
        echo ""
        
        # 2. 同步状态概览
        echo "📊 同步状态概览："
        php artisan oracle:sync-status --limit=10 --no-interaction
        echo ""
        
        # 3. 正在运行的同步
        echo "⚡ 正在运行的同步："
        RUNNING_SYNCS=$($MYSQL_CMD -Bse "
            SELECT CONCAT(
                table_name, 
                ' (', sync_type, ') - 已运行 ', 
                TIMESTAMPDIFF(MINUTE, start_time, NOW()), 
                ' 分钟'
            )
            FROM sync_logs 
            WHERE status = 'running'
            ORDER BY start_time;
        " 2>/dev/null)
        
        if [ -z "$RUNNING_SYNCS" ]; then
            echo "   ✅ 没有正在运行的同步任务"
        else
            echo "$RUNNING_SYNCS"
        fi
        echo ""
        
        # 4. 最近完成的同步
        echo "✅ 最近完成的同步 (最近5条)："
        RECENT_SYNCS=$($MYSQL_CMD -Bse "
            SELECT CONCAT(
                DATE_FORMAT(end_time, '%H:%i:%s'), ' - ',
                table_name, 
                ' (', sync_type, ') - ',
                records_processed, ' 条记录 - ',
                status
            )
            FROM sync_logs 
            WHERE status IN ('completed', 'failed')
            AND DATE(created_at) = CURDATE()
            ORDER BY end_time DESC 
            LIMIT 5;
        " 2>/dev/null)
        
        if [ -z "$RECENT_SYNCS" ]; then
            echo "   ℹ️  今日暂无完成的同步记录"
        else
            echo "$RECENT_SYNCS"
        fi
        echo ""
        
        # 5. 今日同步统计
        echo "📈 今日同步统计："
        TODAY_STATS=$($MYSQL_CMD -Bse "
            SELECT CONCAT(
                '总同步次数: ', COUNT(*), 
                ' | 成功: ', SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END),
                ' | 失败: ', SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END),
                ' | 运行中: ', SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END),
                ' | 总处理记录: ', COALESCE(SUM(records_processed), 0)
            )
            FROM sync_logs 
            WHERE DATE(created_at) = CURDATE();
        " 2>/dev/null)
        
        if [ -z "$TODAY_STATS" ]; then
            echo "   ℹ️  暂无统计数据"
        else
            echo "   $TODAY_STATS"
        fi
        echo ""
        
        # 6. 各表同步状态
        echo "📋 各表最新状态："
        TABLE_STATUS=$($MYSQL_CMD -Bse "
            SELECT CONCAT(
                RPAD(table_name, 12, ' '), ' | ',
                RPAD(status, 10, ' '), ' | ',
                CASE 
                    WHEN status = 'running' THEN CONCAT('运行中 ', TIMESTAMPDIFF(MINUTE, start_time, NOW()), 'min')
                    WHEN end_time IS NOT NULL THEN DATE_FORMAT(end_time, '%H:%i:%s')
                    ELSE '未知'
                END
            )
            FROM (
                SELECT table_name, status, start_time, end_time,
                       ROW_NUMBER() OVER (PARTITION BY table_name ORDER BY created_at DESC) as rn
                FROM sync_logs
                WHERE DATE(created_at) = CURDATE()
            ) latest_per_table
            WHERE rn = 1
            ORDER BY table_name;
        " 2>/dev/null)
        
        if [ -z "$TABLE_STATUS" ]; then
            echo "   ℹ️  暂无表状态数据"
        else
            echo "   表名         | 状态       | 时间/持续时间"
            echo "   ----------------------------------------"
            echo "$TABLE_STATUS"
        fi
        
        echo ""
        echo "================================================"
        echo "💡 提示: 按 Ctrl+C 停止监控，等待 ${REFRESH_INTERVAL}秒 后刷新..."
        
        sleep $REFRESH_INTERVAL
    done
}

# 信号处理
trap 'echo -e "\n\n🛑 监控已停止"; exit 0' INT

# 启动监控循环
monitor_loop 