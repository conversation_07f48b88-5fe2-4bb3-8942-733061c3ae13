-- 优化MySQL触发器：减少不必要的sync_change_log记录
-- 通过比较OLD和NEW值，只在真正发生变化时记录

-- 1. 优化materials表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_materials_after_update;

DELIMITER $$
CREATE TRIGGER trg_materials_after_update
    AFTER UPDATE ON materials
    FOR EACH ROW
BEGIN
    -- 🔧 只有在真正发生变化时才记录到sync_change_log
    -- 排除只有updated_at字段变化的情况
    IF NOT (
        OLD.material_code <=> NEW.material_code AND
        OLD.company_code <=> NEW.company_code AND
        OLD.figure <=> NEW.figure AND
        OLD.unit <=> NEW.unit AND
        OLD.category_code <=> NEW.category_code AND
        OLD.gross_weight <=> NEW.gross_weight AND
        OLD.net_weight <=> NEW.net_weight AND
        OLD.paint_area <=> NEW.paint_area AND
        OLD.work_hours <=> NEW.work_hours AND
        OLD.length <=> NEW.length AND
        OLD.width <=> NEW.width AND
        OLD.height <=> NEW.height AND
        OLD.supply_type <=> NEW.supply_type AND
        OLD.is_package <=> NEW.is_package AND
        OLD.status <=> NEW.status
        -- 注意：不比较created_at和updated_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            pk_old_json,
            created_at,
            updated_at
        ) VALUES (
            'materials',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'material_code', NEW.material_code
            ),
            JSON_OBJECT(
                'company_code', OLD.company_code,
                'material_code', OLD.material_code
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 2. 优化material_translations表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_update;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_update
    AFTER UPDATE ON material_translations
    FOR EACH ROW
BEGIN
    -- 🔧 只有在真正发生变化时才记录到sync_change_log
    IF NOT (
        OLD.material_code <=> NEW.material_code AND
        OLD.company_code <=> NEW.company_code AND
        OLD.locale <=> NEW.locale AND
        OLD.product_name <=> NEW.product_name AND
        OLD.specification <=> NEW.specification
        -- 注意：不比较created_at和updated_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            pk_old_json,
            created_at,
            updated_at
        ) VALUES (
            'material_translations',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'material_code', NEW.material_code,
                'locale', NEW.locale
            ),
            JSON_OBJECT(
                'company_code', OLD.company_code,
                'material_code', OLD.material_code,
                'locale', OLD.locale
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 3. 优化materials表的INSERT触发器（保持现有逻辑）
DROP TRIGGER IF EXISTS trg_materials_after_insert;

DELIMITER $$
CREATE TRIGGER trg_materials_after_insert
    AFTER INSERT ON materials
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        pk_old_json,
        created_at,
        updated_at
    ) VALUES (
        'materials',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'material_code', NEW.material_code
        ),
        JSON_OBJECT(),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 4. 优化materials表的DELETE触发器（保持现有逻辑）
DROP TRIGGER IF EXISTS trg_materials_after_delete;

DELIMITER $$
CREATE TRIGGER trg_materials_after_delete
    AFTER DELETE ON materials
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        pk_old_json,
        created_at,
        updated_at
    ) VALUES (
        'materials',
        'DELETE',
        JSON_OBJECT(),
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'material_code', OLD.material_code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 5. 优化material_translations表的INSERT触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_insert;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_insert
    AFTER INSERT ON material_translations
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        pk_old_json,
        created_at,
        updated_at
    ) VALUES (
        'material_translations',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'material_code', NEW.material_code,
            'locale', NEW.locale
        ),
        JSON_OBJECT(),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 6. 优化material_translations表的DELETE触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_delete;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_delete
    AFTER DELETE ON material_translations
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        pk_old_json,
        created_at,
        updated_at
    ) VALUES (
        'material_translations',
        'DELETE',
        JSON_OBJECT(),
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'material_code', OLD.material_code,
            'locale', OLD.locale
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 7. 创建sync_change_log表的自动清理机制
-- 创建一个清理旧记录的存储过程
DROP PROCEDURE IF EXISTS CleanupSyncChangeLog;

DELIMITER $$
CREATE PROCEDURE CleanupSyncChangeLog()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE record_count INT;
    
    -- 删除30天前的记录
    DELETE FROM sync_change_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    GET DIAGNOSTICS record_count = ROW_COUNT;
    
    -- 记录清理日志
    IF record_count > 0 THEN
        INSERT INTO sync_logs (
            table_name,
            sync_type,
            status,
            records_processed,
            start_time,
            end_time,
            message,
            created_at,
            updated_at
        ) VALUES (
            'sync_change_log',
            'cleanup',
            'success',
            record_count,
            NOW(),
            NOW(),
            CONCAT('自动清理了 ', record_count, ' 条30天前的记录'),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 8. 使用说明
/*
优化说明：

1. **UPDATE触发器优化**：
   - 添加了完整的字段比较逻辑
   - 排除了只有updated_at字段变化的情况
   - 只有真正的业务数据变化才会记录到sync_change_log

2. **性能优化**：
   - 使用 <=> 操作符处理NULL值比较
   - 减少了大量无意义的sync_change_log记录
   - 避免了因updateOrCreate操作导致的时间戳更新记录

3. **自动清理机制**：
   - 创建了CleanupSyncChangeLog存储过程
   - 自动删除30天前的历史记录
   - 可以通过cron定期调用清理

4. **使用方法**：
   ```sql
   -- 在生产环境执行此脚本来优化触发器
   source optimize_triggers.sql;
   
   -- 定期清理历史数据（可以加入cron）
   CALL CleanupSyncChangeLog();
   ```

5. **监控效果**：
   - 执行后观察sync_change_log表的增长速度
   - 应该会显著减少UPDATE类型的记录
   - 只有真正的数据变化才会被记录

6. **回滚方案**：
   如果需要恢复原来的触发器，可以重新创建不带变更检测的版本
*/ 