#!/bin/bash

# 队列工作进程停止脚本

echo "🛑 停止所有队列工作进程..."

# 查找不同类型的队列工作进程
SYNC_PIDS=$(ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | awk '{print $2}')
TRANSFORM_PIDS=$(ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | awk '{print $2}')
OTHER_PIDS=$(ps aux | grep "artisan queue:work" | grep -v "queue=default" | grep -v "queue=transform" | grep -v grep | awk '{print $2}')

# 合并所有PID
ALL_PIDS="$SYNC_PIDS $TRANSFORM_PIDS $OTHER_PIDS"

if [ -z "$ALL_PIDS" ] || [ "$ALL_PIDS" = "  " ]; then
    echo "✅ 没有发现运行中的队列工作进程"
    exit 0
fi

echo "发现以下队列工作进程:"

if [ ! -z "$SYNC_PIDS" ]; then
    echo "🔄 同步队列工作进程:"
    ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | awk '{print "   PID " $2 ": " $11 " " $12 " " $13 " " $14 " " $15 " " $16}'
fi

if [ ! -z "$TRANSFORM_PIDS" ]; then
    echo "🔄 数据转化队列工作进程:"
    ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | awk '{print "   PID " $2 ": " $11 " " $12 " " $13 " " $14 " " $15 " " $16}'
fi

if [ ! -z "$OTHER_PIDS" ]; then
    echo "❓ 其他队列工作进程:"
    ps aux | grep "artisan queue:work" | grep -v "queue=default" | grep -v "queue=transform" | grep -v grep | awk '{print "   PID " $2 ": " $11 " " $12 " " $13 " " $14 " " $15 " " $16}'
fi

echo ""
echo "正在停止进程..."

# 优雅停止所有进程
for PID in $ALL_PIDS; do
    if [ ! -z "$PID" ]; then
        echo "发送 SIGTERM 信号到 PID $PID"
        kill -TERM $PID 2>/dev/null
    fi
done

# 等待进程结束
echo "等待进程优雅退出..."
sleep 5

# 检查是否还有进程运行
REMAINING_SYNC=$(ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | awk '{print $2}')
REMAINING_TRANSFORM=$(ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | awk '{print $2}')
REMAINING_OTHER=$(ps aux | grep "artisan queue:work" | grep -v "queue=default" | grep -v "queue=transform" | grep -v grep | awk '{print $2}')
REMAINING_ALL="$REMAINING_SYNC $REMAINING_TRANSFORM $REMAINING_OTHER"

if [ ! -z "$REMAINING_ALL" ] && [ "$REMAINING_ALL" != "  " ]; then
    echo "⚠️  仍有进程运行，强制终止..."
    for PID in $REMAINING_ALL; do
        if [ ! -z "$PID" ]; then
            echo "发送 SIGKILL 信号到 PID $PID"
            kill -KILL $PID 2>/dev/null
        fi
    done
    sleep 2
fi

# 最终检查
FINAL_SYNC=$(ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | wc -l)
FINAL_TRANSFORM=$(ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | wc -l)
FINAL_OTHER=$(ps aux | grep "artisan queue:work" | grep -v "queue=default" | grep -v "queue=transform" | grep -v grep | wc -l)
FINAL_TOTAL=$((FINAL_SYNC + FINAL_TRANSFORM + FINAL_OTHER))

if [ $FINAL_TOTAL -eq 0 ]; then
    echo "✅ 所有队列工作进程已停止"
    
    # 清理日志文件提示
    echo ""
    echo "💡 提示:"
    echo "- 重新启动: ./start_queue.sh [同步队列数] [转化队列数]"
    echo "- 默认启动: ./start_queue.sh (4个同步+2个转化)"
    echo "- 查看日志:"
    echo "  · 同步队列: tail -f storage/logs/queue-sync-worker-*.log"
    echo "  · 转化队列: tail -f storage/logs/queue-transform-worker-*.log"
    echo "- 状态检查: php artisan test:queue-management"
else
    echo "❌ 部分进程可能仍在运行，请手动检查:"
    echo "   - 同步队列: $FINAL_SYNC 个"
    echo "   - 转化队列: $FINAL_TRANSFORM 个"
    echo "   - 其他队列: $FINAL_OTHER 个"
    echo ""
    echo "剩余进程详情:"
    ps aux | grep "artisan queue:work" | grep -v grep
fi 