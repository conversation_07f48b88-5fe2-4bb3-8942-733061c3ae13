[supervisord]
nodaemon=true
pidfile=/tmp/supervisord.pid
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

; ===================================================================
; 基础配置
; ===================================================================

; 基础配置
[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[program:php-fpm]
command=/usr/local/sbin/php-fpm -F
autostart=true
autorestart=true
priority=10
stderr_logfile=/var/log/php-fpm.err.log
stdout_logfile=/var/log/php-fpm.out.log

; ===================================================================
; 优化后的队列配置 - 基于性能测试结果
; ===================================================================

; 通用 Laravel 队列处理器（默认队列）- 保持原配置
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --timeout=3600 --memory=512 --tries=3 --max-jobs=1000 --max-time=3600
numprocs=5
autostart=true
autorestart=true
startretries=3
stopwaitsecs=3600
stopasgroup=true
killasgroup=true
priority=20
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-worker.log
stdout_logfile_maxbytes=20MB
stdout_logfile_backups=5

; Transform队列 - 快速工作进程（处理轻量转化任务）
; 测试结果：77ms处理时间，适合category、customer、bom等快速任务
[program:laravel-transform-fast]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --queue=transform --timeout=300 --sleep=1 --tries=2 --memory=256 --max-jobs=500
numprocs=6
autostart=true
autorestart=true
startretries=3
stopwaitsecs=300
stopasgroup=true
killasgroup=true
priority=21
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-transform-fast.log
stdout_logfile_maxbytes=20MB
stdout_logfile_backups=5

; Transform队列 - 重量工作进程（处理大数据量转化任务）
; 测试结果：1分45秒处理时间，适合material等大数据量任务
[program:laravel-transform-heavy]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --queue=transform --timeout=3600 --sleep=3 --tries=3 --memory=1024 --max-jobs=100 --max-time=7200
numprocs=4
autostart=true
autorestart=true
startretries=3
stopwaitsecs=3600
stopasgroup=true
killasgroup=true
priority=22
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-transform-heavy.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10

; ===================================================================
; 高峰期临时扩容配置（可按需启用）
; ===================================================================

; 高峰期额外的快速Transform进程
[program:laravel-transform-burst]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --queue=transform --timeout=180 --sleep=1 --tries=2 --memory=256 --max-jobs=200
numprocs=3
autostart=false
autorestart=true
startretries=3
stopwaitsecs=180
stopasgroup=true
killasgroup=true
priority=23
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-transform-burst.log
stdout_logfile_maxbytes=20MB
stdout_logfile_backups=3

; ===================================================================
; 同步任务专用队列（可选）
; ===================================================================

; 同步任务队列 - 用于Oracle数据同步
[program:laravel-sync]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --queue=sync --timeout=7200 --sleep=5 --tries=3 --memory=512 --max-jobs=50
numprocs=2
autostart=true
autorestart=true
startretries=3
stopwaitsecs=7200
stopasgroup=true
killasgroup=true
priority=24
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-sync.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10

; ===================================================================
; 监控和统计
; ===================================================================

; 队列监控进程
[program:queue-monitor]
command=php /var/www/html/artisan queue:health-check --monitor --interval=60
autostart=true
autorestart=true
startretries=3
priority=30
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-monitor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5

; ===================================================================
; 管理脚本
; ===================================================================

; 高峰期管理脚本（手动控制）
; 启动高峰期进程：supervisorctl start laravel-transform-burst:*
; 停止高峰期进程：supervisorctl stop laravel-transform-burst:*
; 重启Transform进程：supervisorctl restart laravel-transform-fast:* laravel-transform-heavy:*

; 统一管理多个Laravel服务的队列（如有多个项目时使用）
; [include]
; files = /etc/supervisor/conf.d/*.conf 