#!/bin/bash

# 全量同步所有表快捷脚本
# 用法: ./full_sync_all.sh

echo "🚀 开始执行所有Oracle表的全量同步"
echo "📅 时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "================================================"

cd "/var/www/html"

# 定义同步表列表（按依赖顺序）
TABLES=(
    "BMAA_T"
    "BMBA_T"
    "IMAA_T"
    "IMAAL_T"
    "IMAF_T"
    "RTAXL_T"
    "PMAB_T"
    "PMAAL_T"
)

TOTAL_TABLES=${#TABLES[@]}
CURRENT=0

echo "📋 计划同步表: ${TOTAL_TABLES}个"
echo "📝 表列表: ${TABLES[*]}"
echo ""

# 执行同步
for TABLE in "${TABLES[@]}"; do
    CURRENT=$((CURRENT + 1))
    
    echo "🔄 [$CURRENT/$TOTAL_TABLES] 同步表: $TABLE"
    echo "⏰ 开始时间: $(date '+%H:%M:%S')"
    
    START_TIME=$(date +%s)
    
    # 执行同步命令
    php artisan oracle:sync --table="$TABLE" --type=full --no-interaction
    EXIT_CODE=$?
    
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    if [ $EXIT_CODE -eq 0 ]; then
        echo "✅ [$CURRENT/$TOTAL_TABLES] $TABLE 同步完成 (耗时: ${DURATION}秒)"
    else
        echo "❌ [$CURRENT/$TOTAL_TABLES] $TABLE 同步失败 (耗时: ${DURATION}秒)"
        echo "💡 建议检查错误日志: tail -f storage/logs/laravel.log"
        
        # 询问是否继续
        echo -n "❓ 是否继续同步其他表? (y/n): "
        read -r CONTINUE
        if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
            echo "🛑 用户选择停止同步"
            exit 1
        fi
    fi
    
    echo "⏰ 完成时间: $(date '+%H:%M:%S')"
    echo "---"
    
    # 短暂暂停，避免过度占用资源
    sleep 2
done

echo ""
echo "🎉 全量同步任务完成！"
echo "📅 完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "📊 建议操作："
echo "1. 检查同步状态: php artisan oracle:sync-status"
echo "2. 检查队列状态: php artisan queue:health-check"
echo "3. 执行数据转化: php artisan transform:data all --company-code=TB"
echo "4. 查看详细日志: tail -f storage/logs/laravel.log" 