#!/bin/bash

# 队列工作进程启动脚本
# 用法: ./start_queue.sh [sync_workers] [transform_workers]
# 默认: 5个同步队列工作进程 + 5个数据转化队列工作进程

SYNC_WORKERS=${1:-5}
TRANSFORM_WORKERS=${2:-5}
PROJECT_DIR="/Users/<USER>/Develop/ERP/Synchronism"

echo "🚀 启动队列工作进程..."
echo "📋 同步队列工作进程: $SYNC_WORKERS 个"
echo "🔄 数据转化队列工作进程: $TRANSFORM_WORKERS 个"
echo ""

cd "$PROJECT_DIR"

# 检查是否已有工作进程在运行
EXISTING_SYNC=$(ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | wc -l)
EXISTING_TRANSFORM=$(ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | wc -l)
EXISTING_GENERAL=$(ps aux | grep "artisan queue:work" | grep -v "queue=default" | grep -v "queue=transform" | grep -v grep | wc -l)

if [ $EXISTING_SYNC -gt 0 ] || [ $EXISTING_TRANSFORM -gt 0 ] || [ $EXISTING_GENERAL -gt 0 ]; then
    echo "⚠️  检测到队列工作进程已在运行:"
    [ $EXISTING_SYNC -gt 0 ] && echo "   - 同步队列: $EXISTING_SYNC 个"
    [ $EXISTING_TRANSFORM -gt 0 ] && echo "   - 转化队列: $EXISTING_TRANSFORM 个"
    [ $EXISTING_GENERAL -gt 0 ] && echo "   - 其他队列: $EXISTING_GENERAL 个"
    echo ""
    echo "如需重新启动，请先运行: ./stop_queue.sh"
    exit 1
fi

echo "🔄 启动同步队列工作进程..."
# 启动同步队列工作进程 (处理默认队列)
for ((i=1; i<=SYNC_WORKERS; i++)); do
    echo "启动同步工作进程 $i/$SYNC_WORKERS"
    nohup php artisan queue:work --queue=default --timeout=3600 --memory=512 --tries=3 --sleep=3 > storage/logs/queue-sync-worker-$i.log 2>&1 &
    WORKER_PID=$!
    echo "同步工作进程 $i PID: $WORKER_PID"
    sleep 1
done

echo ""
echo "🔄 启动数据转化队列工作进程..."
# 启动数据转化队列工作进程 (处理transform队列)
for ((i=1; i<=TRANSFORM_WORKERS; i++)); do
    echo "启动转化工作进程 $i/$TRANSFORM_WORKERS"
    nohup php artisan queue:work --queue=transform --timeout=1800 --memory=256 --tries=2 --sleep=5 > storage/logs/queue-transform-worker-$i.log 2>&1 &
    WORKER_PID=$!
    echo "转化工作进程 $i PID: $WORKER_PID"
    sleep 1
done

sleep 3

# 验证启动结果
RUNNING_SYNC=$(ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | wc -l)
RUNNING_TRANSFORM=$(ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | wc -l)
TOTAL_RUNNING=$((RUNNING_SYNC + RUNNING_TRANSFORM))

echo ""
echo "✅ 启动完成！"
echo "📊 当前运行的队列工作进程:"
echo "   - 同步队列: $RUNNING_SYNC/$SYNC_WORKERS"
echo "   - 转化队列: $RUNNING_TRANSFORM/$TRANSFORM_WORKERS"
echo "   - 总计: $TOTAL_RUNNING/$((SYNC_WORKERS + TRANSFORM_WORKERS))"

if [ $TOTAL_RUNNING -gt 0 ]; then
    echo ""
    echo "📊 队列工作进程状态:"
    
    if [ $RUNNING_SYNC -gt 0 ]; then
        echo "🔄 同步队列工作进程:"
        ps aux | grep "artisan queue:work.*--queue=default" | grep -v grep | awk '{print "   PID " $2 ": " $11 " " $12 " " $13 " " $14 " " $15 " " $16}'
    fi
    
    if [ $RUNNING_TRANSFORM -gt 0 ]; then
        echo "🔄 数据转化队列工作进程:"
        ps aux | grep "artisan queue:work.*--queue=transform" | grep -v grep | awk '{print "   PID " $2 ": " $11 " " $12 " " $13 " " $14 " " $15 " " $16}'
    fi
    
    echo ""
    echo "📝 日志文件:"
    echo "   - 同步队列: storage/logs/queue-sync-worker-*.log"
    echo "   - 转化队列: storage/logs/queue-transform-worker-*.log"
    echo ""
    echo "🛑 停止命令: ./stop_queue.sh"
    echo "📊 状态检查: php artisan test:queue-management"
    echo ""
    echo "💡 使用说明:"
    echo "   - 同步队列处理数据同步任务 (SyncTableJob)"
    echo "   - 转化队列处理数据转化任务 (TransformDataJob)"
    echo "   - 修改工作进程数量: ./start_queue.sh [同步] [转化]"
    echo "   - 例如: ./start_queue.sh 6 3 (6个同步+3个转化)"
else
    echo "❌ 启动失败，请检查错误信息"
    exit 1
fi 