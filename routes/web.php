<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SyncController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// 首页重定向到仪表板（需要登录）
Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard页面
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    
    // 用户资料管理
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // 数据同步监控路由（需要认证）
    Route::prefix('sync')->name('sync.')->group(function () {
        Route::get('/', [SyncController::class, 'index'])->name('index');
        Route::get('/stats', [SyncController::class, 'stats'])->name('stats');
        Route::get('/logs', [SyncController::class, 'logs'])->name('logs');
        Route::get('/status', [SyncController::class, 'status'])->name('status');
        Route::get('/config', [SyncController::class, 'config'])->name('config');
        Route::get('/table/{tableName}', [SyncController::class, 'tableDetail'])->name('table.detail');
        Route::post('/trigger', [SyncController::class, 'triggerSync'])->name('trigger');
    });
});

require __DIR__.'/auth.php';
