<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SyncController;
use App\Http\Controllers\TransformController;
use App\Http\Controllers\ExternalApiController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Dashboard API路由（使用Sanctum SPA认证）
Route::middleware(['auth:sanctum'])->prefix('dashboard')->name('dashboard.')->group(function () {
    Route::get('/stats', [DashboardController::class, 'getSyncStats'])->name('stats');
    Route::get('/queue/status', [DashboardController::class, 'getQueueStatus'])->name('queue.status');
    Route::post('/sync/incremental', [DashboardController::class, 'triggerIncrementalSync'])->name('sync.incremental');
    Route::post('/sync/full', [DashboardController::class, 'triggerFullSync'])->name('sync.full');
    Route::get('/sync/progress', [DashboardController::class, 'getSyncProgress'])->name('sync.progress');
    Route::get('/logs', [DashboardController::class, 'getSyncLogs'])->name('logs');
    Route::get('/batch-status', [DashboardController::class, 'getBatchStatus'])->name('batch.status');
    Route::get('/multi-table-progress', [DashboardController::class, 'getMultiTableProgress'])->name('multi.table.progress');
    Route::get('/table-data-counts', [DashboardController::class, 'getTableDataCounts'])->name('table.data.counts');
    Route::get('/sync-records-24h', [DashboardController::class, 'getSyncRecords24h'])->name('sync.records.24h');
});

// 队列管理API路由（使用Sanctum SPA认证）
Route::middleware(['auth:sanctum'])->prefix('queue')->name('queue.')->group(function () {
    Route::get('/status', [App\Http\Controllers\QueueController::class, 'getQueueStatus'])->name('status');
    Route::post('/start-worker', [App\Http\Controllers\QueueController::class, 'startQueueWorker'])->name('start.worker');
    Route::post('/restart-workers', [App\Http\Controllers\QueueController::class, 'restartQueueWorkers'])->name('restart.workers');
    Route::post('/clear-failed', [App\Http\Controllers\QueueController::class, 'clearFailedJobs'])->name('clear.failed');
    Route::post('/process-pending', [App\Http\Controllers\QueueController::class, 'processPendingJobs'])->name('process.pending');
});

// 数据同步监控API路由（使用Sanctum SPA认证）
Route::middleware(['auth:sanctum'])->prefix('sync')->name('sync.')->group(function () {
    Route::get('/stats', [SyncController::class, 'stats'])->name('stats');
    Route::get('/logs', [SyncController::class, 'logs'])->name('logs');
    Route::get('/status', [SyncController::class, 'status'])->name('status');
    Route::get('/config', [SyncController::class, 'config'])->name('config');
    Route::get('/table/{tableName}', [SyncController::class, 'tableDetail'])->name('table.detail');
    Route::post('/trigger', [SyncController::class, 'triggerSync'])->name('trigger');
});

// 数据转化API路由（使用Sanctum SPA认证）
Route::middleware(['auth:sanctum'])->prefix('transform')->name('transform.')->group(function () {
    Route::get('/stats', [TransformController::class, 'getStats'])->name('stats');
    Route::get('/types', [TransformController::class, 'getTransformTypes'])->name('types');
    Route::post('/single', [TransformController::class, 'transform'])->name('single');
    Route::post('/batch', [TransformController::class, 'batchTransform'])->name('batch');
});

// 第三方系统API路由（使用Sanctum Token认证）
Route::middleware(['auth:sanctum'])->prefix('external')->name('external.')->group(function () {
    // 获取API统计信息
    Route::get('/stats', [ExternalApiController::class, 'getApiStats'])->name('stats');
    
    // 物料分类相关
    Route::get('/categories', [ExternalApiController::class, 'getCategories'])->name('categories');
    
    // 客户相关
    Route::get('/customers', [ExternalApiController::class, 'getCustomers'])->name('customers');
    
    // 物料相关
    Route::get('/materials', [ExternalApiController::class, 'getMaterials'])->name('materials');
    Route::get('/material-translations', [ExternalApiController::class, 'getMaterialTranslations'])->name('material.translations');
    
    // BOM相关
    Route::get('/BOM', [ExternalApiController::class, 'getBOM'])->name('BOM');
    
    // 增量同步相关
    Route::get('/incremental/changes', [ExternalApiController::class, 'getIncrementalChanges'])->name('incremental.changes');
    Route::get('/incremental/record-data', [ExternalApiController::class, 'getChangeRecordData'])->name('incremental.record.data');
    Route::get('/incremental/stats', [ExternalApiController::class, 'getIncrementalStats'])->name('incremental.stats');
});

// 测试API路由（仅开发环境）
if (app()->environment('local', 'testing')) {
    Route::prefix('test')->name('test.api.')->group(function () {
        Route::get('/dashboard/stats', [DashboardController::class, 'getTestSyncStats'])->name('dashboard.stats');
        Route::get('/sync/progress', [DashboardController::class, 'getSyncProgress'])->name('sync.progress');
        Route::get('/multi-table-progress', [DashboardController::class, 'getMultiTableProgress'])->name('multi.table.progress');
        Route::get('/dashboard/batch-status', [DashboardController::class, 'getBatchStatus'])->name('dashboard.batch.status');
        
        // 测试用同步触发路由（无需认证，仅测试环境）
        Route::post('/dashboard/trigger-incremental-sync', [DashboardController::class, 'triggerIncrementalSync'])
            ->name('dashboard.trigger.incremental');
        Route::post('/dashboard/trigger-full-sync', [DashboardController::class, 'triggerFullSync'])
            ->name('dashboard.trigger.full');
    });
}


