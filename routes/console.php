<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// 数据同步定时任务，每分钟1次
Schedule::command('oracle:sync --type=incremental')
    ->everyMinute()
    ->withoutOverlapping(300)  // 5分钟超时，防止长时间运行的任务阻塞
    ->runInBackground()
    ->skip(function () {
        // 添加额外的重复检查机制
        $currentMinute = date('Y-m-d-H-i');
        $lockKey = "sync_running_{$currentMinute}";
        
        // 如果当前分钟已有同步任务运行，跳过执行
        if (Cache::has($lockKey)) {
            Log::warning("检测到同一分钟内的重复同步调度，跳过执行", [
                'minute' => $currentMinute,
                'lock_key' => $lockKey
            ]);
            return true; // 跳过执行
        }
        
        // 设置当前分钟的执行标记，有效期2分钟
        Cache::put($lockKey, true, 120);
        return false; // 继续执行
    })
    ->before(function () {
        // 任务开始前记录日志
        Log::info('Oracle增量同步任务开始执行', [
            'time' => now()->toDateTimeString(),
            'minute' => date('Y-m-d-H-i')
        ]);
    })
    ->onSuccess(function () {
        Log::info('Oracle数据增量同步任务执行成功', [
            'time' => now()->toDateTimeString()
        ]);
    })
    ->onFailure(function () {
        Log::error('Oracle数据增量同步任务执行失败', [
            'time' => now()->toDateTimeString()
        ]);
    });

// 每日全量同步（凌晨2点执行）
// Schedule::command('oracle:sync --type=full')
//     ->dailyAt('02:00')
//     ->withoutOverlapping(3600)  // 1小时超时
//     ->runInBackground()
//     ->appendOutputTo(storage_path('logs/sync-full.log'))
//     ->onSuccess(function () {
//         \Illuminate\Support\Facades\Log::info('Oracle数据全量同步任务执行成功');
//     })
//     ->onFailure(function () {
//         \Illuminate\Support\Facades\Log::error('Oracle数据全量同步任务执行失败');
//     });

// 清理旧的同步日志（每天执行一次）
Schedule::call(function () {
    // 删除3天前的同步日志，且状态为success
    \App\Models\SyncLog::where('created_at', '<', now()->subDays(7))->where('status', 'success')->delete();
    \Illuminate\Support\Facades\Log::info('已清理3天前的同步日志');
})->daily();

// 清理过期的同步锁（每10分钟执行一次）
Schedule::call(function () {
    $pattern = 'sync_running_*';
    $keys = Cache::getRedis()->keys($pattern);
    
    $expiredCount = 0;
    foreach ($keys as $key) {
        $keyName = str_replace(config('cache.prefix') . ':', '', $key);
        // 检查锁的时间戳，超过5分钟的认为是过期锁
        if (preg_match('/sync_running_(\d{4}-\d{2}-\d{2}-\d{2}-\d{2})/', $keyName, $matches)) {
            $lockTime = \Carbon\Carbon::createFromFormat('Y-m-d-H-i', $matches[1]);
            if ($lockTime->diffInMinutes(now()) > 5) {
                Cache::forget($keyName);
                $expiredCount++;
            }
        }
    }
    
    if ($expiredCount > 0) {
        Log::info("清理了 {$expiredCount} 个过期的同步锁");
    }
})->everyTenMinutes();
