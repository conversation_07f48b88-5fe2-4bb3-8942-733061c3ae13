<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('IMAA_T')) {
            Schema::create('IMAA_T', function (Blueprint $table) {
                // 核心字段定义
                $table->decimal('IMAAENT', 5, 0); // 无小数位的5位十进制数
                $table->string('IMAA001', 40);    // 40字符字符串，非空
                
                // 基础属性字段（IMAA002-IMAA045）
                $table->string('IMAA002', 10)->nullable();
                $table->string('IMAA003', 10)->nullable();
                $table->string('IMAA004', 10)->nullable();
                $table->string('IMAA005', 40)->nullable();
                $table->string('IMAA006', 10)->nullable();
                $table->string('IMAA009', 10)->nullable();
                $table->string('IMAA010', 10)->nullable();
                $table->string('IMAA011', 10)->nullable();
                $table->string('IMAA012', 1)->nullable();
                $table->string('IMAA013', 40)->nullable();
                $table->string('IMAA014', 40)->nullable();
                $table->decimal('IMAA016', 20, 6)->nullable();
                $table->decimal('IMAA017', 20, 6)->nullable();
                $table->string('IMAA018', 10)->nullable();
                $table->decimal('IMAA019', 20, 6)->nullable();
                $table->decimal('IMAA020', 20, 6)->nullable();
                $table->decimal('IMAA021', 20, 6)->nullable();
                $table->string('IMAA022', 10)->nullable();
                $table->decimal('IMAA023', 20, 6)->nullable();
                $table->string('IMAA024', 10)->nullable();
                $table->decimal('IMAA025', 20, 6)->nullable();
                $table->string('IMAA026', 10)->nullable();
                $table->string('IMAA027', 1)->nullable();
                $table->decimal('IMAA028', 20, 6)->nullable();
                $table->string('IMAA029', 10)->nullable();
                $table->decimal('IMAA030', 20, 6)->nullable();
                $table->decimal('IMAA031', 20, 6)->nullable();
                $table->string('IMAA032', 10)->nullable();
                $table->decimal('IMAA033', 20, 6)->nullable();
                $table->string('IMAA034', 10)->nullable();
                $table->string('IMAA035', 40)->nullable();
                $table->string('IMAA036', 1)->nullable();
                $table->string('IMAA037', 1)->nullable();
                $table->string('IMAA038', 1)->nullable();
                $table->string('IMAA039', 40)->nullable();
                $table->date('IMAA040')->nullable();
                $table->string('IMAA041', 255)->nullable();
                $table->string('IMAA042', 20)->nullable();
                $table->string('IMAA043', 10)->nullable();
                $table->string('IMAA044', 10)->nullable();
                $table->string('IMAA045', 10)->nullable();
                
                // 扩展属性字段（IMAA100-IMAA146）
                $table->string('IMAA100', 10)->nullable();
                $table->string('IMAA101', 10)->nullable();
                $table->decimal('IMAA102', 5, 0)->nullable();
                $table->decimal('IMAA103', 5, 0)->nullable();
                $table->string('IMAA104', 10)->nullable();
                $table->string('IMAA105', 10)->nullable();
                $table->string('IMAA106', 10)->nullable();
                $table->string('IMAA107', 10)->nullable();
                $table->string('IMAA108', 10)->nullable(); // 索引关联字段
                $table->string('IMAA109', 10)->nullable();
                $table->string('IMAA110', 1)->nullable();
                $table->date('IMAA111')->nullable();
                $table->date('IMAA112')->nullable();
                $table->decimal('IMAA113', 5, 0)->nullable();
                $table->string('IMAA114', 10)->nullable();
                $table->decimal('IMAA115', 20, 6)->nullable();
                $table->decimal('IMAA116', 20, 6)->nullable();
                $table->decimal('IMAA117', 20, 6)->nullable();
                $table->decimal('IMAA118', 5, 0)->nullable();
                $table->decimal('IMAA119', 25, 6)->nullable();
                $table->decimal('IMAA120', 20, 6)->nullable();
                $table->string('IMAA121', 1)->nullable();
                $table->string('IMAA122', 10)->nullable();
                $table->string('IMAA123', 80)->nullable();
                $table->string('IMAA124', 10)->nullable();
                $table->string('IMAA125', 1)->nullable();
                $table->string('IMAA126', 10)->nullable();
                $table->string('IMAA127', 10)->nullable();
                $table->string('IMAA128', 10)->nullable();
                $table->string('IMAA129', 10)->nullable();
                $table->string('IMAA130', 80)->nullable();
                $table->string('IMAA131', 10)->nullable();
                $table->string('IMAA132', 10)->nullable();
                $table->string('IMAA133', 10)->nullable();
                $table->string('IMAA134', 10)->nullable();
                $table->string('IMAA135', 10)->nullable();
                $table->string('IMAA136', 10)->nullable();
                $table->string('IMAA137', 10)->nullable();
                $table->string('IMAA138', 10)->nullable();
                $table->string('IMAA139', 10)->nullable();
                $table->string('IMAA140', 10)->nullable();
                $table->string('IMAA141', 10)->nullable();
                $table->string('IMAA142', 10)->nullable();
                $table->string('IMAA143', 10)->nullable();
                $table->string('IMAA144', 1)->nullable();
                $table->string('IMAA145', 10)->nullable();
                $table->string('IMAA146', 10)->nullable();
                
                // 状态与用户信息字段
                $table->string('IMAASTUS', 10)->nullable();
                $table->string('IMAAOWNID', 20)->nullable();
                $table->string('IMAAOWNDP', 10)->nullable();
                $table->string('IMAACRTID', 20)->nullable();
                $table->string('IMAACRTDP', 10)->nullable();
                $table->timestamp('IMAACRTDT')->nullable();
                $table->string('IMAAMODID', 20)->nullable();
                $table->timestamp('IMAAMODDT')->nullable();
                $table->string('IMAACNFID', 20)->nullable();
                $table->timestamp('IMAACNFDT')->nullable();
                
                // 用户自定义字段（IMAAUD001-IMAAUD030）
                for ($i = 1; $i <= 10; $i++) {
                    $table->string("IMAAUD0" . sprintf("%02d", $i), 40)->nullable();
                }
                for ($i = 11; $i <= 20; $i++) {
                    $table->decimal("IMAAUD0$i", 20, 6)->nullable();
                }
                for ($i = 21; $i <= 30; $i++) {
                    $table->timestamp("IMAAUD0$i")->nullable();
                }
                
                // 扩展属性字段（IMAA147-IMAA175）
                $table->decimal('IMAA147', 20, 6)->nullable();
                $table->decimal('IMAA148', 5, 0)->nullable();
                $table->string('IMAA149', 10)->nullable();
                $table->string('IMAA150', 80)->nullable();
                $table->string('IMAA151', 80)->nullable();
                $table->string('IMAA152', 80)->nullable();
                $table->string('IMAA153', 80)->nullable();
                $table->decimal('IMAA154', 5, 0)->nullable();
                $table->string('IMAA155', 10)->nullable();
                $table->string('IMAA156', 10)->nullable();
                $table->decimal('IMAA157', 20, 6)->nullable();
                $table->date('IMAA158')->nullable();
                $table->decimal('IMAA159', 20, 6)->nullable();
                $table->decimal('IMAA160', 20, 6)->nullable();
                $table->string('IMAA161', 10)->nullable();
                $table->string('IMAA162', 1)->nullable();
                $table->string('IMAA163', 10)->nullable();
                $table->string('IMAA164', 1)->nullable();
                $table->string('IMAA165', 1)->nullable();
                $table->string('IMAA166', 1)->nullable();
                $table->string('IMAA167', 10)->nullable();
                $table->string('IMAA168', 1)->nullable();
                $table->decimal('IMAA169', 20, 6)->nullable();
                $table->string('IMAA170', 1)->nullable();
                $table->string('IMAA171', 1)->nullable();
                $table->string('IMAA172', 20)->nullable();
                $table->string('IMAA173', 40)->nullable();
                $table->decimal('IMAA174', 20, 6)->nullable();
                $table->string('IMAA175', 1)->nullable();
                
                // 高级扩展字段（IMAAUA001-IMAAUA005）
                $table->string('IMAAUA001', 1)->nullable();
                $table->string('IMAAUA002', 80)->nullable();
                $table->text('IMAAUA003')->nullable(); // Oracle VARCHAR2(4000) 转换为TEXT
                $table->string('IMAAUA004', 80)->nullable();
                $table->decimal('IMAAUA005', 6, 3)->nullable();
                $table->comment('料件主档');

                // 主键约束
                $table->primary(['IMAAENT', 'IMAA001']);
                
                // 索引定义
                $table->index('IMAA001', 'IMAA_01');
                $table->index(['IMAAENT', 'IMAA001', 'IMAA108'], 'IMAA_N');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IMAA_T');
    }
};
