<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plants', function (Blueprint $table) {
            $table->id();
            $table->string('plant_code', 20)->unique()->comment('厂区代码');
            $table->string('company_code', 20)->comment('所属公司代码');
            $table->string('plant_name', 100)->comment('厂区名称');
            $table->string('plant_name_en', 100)->nullable()->comment('厂区英文名称');
            $table->text('address')->nullable()->comment('厂区地址');
            $table->string('contact_person', 50)->nullable()->comment('联系人');
            $table->string('contact_phone', 20)->nullable()->comment('联系电话');
            $table->string('contact_email', 100)->nullable()->comment('联系邮箱');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();
            $table->comment('厂区信息');

            // 外键约束
            $table->foreign('company_code')->references('company_code')->on('companies')->onDelete('cascade');
            
            // 索引
            $table->index(['company_code', 'is_active']);
            $table->index('plant_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plants');
    }
};
