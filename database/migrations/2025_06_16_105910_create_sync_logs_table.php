<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_logs', function (Blueprint $table) {
            $table->id();
            $table->string('table_name', 100)->comment('同步的表名');
            $table->enum('sync_type', ['full', 'incremental'])->default('incremental')->comment('同步类型');
            $table->enum('status', ['pending', 'running', 'success', 'failed', 'partial'])->default('pending')->comment('同步状态');
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->integer('records_processed')->default(0)->comment('处理记录数');
            $table->integer('records_inserted')->default(0)->comment('新增记录数');
            $table->integer('records_updated')->default(0)->comment('更新记录数');
            $table->integer('records_deleted')->default(0)->comment('删除记录数');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->json('sync_details')->nullable()->comment('同步详情');
            $table->timestamps();
            
            // 添加索引
            $table->index(['table_name', 'status']);
            $table->index(['table_name', 'created_at']);
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_logs');
    }
};
