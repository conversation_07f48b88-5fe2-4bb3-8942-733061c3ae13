<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('BOM')) {
            Schema::create('BOM', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('parent_material_code', 20)->comment('父料件编码');
                $table->string('child_material_code', 20)->comment('子料件编码');
                $table->decimal('base_quantity', 20, 6)->default(1)->comment('主件底数');
                $table->decimal('child_quantity', 20, 6)->default(1)->comment('组成用量');
                $table->string('unit', 10)->comment('单位');
                $table->enum('is_order_expand', ['1', '2', '3', '4'])->default('1')->comment('工单展开选项：1.不展开,2.不展开，自动开立子工单,3.展开,4.开窗询问');
                $table->enum('is_optional', ['Y', 'N'])->index()->default('N')->comment('是否可选件(Y:是, N:否)');
                $table->enum('is_customer_material', ['Y', 'N'])->default('N')->comment('是否客供料');
                $table->enum('is_agent_purchase', ['Y', 'N'])->default('N')->comment('是否代买料');
                $table->enum('status', ['Y', 'N'])->default('Y')->comment('状态(Y:有效, N:无效)');
                $table->string('customer_code', 20)->nullable()->index()->comment('客户编码，当是可选件时，关联客户表');
                $table->timestamp('effective_time')->index()->nullable()->comment('生效时间');
                $table->timestamp('failure_time')->index()->nullable()->comment('失效时间');
                $table->decimal('loss_rate', 5, 2)->default(0)->comment('损耗率(%)');
                $table->string('version', 20)->default('A')->comment('BOM版本号');
                $table->text('remark')->nullable()->comment('备注');
                $table->comment('BOM表');
                $table->timestamps();
                $table->softDeletes();
                
                // 添加复合索引 - 使用较短的索引名
                $table->index(['company_code', 'parent_material_code', 'effective_time', 'child_material_code','version', 'status'], 'bom_cc_pmc_et_cmc_ver_stat_idx');
                
                // 业务唯一索引，后续通过模型验证确保活动状态的唯一性
                $table->unique(['company_code', 'parent_material_code', 'effective_time', 'child_material_code', 'customer_code','version'], 'bom_unique_idx');
            });
            
            // 添加模型验证逻辑，确保status=Y时唯一
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('BOM');
    }
};
