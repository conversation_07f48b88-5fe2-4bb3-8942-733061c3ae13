<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('BMAA_T')) {
            Schema::create('BMAA_T', function (Blueprint $table) {
                $table->integer('BMAAENT', false, true)->length(5);
                $table->string('BMAASITE', 10);
                $table->string('BMAASTUS', 10)->nullable();
                $table->string('BMAA001', 40);
                $table->string('BMAA002', 30);
                $table->decimal('BMAA003', 20, 6)->nullable();
                $table->string('BMAA004', 10)->nullable();
                $table->string('BMAAOWNID', 20)->nullable();
                $table->string('BMAAOWNDP', 10)->nullable();
                $table->string('BMAACRTID', 20)->nullable();
                $table->string('BMAACRTDP', 10)->nullable();
                $table->timestamp('BMAACRTDT')->nullable();
                $table->string('BMAAMODID', 20)->nullable();
                $table->timestamp('BMAAMODDT')->nullable();
                $table->string('BMAACNFID', 20)->nullable();
                $table->timestamp('BMAACNFDT')->nullable();
                
                // User defined fields
                for ($i = 1; $i <= 10; $i++) {
                    $table->string("BMAAUD0" . sprintf("%02d", $i), 40)->nullable();
                }
                
                for ($i = 11; $i <= 20; $i++) {
                    $table->decimal("BMAAUD0$i", 20, 6)->nullable();
                }
                
                for ($i = 21; $i <= 30; $i++) {
                    $table->timestamp("BMAAUD0$i")->nullable();
                }
                
                $table->string('BMAA005', 20)->nullable();
                
                $table->primary(['BMAAENT', 'BMAASITE', 'BMAA001', 'BMAA002']);
                $table->index(['BMAA001', 'BMAA002'], 'BMAA_01');
                $table->index(['BMAA002'], 'BMAA_02');
                $table->index(['BMAASITE', 'BMAACRTDT'], 'BMAA_N02');

                $table->comment('产品结构单头档');
                
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        };
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('BMAA_T');
    }
};
