<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sync_logs', function (Blueprint $table) {
            // 🚀 添加针对Dashboard查询的复合索引
            
            // 1. 优化最近24小时查询 (getSyncRecords24h)
            $table->index(['start_time', 'id'], 'sync_logs_start_time_id_index');
            
            // 2. 优化表状态查询 (getTableStatus)
            $table->index(['table_name', 'start_time'], 'sync_logs_table_start_time_index');
            
            // 3. 优化成功率计算查询
            $table->index(['table_name', 'start_time', 'status'], 'sync_logs_table_time_status_index');
            
            // 4. 优化趋势图查询 (getSyncTrends)
            $table->index(['start_time', 'table_name', 'status'], 'sync_logs_time_table_status_index');
            
            // 5. 优化正在运行的同步检查
            $table->index(['status', 'created_at'], 'sync_logs_status_created_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sync_logs', function (Blueprint $table) {
            $table->dropIndex('sync_logs_start_time_id_index');
            $table->dropIndex('sync_logs_table_start_time_index');
            $table->dropIndex('sync_logs_table_time_status_index');
            $table->dropIndex('sync_logs_time_table_status_index');
            $table->dropIndex('sync_logs_status_created_index');
        });
    }
}; 