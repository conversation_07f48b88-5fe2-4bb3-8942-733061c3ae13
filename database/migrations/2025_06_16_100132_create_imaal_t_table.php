<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('IMAAL_T')) {
            Schema::create('IMAAL_T', function (Blueprint $table) {
                // 核心字段定义
                $table->decimal('IMAALENT', 5, 0); // 无小数位的5位十进制数
                $table->string('IMAAL001', 40);    // 40字符字符串，非空
                $table->string('IMAAL002', 6);     // 6字符字符串，非空
                
                // 基础属性字段
                $table->string('IMAAL003', 255)->nullable();
                $table->string('IMAAL004', 255)->nullable();
                $table->string('IMAAL005', 10)->nullable();
                $table->comment('料件多语言档');
                
                // 复合主键
                $table->primary(['IMAALENT', 'IMAAL001', 'IMAAL002']);
                
                // 索引定义
                $table->index('IMAAL005', 'IMAAL_01');
                $table->index('IMAAL001', 'IMAAL_02');
                $table->index('IMAALENT', 'IMAAL_03');
                $table->index('IMAAL002', 'IMAAL_04');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IMAAL_T');
    }
};
