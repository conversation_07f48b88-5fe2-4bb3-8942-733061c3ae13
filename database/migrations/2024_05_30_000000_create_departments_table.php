<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('departments')) {
            Schema::create('departments', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->index()->default('TB')->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('name')->comment('部门名称');
                $table->unsignedBigInteger('parent_id')->nullable()->comment('父部门ID');
                $table->unsignedBigInteger('manager_id')->nullable()->comment('部门主管ID');
                $table->text('description')->nullable()->comment('部门描述');
                $table->string('status')->default('正常')->comment('部门状态');
                $table->integer('order')->default(0)->comment('排序顺序');
                $table->timestamps();
                $table->softDeletes();
                $table->comment('部门信息');
                // 外键约束
                $table->foreign('parent_id')->references('id')->on('departments')->onDelete('set null');
                $table->foreign('manager_id')->references('id')->on('users')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
