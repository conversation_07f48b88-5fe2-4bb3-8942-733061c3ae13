<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('BMBA_T')) {
            Schema::create('BMBA_T', function (Blueprint $table) {
                $table->decimal('BMBAENT', 5, 0);
                $table->string('BMBASITE', 10);
                $table->string('BMBA001', 40);
                $table->string('BMBA002', 30);
                $table->string('BMBA003', 40);
                $table->string('BMBA004', 10);
                $table->timestamp('BMBA005');
                $table->timestamp('BMBA006')->nullable();
                $table->string('BMBA007', 10);
                $table->string('BMBA008', 10);
                $table->decimal('BMBA009', 10, 0)->nullable();
                $table->string('BMBA010', 10)->nullable();
                $table->decimal('BMBA011', 20, 6)->nullable();
                $table->decimal('BMBA012', 20, 6)->nullable();
                $table->string('BMBA013', 10)->nullable();
                $table->string('BMBA014', 1)->nullable();
                $table->string('BMBA015', 10)->nullable();
                $table->string('BMBA016', 10)->nullable();
                $table->string('BMBA017', 10)->nullable();
                $table->string('BMBA018', 1)->nullable();
                $table->string('BMBA019', 10)->nullable();
                $table->string('BMBA020', 1)->nullable();
                $table->string('BMBA021', 10)->nullable();
                $table->string('BMBA022', 1)->nullable();
                $table->decimal('BMBA023', 15, 3)->nullable();
                $table->string('BMBA024', 40)->nullable();
                $table->string('BMBA025', 1)->nullable();
                $table->string('BMBA026', 20)->nullable();
                $table->string('BMBA027', 1)->nullable();
                $table->string('BMBA028', 10)->nullable();
                $table->string('BMBA029', 10)->nullable();
                $table->string('BMBA030', 1)->nullable();
                $table->string('BMBA031', 1)->nullable();
                $table->string('BMBA032', 30)->nullable();
                
                // User defined varchar fields
                for ($i = 1; $i <= 10; $i++) {
                    $table->string("BMBAUD0" . sprintf("%02d", $i), 40)->nullable();
                }
                
                // User defined decimal fields
                for ($i = 11; $i <= 20; $i++) {
                    $table->decimal("BMBAUD0$i", 20, 6)->nullable();
                }
                
                // User defined timestamp fields
                for ($i = 21; $i <= 30; $i++) {
                    $table->timestamp("BMBAUD0$i")->nullable();
                }
                
                $table->string('BMBA033', 1)->nullable();
                $table->string('BMBA034', 10)->nullable();
                $table->string('BMBA035', 1)->nullable();
                
                $table->primary(['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA002', 'BMBA003', 'BMBA004', 'BMBA005', 'BMBA007', 'BMBA008']);
                $table->index(['BMBA001', 'BMBA002', 'BMBA003', 'BMBA004', 'BMBA005', 'BMBA007', 'BMBA008'], 'BMBA_01');
                $table->index(['BMBA003', 'BMBAENT', 'BMBA005'], 'BMBA_N02');
                $table->index(['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA002', 'BMBA005', 'BMBA006'], 'BMBA_N3');
                $table->index(['BMBA002', 'BMBA003'], 'BMBA_N4');
                $table->index(['BMBA001'], 'BMBA_N5');
                $table->index(['BMBA001', 'BMBA002', 'BMBAENT'], 'BMBA_N6');
                $table->index(['BMBA020'], 'BMBA_N7');
                $table->index(['BMBASITE'], 'BMBA_N8');

                $table->comment('产品结构单体档');

                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('BMBA_T');
    }
};
