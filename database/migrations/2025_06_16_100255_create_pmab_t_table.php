<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('PMAB_T')) {
            Schema::create('PMAB_T', function (Blueprint $table) {
                // 核心字段定义
                $table->decimal('PMABENT', 5, 0);    // 无小数位的5位十进制数
                $table->string('PMABSITE', 10);      // 10字符字符串，非空
                $table->string('PMAB001', 10);       // 10字符字符串，非空
                
                // 基础属性字段（PMAB002-019）
                $table->string('PMAB002', 1)->nullable();
                $table->string('PMAB003', 10)->nullable();  // 索引字段
                $table->string('PMAB004', 10)->nullable();
                $table->string('PMAB005', 10)->nullable();
                $table->decimal('PMAB006', 25, 6)->nullable();
                $table->decimal('PMAB007', 20, 6)->nullable();
                $table->date('PMAB008')->nullable();
                $table->integer('PMAB009', false, true)->nullable();
                $table->decimal('PMAB010', 25, 6)->nullable();
                $table->string('PMAB011', 10)->nullable();
                $table->string('PMAB012', 10)->nullable();
                $table->string('PMAB013', 10)->nullable();
                $table->string('PMAB014', 10)->nullable();
                $table->string('PMAB015', 10)->nullable();
                $table->string('PMAB016', 10)->nullable();
                $table->string('PMAB017', 1)->nullable();
                $table->string('PMAB018', 10)->nullable();
                $table->decimal('PMAB019', 25, 6)->nullable();
                $table->date('PMAB020')->nullable();
                $table->date('PMAB021')->nullable();
                
                // 扩展属性字段（PMAB030-059）
                $table->string('PMAB030', 10)->nullable();
                $table->string('PMAB031', 20)->nullable();
                $table->string('PMAB032', 6)->nullable();
                $table->string('PMAB033', 10)->nullable();
                $table->string('PMAB034', 10)->nullable();
                $table->string('PMAB035', 10)->nullable();
                $table->string('PMAB036', 10)->nullable();
                $table->string('PMAB037', 10)->nullable();
                $table->string('PMAB038', 10)->nullable();
                $table->string('PMAB039', 10)->nullable();
                $table->string('PMAB040', 10)->nullable();
                $table->string('PMAB041', 10)->nullable();
                $table->string('PMAB042', 10)->nullable();
                $table->string('PMAB043', 10)->nullable();
                $table->string('PMAB044', 10)->nullable();
                $table->decimal('PMAB045', 20, 6)->nullable();
                $table->decimal('PMAB046', 20, 6)->nullable();
                $table->string('PMAB047', 10)->nullable();
                $table->string('PMAB048', 80)->nullable();
                $table->string('PMAB049', 1)->nullable();
                $table->integer('PMAB050')->nullable();
                $table->string('PMAB051', 1)->nullable();
                $table->integer('PMAB052')->nullable();
                $table->string('PMAB053', 10)->nullable();
                $table->string('PMAB054', 10)->nullable();
                $table->string('PMAB055', 10)->nullable();
                $table->string('PMAB056', 2)->nullable();
                $table->string('PMAB057', 10)->nullable();
                $table->string('PMAB058', 10)->nullable();
                $table->string('PMAB059', 10)->nullable();
                
                // 业务字段组（PMAB060-073）
                $table->string('PMAB060', 10)->nullable();
                $table->decimal('PMAB061', 15, 3)->nullable();
                $table->decimal('PMAB062', 15, 3)->nullable();
                $table->decimal('PMAB063', 15, 3)->nullable();
                $table->decimal('PMAB064', 15, 3)->nullable();
                $table->decimal('PMAB065', 15, 3)->nullable();
                $table->decimal('PMAB066', 15, 3)->nullable();
                $table->decimal('PMAB067', 15, 3)->nullable();
                $table->decimal('PMAB068', 15, 3)->nullable();
                $table->decimal('PMAB069', 15, 3)->nullable();
                $table->decimal('PMAB070', 15, 3)->nullable();
                $table->string('PMAB071', 10)->nullable();
                $table->string('PMAB072', 10)->nullable();
                $table->string('PMAB073', 10)->nullable();
                $table->string('PMAB074', 10)->nullable();
                $table->string('PMAB075', 20)->nullable();
                
                // 业务字段组（PMAB080-108）
                $table->string('PMAB080', 10)->nullable();
                $table->string('PMAB081', 20)->nullable();
                $table->string('PMAB082', 6)->nullable();
                $table->string('PMAB083', 10)->nullable();
                $table->string('PMAB084', 10)->nullable();
                $table->string('PMAB085', 10)->nullable();
                $table->string('PMAB086', 10)->nullable();
                $table->string('PMAB087', 10)->nullable();
                $table->string('PMAB088', 10)->nullable();
                $table->string('PMAB089', 10)->nullable();
                $table->string('PMAB090', 10)->nullable();
                $table->string('PMAB091', 10)->nullable();
                $table->string('PMAB092', 10)->nullable();
                $table->string('PMAB093', 10)->nullable();
                $table->string('PMAB094', 10)->nullable();
                $table->decimal('PMAB095', 20, 6)->nullable();
                $table->decimal('PMAB096', 20, 6)->nullable();
                $table->string('PMAB097', 10)->nullable();
                $table->string('PMAB098', 80)->nullable();
                $table->string('PMAB099', 1)->nullable();
                $table->integer('PMAB100')->nullable();
                $table->string('PMAB101', 1)->nullable();
                $table->integer('PMAB102')->nullable();
                $table->string('PMAB103', 10)->nullable();
                $table->string('PMAB104', 10)->nullable();
                $table->string('PMAB105', 10)->nullable();
                $table->string('PMAB106', 2)->nullable();
                $table->string('PMAB107', 10)->nullable();
                $table->string('PMAB108', 10)->nullable();
                $table->string('PMAB109', 10)->nullable();
                $table->decimal('PMAB110', 20, 6)->nullable();
                $table->decimal('PMAB111', 20, 6)->nullable();
                $table->string('PMAB112', 1)->nullable();
                $table->string('PMAB114', 10)->nullable();
                $table->string('PMAB115', 20)->nullable();
                $table->decimal('PMAB116', 20, 6)->nullable();
                $table->string('PMAB120', 15)->nullable();
                $table->string('PMAB121', 30)->nullable();
                
                // 用户与时间戳字段
                $table->string('PMABOWNID', 20)->nullable();
                $table->string('PMABOWNDP', 10)->nullable();
                $table->string('PMABCRTID', 20)->nullable();
                $table->string('PMABCRTDP', 10)->nullable();
                $table->timestamp('PMABCRTDT')->nullable();
                $table->string('PMABMODID', 20)->nullable();
                $table->timestamp('PMABMODDT')->nullable();
                $table->string('PMABCNFID', 20)->nullable();
                $table->timestamp('PMABCNFDT')->nullable();
                
                // 状态与用户自定义字段
                $table->string('PMABSTUS', 10)->nullable();
                
                // 用户自定义字段（PMABUD001-030）
                for ($i = 1; $i <= 10; $i++) {
                    $table->string("PMABUD0" . sprintf("%02d", $i), 40)->nullable();
                }
                for ($i = 11; $i <= 20; $i++) {
                    $table->decimal("PMABUD0$i", 20, 6)->nullable();
                }
                for ($i = 21; $i <= 30; $i++) {
                    $table->timestamp("PMABUD0$i")->nullable();
                }
                
                $table->comment('交易对象据点档');
                
                // 复合主键
                $table->primary(['PMABENT', 'PMABSITE', 'PMAB001']);
                
                // 索引定义
                $table->index(['PMABENT', 'PMAB001'], 'PMAB_N02');
                $table->index(['PMABENT', 'PMAB003'], 'PMAB_N1');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PMAB_T');
    }
};
