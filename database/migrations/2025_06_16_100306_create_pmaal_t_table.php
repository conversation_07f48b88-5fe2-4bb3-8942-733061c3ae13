<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('PMAAL_T')) {
            Schema::create('PMAAL_T', function (Blueprint $table) {
                // 核心字段定义
                $table->decimal('PMAALENT', 5, 0); // 无小数位的5位十进制数
                $table->string('PMAAL001', 10);    // 10字符字符串，非空
                $table->string('PMAAL002', 6);     // 6字符字符串，非空
                
                // 基础属性字段
                $table->string('PMAAL003', 255)->nullable();
                $table->string('PMAAL004', 80)->nullable();
                $table->string('PMAAL005', 10)->nullable();  // 索引字段
                $table->string('PMAAL006', 255)->nullable();
                $table->comment('交易对象多语言档');
                // 复合主键
                $table->primary(['PMAALENT', 'PMAAL001', 'PMAAL002']);
                
                // 索引定义
                $table->index('PMAAL005', 'PMAAL_01');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PMAAL_T');
    }
};
