<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('companies')) {
            Schema::create('companies', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 20)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->enum('is_active', ['Y', 'N'])->default('Y')->comment('状态(Y:有效, N:无效)');
                $table->comment('公司信息表');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['company_code']);
                $table->unique(['company_code']);
            });

            DB::table('companies')->insert([
                ['company_code' => 'SG', 'is_active' => 'Y'],
                ['company_code' => 'TB', 'is_active' => 'Y'],
                ['company_code' => 'TH', 'is_active' => 'Y'],
                ['company_code' => 'ED', 'is_active' => 'Y'],
                ['company_code' => 'EM', 'is_active' => 'Y'],
                ['company_code' => 'TL', 'is_active' => 'Y'],
            ]);
        }

        if (!Schema::hasTable('company_translations')) {
            Schema::create('company_translations', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 20)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('locale', 10)->comment('语言:zh_CN, en_US, th_TH');
                $table->string('company_name')->comment('公司名称');
                $table->comment('公司名称表');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['company_code', 'locale']);
                $table->unique(['company_code', 'locale']);
            });

            DB::table('company_translations')->insert([
                ['company_code' => 'SG', 'locale' => 'zh_CN', 'company_name' => '新加坡贸易公司'],
                ['company_code' => 'SG', 'locale' => 'en_US', 'company_name' => 'Singapore Trade Company'],
                ['company_code' => 'SG', 'locale' => 'th_TH', 'company_name' => 'บริษัทส่งออกสินค้าเกาหลี'],
                ['company_code' => 'TB', 'locale' => 'zh_CN', 'company_name' => '工具箱柜'],
                ['company_code' => 'TB', 'locale' => 'en_US', 'company_name' => 'Tool Box Cabinet'],
                ['company_code' => 'TB', 'locale' => 'th_TH', 'company_name' => 'ตู้กล่องเครื่องมือ'],
                ['company_code' => 'TH', 'locale' => 'zh_CN', 'company_name' => '泰国工厂'],
                ['company_code' => 'TH', 'locale' => 'en_US', 'company_name' => 'Thailand Factory'],
                ['company_code' => 'TH', 'locale' => 'th_TH', 'company_name' => 'รับผลิตภัณฑ์ในประเทศ'],
                ['company_code' => 'ED', 'locale' => 'zh_CN', 'company_name' => '装备发展'],
                ['company_code' => 'ED', 'locale' => 'en_US', 'company_name' => 'Equipment Development'],
                ['company_code' => 'ED', 'locale' => 'th_TH', 'company_name' => 'การพัฒนาอุปกรณ์'],
                ['company_code' => 'EM', 'locale' => 'zh_CN', 'company_name' => '机电设备'],
                ['company_code' => 'EM', 'locale' => 'en_US', 'company_name' => 'Electrical and Mechanical Equipment'],
                ['company_code' => 'EM', 'locale' => 'th_TH', 'company_name' => 'อุปกรณ์ไฟฟ้าและกลไก'],
                ['company_code' => 'TL', 'locale' => 'zh_CN', 'company_name' => '天狼机械'],
                ['company_code' => 'TL', 'locale' => 'en_US', 'company_name' => 'Tianlang Machinery'],
                ['company_code' => 'TL', 'locale' => 'th_TH', 'company_name' => 'อุปกรณ์กลไกสำหรับรถยนต์'],
            ]);
        }

        if (!Schema::hasTable('user_company')) {
            Schema::create('user_company', function (Blueprint $table) {
                $table->id();
                $table->string('user_id', 20)->comment('用户ID');
                $table->string('company_code', 20)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->foreign('company_code')->references('company_code')->on('companies')->onDelete('cascade');
                $table->comment('用户关联公司表');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['user_id', 'company_code']);
                $table->unique(['user_id', 'company_code']);
            });

            DB::table('user_company')->insert([
                ['user_id' => '1', 'company_code' => 'SG'],
                ['user_id' => '1', 'company_code' => 'TB'],
                ['user_id' => '1', 'company_code' => 'TH'],
                ['user_id' => '1', 'company_code' => 'ED'],
                ['user_id' => '1', 'company_code' => 'EM'],
                ['user_id' => '1', 'company_code' => 'TL'],
            ]);
        }

        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_company');
        Schema::dropIfExists('company_translations');
        Schema::dropIfExists('companies');
    }
};
