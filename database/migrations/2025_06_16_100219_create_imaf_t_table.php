<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('IMAF_T')) {
            Schema::create('IMAF_T', function (Blueprint $table) {
                // 核心字段定义
                $table->decimal('IMAFENT', 5, 0);    // 无小数位的5位十进制数
                $table->string('IMAFSITE', 10);      // 10字符字符串，非空
                $table->string('IMAF001', 40);       // 40字符字符串，非空
                
                // 基础属性字段（IMAF011-IMAF018）
                $table->string('IMAF011', 10)->nullable();
                $table->string('IMAF012', 10)->nullable();
                $table->string('IMAF013', 10)->nullable();  // 索引字段
                $table->string('IMAF014', 10)->nullable();
                $table->string('IMAF015', 10)->nullable();
                $table->string('IMAF016', 10)->nullable();
                $table->string('IMAF017', 10)->nullable();
                $table->string('IMAF018', 1)->nullable();
                
                // 数值类型字段（IMAF021-IMAF033）
                $table->decimal('IMAF021', 15, 3)->nullable();
                $table->decimal('IMAF022', 15, 3)->nullable();
                $table->decimal('IMAF023', 20, 6)->nullable();
                $table->decimal('IMAF024', 20, 6)->nullable();
                $table->decimal('IMAF025', 20, 6)->nullable();
                $table->decimal('IMAF026', 20, 6)->nullable();
                $table->decimal('IMAF027', 20, 6)->nullable();
                $table->decimal('IMAF031', 15, 3)->nullable();
                $table->decimal('IMAF032', 15, 3)->nullable();
                $table->decimal('IMAF033', 15, 3)->nullable();
                $table->string('IMAF034', 1)->nullable();
                $table->string('IMAF035', 40)->nullable();
                
                // 分组属性字段（IMAF051-IMAF064）
                $table->string('IMAF051', 10)->nullable();  // 索引字段
                $table->string('IMAF052', 20)->nullable();
                $table->string('IMAF053', 10)->nullable();
                $table->string('IMAF054', 1)->nullable();
                $table->string('IMAF055', 10)->nullable();
                $table->string('IMAF056', 1)->nullable();
                $table->string('IMAF057', 10)->nullable();
                $table->string('IMAF058', 10)->nullable();
                $table->string('IMAF059', 10)->nullable();
                $table->string('IMAF061', 10)->nullable();
                $table->string('IMAF062', 1)->nullable();
                $table->string('IMAF063', 10)->nullable();
                $table->string('IMAF064', 10)->nullable();
                
                // 分类属性字段（IMAF071-IMAF097）
                $table->string('IMAF071', 10)->nullable();
                $table->string('IMAF072', 1)->nullable();
                $table->string('IMAF073', 10)->nullable();
                $table->string('IMAF074', 10)->nullable();
                $table->string('IMAF081', 10)->nullable();
                $table->string('IMAF082', 1)->nullable();
                $table->string('IMAF083', 10)->nullable();
                $table->string('IMAF084', 10)->nullable();
                $table->string('IMAF091', 10)->nullable();
                $table->string('IMAF092', 10)->nullable();
                $table->string('IMAF093', 1)->nullable();
                $table->decimal('IMAF094', 20, 6)->nullable();
                $table->decimal('IMAF095', 20, 6)->nullable();
                $table->date('IMAF096')->nullable();
                $table->string('IMAF097', 10)->nullable();
                
                // 计算相关字段（IMAF101-IMAF118）
                $table->decimal('IMAF101', 20, 6)->nullable();
                $table->decimal('IMAF102', 20, 6)->nullable();
                $table->string('IMAF111', 10)->nullable();  // 索引字段
                $table->string('IMAF112', 10)->nullable();
                $table->string('IMAF113', 10)->nullable();
                $table->decimal('IMAF114', 20, 6)->nullable();
                $table->decimal('IMAF115', 20, 6)->nullable();
                $table->string('IMAF116', 10)->nullable();
                $table->decimal('IMAF117', 15, 3)->nullable();
                $table->decimal('IMAF118', 15, 3)->nullable();
                
                // 业务相关字段（IMAF121-IMAF130）
                $table->string('IMAF121', 10)->nullable();
                $table->string('IMAF122', 10)->nullable();
                $table->string('IMAF123', 40)->nullable();
                $table->decimal('IMAF124', 15, 3)->nullable();
                $table->string('IMAF125', 40)->nullable();
                $table->string('IMAF126', 1)->nullable();
                $table->string('IMAF127', 1)->nullable();
                $table->decimal('IMAF128', 20, 6)->nullable();
                $table->string('IMAF129', 1)->nullable();
                $table->decimal('IMAF130', 20, 6)->nullable();
                
                // 资源相关字段（IMAF141-IMAF176）
                $table->string('IMAF141', 10)->nullable();  // 索引字段
                $table->string('IMAF142', 20)->nullable();  // 索引字段
                $table->string('IMAF143', 10)->nullable();
                $table->string('IMAF144', 10)->nullable();
                $table->decimal('IMAF145', 20, 6)->nullable();
                $table->decimal('IMAF146', 20, 6)->nullable();
                $table->string('IMAF147', 10)->nullable();
                $table->decimal('IMAF148', 20, 6)->nullable();
                $table->decimal('IMAF149', 20, 6)->nullable();
                $table->string('IMAF151', 10)->nullable();
                $table->string('IMAF152', 10)->nullable();
                $table->string('IMAF153', 10)->nullable();
                $table->decimal('IMAF154', 20, 6)->nullable();
                $table->decimal('IMAF155', 15, 3)->nullable();
                $table->string('IMAF156', 10)->nullable();
                $table->string('IMAF157', 40)->nullable();
                $table->string('IMAF158', 10)->nullable();
                $table->string('IMAF161', 1)->nullable();
                $table->string('IMAF162', 1)->nullable();
                $table->string('IMAF163', 1)->nullable();
                $table->decimal('IMAF164', 20, 6)->nullable();
                $table->decimal('IMAF165', 20, 6)->nullable();
                $table->decimal('IMAF166', 20, 6)->nullable();
                $table->decimal('IMAF171', 15, 3)->nullable();
                $table->decimal('IMAF172', 15, 3)->nullable();
                $table->decimal('IMAF173', 15, 3)->nullable();
                $table->decimal('IMAF174', 15, 3)->nullable();
                $table->decimal('IMAF175', 15, 3)->nullable();
                $table->string('IMAF176', 10)->nullable();
                
                // 用户与时间戳字段
                $table->string('IMAFOWNID', 20)->nullable();
                $table->string('IMAFOWNDP', 10)->nullable();
                $table->string('IMAFCRTID', 20)->nullable();
                $table->string('IMAFCRTDP', 10)->nullable();
                $table->timestamp('IMAFCRTDT')->nullable();
                $table->string('IMAFMODID', 20)->nullable();
                $table->timestamp('IMAFMODDT')->nullable();
                $table->string('IMAFCNFID', 20)->nullable();
                $table->timestamp('IMAFCNFDT')->nullable();
                
                // 状态与用户自定义字段
                $table->string('IMAFSTUS', 10)->nullable();
                
                // 用户自定义字段（IMAFUD001-IMAFUD030）
                for ($i = 1; $i <= 10; $i++) {
                    $table->string("IMAFUD0" . sprintf("%02d", $i), 40)->nullable();
                }
                for ($i = 11; $i <= 20; $i++) {
                    $table->decimal("IMAFUD0$i", 20, 6)->nullable();
                }
                for ($i = 21; $i <= 30; $i++) {
                    $table->timestamp("IMAFUD0$i")->nullable();
                }
                
                // 其他业务字段
                $table->string('IMAF177', 1)->nullable();
                $table->string('IMAF178', 10)->nullable();
                $table->decimal('IMAF179', 20, 6)->nullable();
                $table->string('IMAF180', 1)->nullable();
                $table->string('IMAF181', 1)->nullable();
                $table->comment('料件据点进销存盘');
                
                // 复合主键
                $table->primary(['IMAFENT', 'IMAFSITE', 'IMAF001']);
                
                // 索引定义
                $table->index('IMAF011', 'IMAF_01');
                $table->index('IMAF051', 'IMAF_02');
                $table->index('IMAF111', 'IMAF_03');
                $table->index('IMAF141', 'IMAF_04');
                $table->index('IMAF013', 'IMAF_05');
                $table->index('IMAF001', 'IMAF_06');
                $table->index('IMAFSITE', 'IMAF_07');
                $table->index('IMAF142', 'IMAF_08');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IMAF_T');
    }
};
