<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('sync_change_log')) {
            Schema::create('sync_change_log', function (Blueprint $table) {
                $table->id();
                $table->string('table_name', 100)->comment('同步的表名');
                $table->string('change_type', 100)->comment('变更类型:insert,update,delete');
                $table->json('pk_json')->comment('主键json');
                $table->json('pk_old_json')->comment('旧主键json');
                $table->timestamps();
                $table->comment('同步变更日志表');
                $table->index('created_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_change_log');
    }
};
