<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('RTAXL_T')) {
            Schema::create('RTAXL_T', function (Blueprint $table) {
                 // 核心字段定义
                $table->decimal('RTAXLENT', 5, 0); // 无小数位的5位十进制数
                $table->string('RTAXL001', 10);    // 10字符字符串，非空
                $table->string('RTAXL002', 6);     // 6字符字符串，非空
                
                // 基础属性字段
                $table->string('RTAXL003', 500)->nullable();
                $table->string('RTAXL004', 10)->nullable();
                $table->comment('品类基本数据多语言档');
                // 复合主键
                $table->primary(['RTAXLENT', 'RTAXL001', 'RTAXL002']);
                
                // 索引定义
                $table->index('RTAXL001', 'RTAXL_01');
                
                // 字符集与排序规则
                $table->charset = 'utf8mb4';
                $table->collation = 'utf8mb4_unicode_ci';
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('RTAXL_T');
    }
};
