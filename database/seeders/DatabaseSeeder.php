<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        if (!User::where('username', 'admin')->exists()) {
            User::create([
                'name' => 'admin',
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456'),
            ]);
        }

        $this->call([
            CustomerDetailSeeder::class
        ]);
    }
}
