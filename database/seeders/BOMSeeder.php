<?php

namespace Database\Seeders;

use App\Models\BOM;
use Carbon\Carbon;
use DB;
use Illuminate\Container\Attributes\DB as AttributesDB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BOMSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $version = Carbon::now()->format('Ymd');
        $processed = 0;
        $created = 0;
        $updated = 0;
        $skipped = 0;
        
        echo "开始BOM数据填充，版本：{$version}\n";
        
        DB::table('BMBA_T')->where('BMBAENT', '=', '40')->where('BMBASITE', '=', 'ALL')
            ->orderBy('BMBA001')
            ->orderBy('BMBA003')
            ->chunk(1000, function ($boms) use ($version, &$processed, &$created, &$updated, &$skipped) {
                foreach ($boms as $bom) {
                    // 🔧 主键配置
                    $primaryKey = [
                        'company_code' => 'TB',
                        'parent_material_code' => $bom->BMBA001,
                        'child_material_code' => $bom->BMBA003,
                    ];
                    
                    // 🔧 业务数据部分（不包含version）
                    $businessData = [
                        'base_quantity' => $bom->BMBA012 ? $bom->BMBA012 : 1,
                        'child_quantity' => $bom->BMBA011 ? $bom->BMBA011 : 1,
                        'unit' => $bom->BMBA010,
                        'is_order_expand' => $bom->BMBA021,
                        'is_optional' => $bom->BMBA020,
                        'is_customer_material' => $bom->BMBA031,
                        'is_agent_purchase' => $bom->BMBA022,
                        'status' => 'Y',
                        'customer_code' => $bom->BMBA004,
                        'effective_time' => $bom->BMBA005,
                        'failure_time' => $bom->BMBA006,
                    ];
                    
                    // 🔧 检查记录是否存在
                    $existingBom = BOM::where($primaryKey)->first();
                    
                    if (!$existingBom) {
                        // 🔧 新建记录时包含version
                        BOM::create(array_merge($primaryKey, $businessData, ['version' => $version]));
                        $created++;
                    } else {
                        // 🔧 检查业务数据是否有实际变更
                        $hasChanges = false;
                        
                        foreach ($businessData as $key => $newValue) {
                            $oldValue = $existingBom->$key;
                            
                            // 使用严格比较，正确处理数值类型
                            if ($oldValue !== $newValue) {
                                // 特殊处理数值字段的类型转换
                                if (in_array($key, ['base_quantity', 'child_quantity']) && 
                                    is_numeric($oldValue) && is_numeric($newValue) && 
                                    (float)$oldValue === (float)$newValue) {
                                    continue; // 数值相等，跳过
                                }
                                
                                $hasChanges = true;
                                break;
                            }
                        }
                        
                        if ($hasChanges) {
                            // 🔧 只有在有业务数据变更时才更新version和updated_at
                            $existingBom->update(array_merge($businessData, ['version' => $version]));
                            $updated++;
                        } else {
                            // 🔧 无业务数据变更，完全跳过更新（不更新version、updated_at）
                            $skipped++;
                        }
                    }
                    
                    $processed++;
                }
                
                // 每1000条记录输出一次进度
                echo "已处理：{$processed} 条记录\n";
            });
            
        echo "BOM数据填充完成！\n";
        echo "总处理：{$processed} 条\n";
        echo "新建：{$created} 条\n";
        echo "更新：{$updated} 条\n";
        echo "跳过：{$skipped} 条\n";
    }
}
