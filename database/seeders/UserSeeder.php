<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 工具箱柜
        if (!User::where('username', 'TB')->exists()) {
            User::create([
                'name' => 'TB',
                'username' => 'TB',
                'email' => '<EMAIL>',
                'password' => Hash::make('1qazXSW@'),
            ]);
        }

        // 新加坡
        if (!User::where('username', 'SG')->exists()) {
            User::create([
                'name' => 'SG',
                'username' => 'SG',
                'email' => '<EMAIL>',
                'password' => Hash::make('1qazXSW@'),
            ]);
        }

        // 泰国
        if (!User::where('username', 'TH')->exists()) {
            User::create([
                'name' => 'TH',
                'username' => 'TH',
                'email' => '<EMAIL>',
                'password' => Hash::make('1qazXSW@'),
            ]);
        }
    }
}
