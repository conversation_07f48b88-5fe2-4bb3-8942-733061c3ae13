{"private": true, "type": "module", "scripts": {"build": "vue-tsc && vite build && vite build --ssr && vite build && vite build --ssr --ssr", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.4.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.2.1", "typescript": "^5.6.3", "vite": "^6.0.11", "vue": "^3.4.0", "vue-tsc": "^2.0.24"}, "dependencies": {"chart.js": "^4.5.0", "vue-chartjs": "^5.3.2"}}