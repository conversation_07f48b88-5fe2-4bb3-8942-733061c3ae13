<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\SyncLog;
use App\Services\DataSyncService;
use App\Services\QueueManagementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    private DataSyncService $syncService;
    private QueueManagementService $queueService;

    public function __construct(DataSyncService $syncService, QueueManagementService $queueService)
    {
        $this->syncService = $syncService;
        $this->queueService = $queueService;
    }

    /**
     * 显示Dashboard页面
     */
    public function index(): Response
    {
        return Inertia::render('Dashboard', [
            'user' => Auth::user()
        ]);
    }

    /**
     * 获取同步统计数据 - 带缓存优化
     */
    public function getSyncStats(): JsonResponse
    {
        try {
            // 🚀 使用缓存减少数据库查询压力
            $cacheKey = 'dashboard_stats_' . date('Y-m-d-H-i');
            
            $data = Cache::remember($cacheKey, 60, function () {
                $stats = $this->syncService->getSyncStats();
                
                // 获取最近7天的同步趋势
                $trends = $this->getSyncTrends();
                
                // 获取表状态
                $tableStatus = $this->getTableStatus();
                
                return [
                    'stats' => $stats,
                    'trends' => $trends,
                    'table_status' => $tableStatus,
                ];
            });
            
            return response()->json([
                'success' => true,
                'data' => [
                    'stats' => $data['stats'],
                    'trends' => $data['trends'],
                    'table_status' => $data['table_status'],
                    'last_updated' => now()->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('Dashboard统计数据获取失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => '服务器内部错误',
                'message' => '获取统计数据失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 获取同步趋势数据（最近7天） - 优化版本
     */
    private function getSyncTrends(): array
    {
        $trends = [];
        $tables = array_keys(config('sync.tables', []));
        
        // 🚀 优化：使用单个查询获取最近7天的所有数据
        $startDate = Carbon::now()->subDays(6)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        $allLogs = SyncLog::whereBetween('start_time', [$startDate, $endDate])
            ->selectRaw('
                DATE(start_time) as sync_date,
                table_name,
                status,
                SUM(records_processed) as records_processed,
                COUNT(*) as sync_count
            ')
            ->groupBy('sync_date', 'table_name', 'status')
            ->get()
            ->groupBy('sync_date');
        
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateStr = $date->format('Y-m-d');
            
            $dayStats = [
                'date' => $dateStr,
                'date_label' => $date->format('m-d'),
                'total_syncs' => 0,
                'successful_syncs' => 0,
                'failed_syncs' => 0,
                'records_processed' => 0,
                'tables' => []
            ];
            
            $dayLogs = $allLogs->get($dateStr, collect());
            
            foreach ($tables as $tableName) {
                $tableLogs = $dayLogs->where('table_name', $tableName);
                
                // 🎯 优化：只计算前端图表需要的汇总数据，不保存详细的表级统计
                $syncs = $tableLogs->sum('sync_count');
                $successful = $tableLogs->where('status', SyncLog::STATUS_SUCCESS)->sum('sync_count');
                $failed = $tableLogs->where('status', SyncLog::STATUS_FAILED)->sum('sync_count');
                $records = $tableLogs->sum('records_processed');
                
                $dayStats['total_syncs'] += $syncs;
                $dayStats['successful_syncs'] += $successful;
                $dayStats['failed_syncs'] += $failed;
                $dayStats['records_processed'] += $records;
            }
            
            $trends[] = $dayStats;
        }
        
        return $trends;
    }

    /**
     * 获取表状态 - 优化版本，移除页面不需要的字段
     */
    private function getTableStatus(): array
    {
        $tables = array_keys(config('sync.tables', []));
        $status = [];
        
        // 🚀 优化：使用单个查询获取所有表的最新同步记录
        $latestSyncs = SyncLog::whereIn('table_name', $tables)
            ->selectRaw('
                table_name,
                MAX(start_time) as latest_start_time
            ')
            ->groupBy('table_name')
            ->get()
            ->keyBy('table_name');
        
        // 获取最新同步记录的详细信息 - 只选择必要字段
        $latestSyncDetails = [];
        foreach ($latestSyncs as $tableName => $sync) {
            if ($sync->latest_start_time) {
                $latestSyncDetails[$tableName] = SyncLog::where('table_name', $tableName)
                    ->where('start_time', $sync->latest_start_time)
                    ->select(['status', 'start_time', 'end_time']) // 🎯 只选择页面需要的字段
                    ->first();
            }
        }
        
        // 🚀 优化：使用单个查询获取最近7天的成功率数据
        $successRates = SyncLog::whereIn('table_name', $tables)
            ->where('start_time', '>=', Carbon::now()->subDays(7))
            ->selectRaw('
                table_name,
                COUNT(*) as total_count,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as success_count
            ', [SyncLog::STATUS_SUCCESS])
            ->groupBy('table_name')
            ->get()
            ->keyBy('table_name');
        
        foreach ($tables as $tableName) {
            $lastSync = $latestSyncDetails[$tableName] ?? null;
            $successRateData = $successRates->get($tableName);
            
            $successRate = 0.0;
            if ($successRateData && $successRateData->total_count > 0) {
                $successRate = round(($successRateData->success_count / $successRateData->total_count) * 100, 2);
            }
            
            $status[$tableName] = [
                'table_name' => $tableName,
                // 🎯 优化：只返回前端实际需要的字段
                'last_sync' => $lastSync ? [
                    'status' => $lastSync->status,
                    'start_time' => $lastSync->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $lastSync->end_time?->format('Y-m-d H:i:s'),
                ] : null,
                'success_rate' => $successRate,
            ];
        }
        
        return $status;
    }

    // calculateTableSuccessRate方法已移除，功能整合到getTableStatus()中

    /**
     * 手动触发增量同步（异步模式，避免504超时）
     */
    public function triggerIncrementalSync(Request $request): JsonResponse
    {
        set_time_limit(30); // 设置30秒超时，足够启动任务
        
        $tableName = $request->input('table');
        $tableNames = $request->input('tables', []);
        $parallel = filter_var($request->input('parallel', true), FILTER_VALIDATE_BOOLEAN); // 转换为布尔值
        
        // 处理前端传递的JSON字符串格式的tables参数
        if (is_string($tableNames)) {
            $tableNames = json_decode($tableNames, true) ?? [];
        }
        
        Log::info("🚀 触发增量同步", [
            'tables' => $tableNames ?: [$tableName],
            'parallel' => $parallel,
            'request_data' => $request->all()
        ]);
        
        try {
            // 验证表名
            $syncTables = config('sync.tables', []);
            $requestedTables = $tableNames ?: ($tableName ? [$tableName] : []);
            
            // 检查是否有无效的表名
            $invalidTables = [];
            foreach ($requestedTables as $table) {
                if (!isset($syncTables[$table])) {
                    $invalidTables[] = $table;
                }
            }
            
            if (!empty($invalidTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '无效的表名: ' . implode(', ', $invalidTables)
                ], 400);
            }
            
            // 检查是否有表正在同步
            $runningTables = [];
            foreach ($requestedTables as $table) {
                $running = SyncLog::where('table_name', $table)
                    ->where('status', SyncLog::STATUS_RUNNING)
                    ->where('created_at', '>', Carbon::now()->subHours(2))
                    ->exists();
                    
                if ($running) {
                    $runningTables[] = $table;
                }
            }
            
            if (!empty($runningTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '以下表正在同步中，请等待完成: ' . implode(', ', $runningTables)
                ], 409);
            }
            
                        // 🔧 修复时序问题：先启动队列工作进程，再加入任务
            Log::info("🔧 修复时序：先启动队列工作进程，确保任务能被立即处理");
            
            $syncRequestedTables = $tableNames ?: ($tableName ? [$tableName] : []);
            
            if (empty($syncRequestedTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '请指定要同步的表'
                ], 400);
            }

            // 先确保有足够的队列工作进程（关键修复：调整调用顺序）
            $queueCheck = $this->ensureQueueWorkers($request);
            
            if (!$queueCheck['success']) {
                Log::warning("❌ 队列工作进程检查失败，阻止增量同步操作");
                
                return response()->json([
                    'success' => false,
                    'status' => 'blocked',
                    'message' => $queueCheck['message'],
                    'error' => 'NO_QUEUE_WORKERS',
                    'blocking' => true,
                    'queue_error' => $queueCheck,
                    'instructions' => $queueCheck['instructions'] ?? null
                ], 400);
            }

            // 再异步启动同步任务
            if ($tableName) {
                // 单表异步同步
                $result = $this->startAsyncSync([$tableName], SyncLog::TYPE_INCREMENTAL, $parallel);
                $message = "表 {$tableName} 增量同步已启动";
            } elseif (!empty($tableNames)) {
                // 多表异步同步
                $result = $this->startAsyncSync($tableNames, SyncLog::TYPE_INCREMENTAL, $parallel);
                $tableList = implode(', ', $tableNames);
                $message = "表 {$tableList} 增量同步已启动";
            }

            // 检查任务是否成功加入队列
            $successfulTasks = array_filter($result, function($taskResult) {
                return $taskResult['success'] ?? false;
            });

            if (empty($successfulTasks)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '所有任务加入队列失败',
                    'result' => $result
                ], 500);
            }

            Log::info("✅ 修复完成：工作进程已启动，任务已加入队列", [
                'successful_tasks' => count($successfulTasks),
                'total_tasks' => count($result),
                'queue_status' => $queueCheck['message']
            ]);
            
            return response()->json([
                'success' => true,
                'status' => 'started',
                'message' => $message,
                'result' => $result,
                'parallel' => $parallel && count($requestedTables) > 1,
                'sync_type' => 'incremental',
                'tables' => $requestedTables,
                'queue_status' => $queueCheck
            ]);
            
        } catch (\Exception $e) {
            Log::error("启动增量同步失败", [
                'error' => $e->getMessage(),
                'tables' => $requestedTables ?? [],
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'status' => 'failed',
                'message' => '启动同步失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查队列工作进程状态（仅检查，不自动启动）
     */
    private function ensureQueueWorkers(Request $request): array
    {
        try {
            // 获取要同步的表数量
            $tableNames = $request->input('tables', []);
            $taskCount = count($tableNames);
            
            // 检查当前队列状态
            $hasWorkers = $this->queueService->hasActiveWorkers();
            $workerCount = $this->queueService->getWorkerCount();
            $pendingJobs = $this->queueService->getPendingJobsCount();
            
            Log::info("队列状态检查: 任务数={$taskCount}, 当前工作进程={$workerCount}, 待处理任务={$pendingJobs}");
            
            // 🔧 核心策略：仅检查状态，不尝试自动启动
            if (!$hasWorkers || $workerCount === 0) {
                Log::warning("❌ 没有检测到队列工作进程，数据同步需要队列工作进程");
                
                return [
                    'success' => false,
                    'message' => '❌ 没有检测到队列工作进程，请先手动启动',
                    'error' => 'NO_QUEUE_WORKERS',
                    'manual_start_required' => true,
                    'instructions' => [
                        'title' => '🚀 启动队列工作进程',
                        'description' => '数据同步需要队列工作进程处理任务，请选择以下方式之一启动：',
                        'commands' => [
                            'simple' => './start_queue.sh 2',
                            'single_worker' => 'php artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3',
                            'background' => 'nohup php artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 &'
                        ],
                        'note' => '💡 推荐使用 ./start_queue.sh 2 启动2个工作进程',
                        'after_start' => '启动成功后，点击"刷新状态"按钮重新检查'
                    ],
                    'blocking' => true // 标记为阻塞性错误，必须解决才能继续
                ];
            }
            
            // 检查工作进程数量是否足够
            $recommendedWorkers = $taskCount > 1 ? min($taskCount, 4) : 1;
            if ($workerCount < $recommendedWorkers) {
                Log::info("当前工作进程数({$workerCount})少于推荐数量({$recommendedWorkers})");
                
                return [
                    'success' => true,
                    'message' => "队列工作进程数量偏少，推荐启动{$recommendedWorkers}个进程",
                    'worker_count' => $workerCount,
                    'recommended_workers' => $recommendedWorkers,
                    'suggestion' => "建议启动更多工作进程以提高同步效率",
                    'proceed_anyway' => true
                ];
            }
            
            // 工作进程状态良好
            return [
                'success' => true,
                'message' => "队列工作进程状态良好 ({$workerCount} 个)",
                'worker_count' => $workerCount,
                'pending_jobs' => $pendingJobs,
                'mode' => $taskCount > 1 ? 'multi_table' : 'single_table'
            ];
            
        } catch (\Exception $e) {
            Log::error("队列状态检查失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '队列状态检查失败: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 异步启动同步任务（避免504超时）
     */
    private function startAsyncSync(array $tableNames, string $syncType, bool $parallel = true): array
    {
        try {
            $results = [];
            
            // 使用数据库事务确保SyncLog创建和队列任务分发的一致性
            DB::transaction(function () use ($tableNames, $syncType, $parallel, &$results) {
                foreach ($tableNames as $tableName) {
                    // 检查是否已有pending或running状态的任务
                    $existingLog = SyncLog::where('table_name', $tableName)
                        ->whereIn('status', [SyncLog::STATUS_PENDING, SyncLog::STATUS_RUNNING])
                        ->where('created_at', '>', Carbon::now()->subHours(1))
                        ->first();
                    
                    if ($existingLog) {
                        Log::warning("表 {$tableName} 已有正在进行的同步任务", [
                            'existing_sync_log_id' => $existingLog->id,
                            'status' => $existingLog->status
                        ]);
                        
                        $results[$tableName] = [
                            'success' => false,
                            'error' => '该表已有同步任务在进行中',
                            'existing_sync_log_id' => $existingLog->id,
                            'message' => "表 {$tableName} 已有同步任务在进行中"
                        ];
                        continue;
                    }
                    
                    // 创建初始的SyncLog记录（状态设为PENDING）
                    $syncLog = SyncLog::create([
                        'table_name' => $tableName,
                        'sync_type' => $syncType,
                        'status' => SyncLog::STATUS_PENDING,
                        'start_time' => now(),
                        'records_processed' => 0,
                        'records_inserted' => 0,
                        'records_updated' => 0,
                    ]);
                    
                    // 获取表配置
                    $syncTables = config('sync.tables', []);
                    $tableConfig = $syncTables[$tableName] ?? [];
                    
                    // 使用SyncTableJob队列作业（更稳定）
                    \App\Jobs\SyncTableJob::dispatch($tableName, $syncType, $tableConfig, null, $syncLog->id);
                    
                    $results[$tableName] = [
                        'success' => true,
                        'sync_log_id' => $syncLog->id,
                        'status' => 'queued',
                        'message' => "表 {$tableName} 同步任务已加入队列"
                    ];
                    
                    Log::info("同步任务已加入队列", [
                        'table' => $tableName,
                        'sync_type' => $syncType,
                        'sync_log_id' => $syncLog->id,
                        'parallel' => $parallel
                    ]);
                }
            });
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error("异步启动同步任务失败", [
                'tables' => $tableNames,
                'sync_type' => $syncType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $results = [];
            foreach ($tableNames as $tableName) {
                $results[$tableName] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'message' => "启动 {$tableName} 同步任务失败"
                ];
            }
            
            return $results;
        }
    }

    /**
     * 手动触发全量同步（异步模式，避免504超时）
     */
    public function triggerFullSync(Request $request): JsonResponse
    {
        set_time_limit(30); // 设置30秒超时，足够启动任务
        
        $tableName = $request->input('table');
        $tableNames = $request->input('tables', []);
        $parallel = filter_var($request->input('parallel', true), FILTER_VALIDATE_BOOLEAN); // 转换为布尔值
        
        // 处理前端传递的JSON字符串格式的tables参数
        if (is_string($tableNames)) {
            $tableNames = json_decode($tableNames, true) ?? [];
        }
        
        Log::info("🚀 触发全量同步", [
            'tables' => $tableNames ?: [$tableName],
            'parallel' => $parallel,
            'request_data' => $request->all()
        ]);
        
        try {
            // 验证表名
            $syncTables = config('sync.tables', []);
            $requestedTables = $tableNames ?: ($tableName ? [$tableName] : []);
            
            // 检查是否有无效的表名
            $invalidTables = [];
            foreach ($requestedTables as $table) {
                if (!isset($syncTables[$table])) {
                    $invalidTables[] = $table;
                }
            }
            
            if (!empty($invalidTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '无效的表名: ' . implode(', ', $invalidTables)
                ], 400);
            }
            
            // 检查是否有表正在同步
            $runningTables = [];
            foreach ($requestedTables as $table) {
                $running = SyncLog::where('table_name', $table)
                    ->where('status', SyncLog::STATUS_RUNNING)
                    ->where('created_at', '>', Carbon::now()->subHours(2))
                    ->exists();
                    
                if ($running) {
                    $runningTables[] = $table;
                }
            }
            
            if (!empty($runningTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '以下表正在同步中，请等待完成: ' . implode(', ', $runningTables)
                ], 409);
            }
            
                        // 🔧 修复时序问题：先启动队列工作进程，再加入任务
            Log::info("🔧 修复时序：先启动队列工作进程，确保任务能被立即处理");
            
            $syncRequestedTables = $tableNames ?: ($tableName ? [$tableName] : []);
            
            if (empty($syncRequestedTables)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '请指定要同步的表'
                ], 400);
            }

            // 先确保有足够的队列工作进程（关键修复：调整调用顺序）
            $queueCheck = $this->ensureQueueWorkers($request);
            
            if (!$queueCheck['success']) {
                Log::warning("❌ 队列工作进程检查失败，阻止全量同步操作");
                
                return response()->json([
                    'success' => false,
                    'status' => 'blocked',
                    'message' => $queueCheck['message'],
                    'error' => 'NO_QUEUE_WORKERS',
                    'blocking' => true,
                    'queue_error' => $queueCheck,
                    'instructions' => $queueCheck['instructions'] ?? null
                ], 400);
            }

            // 再异步启动同步任务
            if ($tableName) {
                // 单表异步同步
                $result = $this->startAsyncSync([$tableName], SyncLog::TYPE_FULL, $parallel);
                $message = "表 {$tableName} 全量同步已启动";
            } elseif (!empty($tableNames)) {
                // 多表异步同步
                $result = $this->startAsyncSync($tableNames, SyncLog::TYPE_FULL, $parallel);
                $tableList = implode(', ', $tableNames);
                $message = "表 {$tableList} 全量同步已启动";
            }

            // 检查任务是否成功加入队列
            $successfulTasks = array_filter($result, function($taskResult) {
                return $taskResult['success'] ?? false;
            });

            if (empty($successfulTasks)) {
                return response()->json([
                    'success' => false,
                    'status' => 'failed',
                    'message' => '所有任务加入队列失败',
                    'result' => $result
                ], 500);
            }

            Log::info("✅ 修复完成：工作进程已启动，任务已加入队列", [
                'successful_tasks' => count($successfulTasks),
                'total_tasks' => count($result),
                'queue_status' => $queueCheck['message']
            ]);
            
            return response()->json([
                'success' => true,
                'status' => 'started',
                'message' => $message,
                'result' => $result,
                'parallel' => $parallel && count($requestedTables) > 1,
                'sync_type' => 'full',
                'tables' => $requestedTables,
                'queue_status' => $queueCheck
            ]);
            
        } catch (\Exception $e) {
            Log::error("启动全量同步失败", [
                'error' => $e->getMessage(),
                'tables' => $requestedTables ?? [],
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'status' => 'failed',
                'message' => '启动同步失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取同步日志
     */
    public function getSyncLogs(Request $request): JsonResponse
    {
        $tableName = $request->input('table');
        $limit = $request->input('limit', 50);
        
        $query = SyncLog::query();
        
        if ($tableName) {
            $query->where('table_name', $tableName);
        }
        
        $logs = $query->orderBy('start_time', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'table_name' => $log->table_name,
                    'sync_type' => $log->sync_type,
                    'status' => $log->status,
                    'start_time' => $log->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $log->end_time?->format('Y-m-d H:i:s'),
                    'duration' => $log->start_time && $log->end_time 
                        ? $log->end_time->diffInSeconds($log->start_time) . 's'
                        : null,
                    'records_processed' => $log->records_processed,
                    'records_inserted' => $log->records_inserted,
                    'records_updated' => $log->records_updated,
                    'error_message' => $log->error_message,
                ];
            });
        
        return response()->json([
            'success' => true,
            'logs' => $logs
        ]);
    }

    /**
     * 获取批处理同步状态
     */
    public function getBatchStatus(Request $request): JsonResponse
    {
        $batchId = $request->input('batch_id');
        
        if (!$batchId) {
            return response()->json([
                'success' => false,
                'message' => '缺少批处理ID'
            ], 400);
        }

        try {
            $result = $this->syncService->getBatchStatus($batchId);
            
            return response()->json($result);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取批处理状态失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试用：获取同步统计数据（无需认证）
     */
    public function getTestSyncStats(): JsonResponse
    {
        $stats = $this->syncService->getSyncStats();
        
        // 获取最近7天的同步趋势
        $trends = $this->getSyncTrends();
        
        // 获取表状态
        $tableStatus = $this->getTableStatus();
        
        return response()->json([
            'stats' => $stats,
            'trends' => $trends,
            'table_status' => $tableStatus,
            'last_updated' => now()->format('Y-m-d H:i:s')
        ]);
    }

    /**
     * 获取同步进度（实时）
     */
    public function getSyncProgress(Request $request): JsonResponse
    {
        $tableName = $request->input('table_name');
        
        if (!$tableName) {
            return response()->json([
                'success' => false,
                'message' => '缺少表名参数'
            ], 400);
        }

        try {
            // 检查是否有正在运行的同步任务
            $runningSync = SyncLog::where('table_name', $tableName)
                ->where('status', SyncLog::STATUS_RUNNING)
                ->orderBy('start_time', 'desc')
                ->first();

            if (!$runningSync) {
                return response()->json([
                    'success' => true,
                    'running' => false,
                    'message' => '没有正在运行的同步任务'
                ]);
            }

            // 获取配置信息
            $config = config("sync.tables.{$tableName}", []);
            $filterConditions = $config['filter_conditions'] ?? [];
            
            // 构建查询条件以获取总记录数
            $whereConditions = [];
            $bindings = [];
            
            foreach ($filterConditions as $field => $value) {
                $whereConditions[] = "{$field} = ?";
                $bindings[] = $value;
            }
            
            $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

            // 获取Oracle数据总数
            $countSql = "SELECT COUNT(*) as total FROM {$tableName} {$whereClause}";
            $totalRecords = DB::connection('oracle')->select($countSql, $bindings)[0]->total ?? 0;

            // 获取MySQL中已处理的记录数
            $processedRecords = $runningSync->records_processed ?? 0;
            
            // 如果数据库中的进度为0，尝试从日志文件中获取实时进度
            if ($processedRecords == 0) {
                $logProgress = $this->getProgressFromLogs($tableName);
                if ($logProgress) {
                    $processedRecords = $logProgress['processed'];
                }
            }

            // 计算进度百分比
            $percentage = $totalRecords > 0 ? round(($processedRecords / $totalRecords) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'running' => true,
                'table_name' => $tableName,
                'progress' => [
                    'processed_records' => $processedRecords,
                    'total_records' => $totalRecords,
                    'percentage' => $percentage,
                    'start_time' => $runningSync->start_time?->format('Y-m-d H:i:s'),
                    'duration' => $runningSync->start_time 
                        ? Carbon::now()->diffInSeconds($runningSync->start_time) 
                        : 0,
                    'estimated_remaining' => $percentage > 0 && $percentage < 100
                        ? round((100 - $percentage) / $percentage * Carbon::now()->diffInSeconds($runningSync->start_time))
                        : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取同步进度失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 从日志文件中获取最新的同步进度
     */
    private function getProgressFromLogs(string $tableName): ?array
    {
        try {
            $logFile = storage_path('logs/laravel.log');
            if (!file_exists($logFile)) {
                return null;
            }

            // 读取日志文件的最后几行
            $command = "tail -n 50 " . escapeshellarg($logFile) . " | grep " . escapeshellarg("同步表 {$tableName} 进度");
            $output = shell_exec($command);
            
            if (!$output) {
                return null;
            }

            // 解析最后一条进度记录
            $lines = explode("\n", trim($output));
            $lastLine = trim(end($lines));
            
            if (empty($lastLine)) {
                return null;
            }

            // 提取JSON部分
            if (preg_match('/\{[^}]+\}/', $lastLine, $matches)) {
                $jsonData = json_decode($matches[0], true);
                if ($jsonData && isset($jsonData['processed'])) {
                    return [
                        'processed' => $jsonData['processed'],
                        'total' => $jsonData['total'] ?? 0,
                        'percentage' => $jsonData['percentage'] ?? 0
                    ];
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::warning("从日志获取进度失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取多表同步进度
     */
    public function getMultiTableProgress(Request $request): JsonResponse
    {
        $tableNamesInput = $request->input('tables', []);
        $batchId = $request->input('batch_id');

        // 处理前端传来的参数，可能是字符串（逗号分隔）或数组
        if (is_string($tableNamesInput)) {
            $tableNames = array_filter(explode(',', $tableNamesInput));
        } else {
            $tableNames = $tableNamesInput;
        }

        if (empty($tableNames)) {
            return response()->json([
                'success' => false,
                'message' => '未指定表名'
            ]);
        }

        $tableProgress = [];
        
        foreach ($tableNames as $tableName) {
            // 检查是否有正在运行的同步任务
            $runningSync = SyncLog::where('table_name', $tableName)
                ->where('status', SyncLog::STATUS_RUNNING)
                ->where('created_at', '>', Carbon::now()->subHours(2))
                ->orderBy('created_at', 'desc')
                ->first();

            if ($runningSync) {
                // 有正在运行的任务，计算进度
                $progress = $this->getTableProgressFromLog($runningSync);
                $tableProgress[$tableName] = [
                    'table_name' => $tableName,
                    'status' => 'running',
                    'progress' => $progress,
                    'sync_log_id' => $runningSync->id,
                    'start_time' => $runningSync->start_time?->format('Y-m-d H:i:s'),
                    'sync_type' => $runningSync->sync_type,
                ];
            } else {
                // 检查最近完成的任务 (扩大时间窗口到30分钟)
                $recentSync = SyncLog::where('table_name', $tableName)
                    ->whereIn('status', [SyncLog::STATUS_SUCCESS, SyncLog::STATUS_FAILED])
                    ->where('created_at', '>', Carbon::now()->subMinutes(30))
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($recentSync) {
                    $tableProgress[$tableName] = [
                        'table_name' => $tableName,
                        'status' => $recentSync->status,
                        'progress' => [
                            'percentage' => $recentSync->status === SyncLog::STATUS_SUCCESS ? 100 : 0,
                            'processed_records' => $recentSync->records_processed,
                            'total_records' => $recentSync->records_processed,
                            'message' => $recentSync->status === SyncLog::STATUS_SUCCESS 
                                ? '同步完成' 
                                : '同步失败: ' . $recentSync->error_message
                        ],
                        'sync_log_id' => $recentSync->id,
                        'start_time' => $recentSync->start_time?->format('Y-m-d H:i:s'),
                        'end_time' => $recentSync->end_time?->format('Y-m-d H:i:s'),
                        'sync_type' => $recentSync->sync_type,
                        'records_processed' => $recentSync->records_processed,
                    ];
                } else {
                    $tableProgress[$tableName] = [
                        'table_name' => $tableName,
                        'status' => 'not_running',
                        'progress' => null,
                        'message' => '无正在运行的同步任务'
                    ];
                }
            }
        }

        return response()->json([
            'success' => true,
            'table_progress' => $tableProgress,
            'batch_id' => $batchId,
            'last_updated' => now()->format('Y-m-d H:i:s')
        ]);
    }

    /**
     * 从SyncLog计算表的同步进度
     */
    private function getTableProgressFromLog(SyncLog $syncLog): array
    {
        $progress = [
            'percentage' => 0,
            'processed_records' => $syncLog->records_processed ?? 0,
            'total_records' => 0,
            'message' => '正在同步...'
        ];

        try {
            // 尝试从Oracle实时获取总记录数
            $config = config("sync.tables.{$syncLog->table_name}", []);
            $filterConditions = $config['filter_conditions'] ?? [];
            
            $whereConditions = [];
            $bindings = [];
            
            foreach ($filterConditions as $field => $value) {
                $whereConditions[] = "{$field} = ?";
                $bindings[] = $value;
            }
            
            $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
            $countSql = "SELECT COUNT(*) as total FROM {$syncLog->table_name} {$whereClause}";
            
            $totalRecords = DB::connection('oracle')->select($countSql, $bindings)[0]->total ?? 0;
            
            if ($totalRecords > 0) {
                $processedRecords = $syncLog->records_processed ?? 0;
                $percentage = min(99, round(($processedRecords / $totalRecords) * 100, 2));
                
                $progress['percentage'] = $percentage;
                $progress['total_records'] = $totalRecords;
                $progress['processed_records'] = $processedRecords;
                
                // 计算运行时间和速度
                $runningTime = $syncLog->start_time ? Carbon::now()->diffInSeconds($syncLog->start_time) : 0;
                $speed = $runningTime > 0 ? round($processedRecords / ($runningTime / 60), 0) : 0;
                
                if ($percentage >= 100) {
                    $progress['message'] = "✅ 同步即将完成... ({$processedRecords}/{$totalRecords})";
                } else {
                    $progress['message'] = "{$syncLog->table_name}: {$processedRecords}/{$totalRecords} 条记录 ({$percentage}%)";
                    if ($speed > 0) {
                        $progress['message'] .= " - {$speed} 条/分钟";
                    }
                }
                
                return $progress;
            }
        } catch (\Exception $e) {
            Log::warning("获取Oracle表记录数失败: " . $e->getMessage());
        }

        // 如果Oracle查询失败，回退到估算方法
        if ($syncLog->records_processed > 0) {
            $estimatedTotalRecords = $this->getEstimatedTableRecords($syncLog->table_name);
            
            if ($estimatedTotalRecords > 0) {
                $percentage = min(99, round(($syncLog->records_processed / $estimatedTotalRecords) * 100));
                $progress['percentage'] = $percentage;
                $progress['total_records'] = $estimatedTotalRecords;
                $progress['message'] = "已处理 {$syncLog->records_processed} / ~{$estimatedTotalRecords} 条记录 ({$percentage}%)";
            } else {
                $progress['message'] = "已处理 {$syncLog->records_processed} 条记录";
                $progress['percentage'] = 50;
            }
        } else {
            $progress['message'] = '正在初始化同步...';
            $progress['percentage'] = 5;
        }

        return $progress;
    }

    /**
     * 估算表的记录数（基于历史同步记录）
     */
    private function getEstimatedTableRecords(string $tableName): int
    {
        // 获取最近成功的全量同步记录
        $recentFullSync = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_SUCCESS)
            ->where('sync_type', SyncLog::TYPE_FULL)
            ->where('created_at', '>', Carbon::now()->subDays(30))
            ->orderBy('created_at', 'desc')
            ->first();

        if ($recentFullSync && $recentFullSync->records_processed > 0) {
            return $recentFullSync->records_processed;
        }

        // 如果没有全量同步记录，使用最近成功同步的最大记录数
        $maxRecords = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_SUCCESS)
            ->where('created_at', '>', Carbon::now()->subDays(7))
            ->max('records_processed');

        return $maxRecords ?? 0;
    }

    /**
     * 获取队列状态（新增方法）
     */
    public function getQueueStatus(): JsonResponse
    {
        try {
            $stats = $this->queueService->getQueueStats();
            
            return response()->json([
                'success' => true,
                'worker_count' => $stats['worker_count'],
                'pending_jobs' => $stats['pending_jobs'],
                'failed_jobs' => $stats['failed_jobs'],
                'has_active_workers' => $stats['has_active_workers'],
                'timestamp' => $stats['timestamp'],
                'message' => $stats['has_active_workers'] 
                    ? "队列工作进程正常运行 ({$stats['worker_count']} 个)" 
                    : '没有检测到队列工作进程'
            ]);
        } catch (\Exception $e) {
            Log::error('获取队列状态失败: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '获取队列状态失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'worker_count' => 0,
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'has_active_workers' => false,
                'timestamp' => now()->toISOString()
            ]);
        }
    }

    /**
     * 获取各表数据条数 - 优化版本
     */
    public function getTableDataCounts(): JsonResponse
    {
        try {
            $tables = [
                'RTAXL_T' => '分类数据表',
                'PMAB_T' => '客户主表',
                'PMAAL_T' => '客户多语言表',
                'BMBA_T' => 'BOM表',
                'BMAA_T' => '物料BOM主表',
                'IMAA_T' => '物料主表',
                'IMAAL_T' => '物料多语言表',
                'IMAF_T' => '物料工厂表'
            ];

            // 🚀 优化：使用缓存机制，减少Oracle查询频率
            $cacheKey = 'dashboard:table_data_counts';
            $cacheTtl = 300; // 5分钟缓存
            
            $result = Cache::remember($cacheKey, $cacheTtl, function () use ($tables) {
                $result = [];
                
                // 🚀 优化：使用并发查询减少总查询时间
                $queries = [];
                foreach ($tables as $tableName => $description) {
                    $queries[$tableName] = function () use ($tableName) {
                        try {
                            return DB::table($tableName)->count();
                        } catch (\Exception $e) {
                            Log::warning("查询表 {$tableName} 数据量失败", ['error' => $e->getMessage()]);
                            return 0;
                        }
                    };
                }
                
                // 执行所有查询
                foreach ($queries as $tableName => $query) {
                    $count = $query();
                    $result[$tableName] = [
                        'table_name' => $tableName,
                        'record_count' => $count,
                        'status' => $count > 0 ? 'available' : ($count === 0 ? 'empty' : 'not_exist')
                    ];
                }
                
                return $result;
            });

            return response()->json([
                'success' => true,
                'tables' => $result,
                'cached' => Cache::has($cacheKey)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取表数据条数失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取24小时内的同步记录 - 增量数据优化版本
     * 只返回有数据变动的记录，确保24小时内数据完整性
     */
    public function getSyncRecords24h(Request $request): JsonResponse
    {
        try {
            // 🔍 添加调试信息
            $debugInfo = [
                'db_host' => config('database.connections.mysql.host'),
                'db_database' => config('database.connections.mysql.database'),
                'current_time' => now()->format('Y-m-d H:i:s'),
                '24h_ago' => now()->subDay()->format('Y-m-d H:i:s'),
                'timezone' => config('app.timezone'),
                'app_env' => app()->environment()
            ];
            
            // 🎯 核心优化：只查询有数据变动的记录（records_processed > 0）
            $query = SyncLog::where('start_time', '>=', now()->subDay())
                ->where('records_processed', '>', 0) // 🔥 关键过滤：只要有数据变动的记录
                ->select([
                    'id', 'table_name', 'sync_type', 'status', 
                    'start_time', 'records_processed', 'records_inserted', 'records_updated'
                ])
                ->orderBy('start_time', 'desc');
            
            // 🚀 移除分页限制，确保24小时内所有有效数据都返回
            $records = $query->get();
            
            // 🔍 调试：记录原始查询结果
            $debugInfo['raw_query_count'] = $records->count();
            $debugInfo['query_sql'] = $query->toSql();
            $debugInfo['query_bindings'] = $query->getBindings();
            
            // 🎯 按表分组统计，进一步减少前端数据量
            $groupedData = $records->groupBy('table_name')->map(function ($tableRecords, $tableName) {
                // 计算该表24小时内的汇总数据
                $totalProcessed = $tableRecords->sum('records_processed');
                $totalInserted = $tableRecords->sum('records_inserted');
                $totalUpdated = $tableRecords->sum('records_updated');
                $syncCount = $tableRecords->count();
                $successCount = $tableRecords->where('status', 'success')->count();
                
                // 按小时分组数据，便于前端绘制曲线
                $hourlyData = [];
                for ($i = 23; $i >= 0; $i--) {
                    $hourStart = now()->subHours($i)->startOfHour();
                    $hourEnd = now()->subHours($i)->endOfHour();
                    
                    $hourRecords = $tableRecords->filter(function ($record) use ($hourStart, $hourEnd) {
                        $recordTime = Carbon::parse($record->start_time);
                        return $recordTime >= $hourStart && $recordTime <= $hourEnd;
                    });
                    
                    $hourProcessed = $hourRecords->sum('records_processed');
                    
                    // 🎯 只有有数据变动的小时才加入数组
                    if ($hourProcessed > 0) {
                        $hourlyData[] = [
                            'hour' => $hourStart->format('H:i'),
                            'timestamp' => $hourStart->toISOString(),
                            'records_processed' => $hourProcessed,
                            'sync_count' => $hourRecords->count()
                        ];
                    }
                }
                
                return [
                    'table_name' => $tableName,
                    'summary' => [
                        'total_processed' => $totalProcessed,
                        'total_inserted' => $totalInserted,
                        'total_updated' => $totalUpdated,
                        'sync_count' => $syncCount,
                        'success_rate' => $syncCount > 0 ? round(($successCount / $syncCount) * 100, 1) : 0
                    ],
                    'hourly_data' => $hourlyData, // 🎯 只包含有数据变动的小时
                    'latest_sync' => $tableRecords->first() ? [
                        'start_time' => $tableRecords->first()->start_time,
                        'records_processed' => $tableRecords->first()->records_processed,
                        'status' => $tableRecords->first()->status
                    ] : null
                ];
            });

            // 🎯 全局统计（仅有变动的数据）
            $globalStats = [
                'total_syncs_with_changes' => $records->count(),
                'total_records_processed' => $records->sum('records_processed'),
                'total_records_inserted' => $records->sum('records_inserted'),
                'total_records_updated' => $records->sum('records_updated'),
                'success_count' => $records->where('status', 'success')->count(),
                'tables_with_changes' => $groupedData->count(),
                'data_freshness' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'records' => $groupedData->values(), // 🎯 按表分组的增量数据
                    'stats' => $globalStats,
                    'has_changes' => $records->isNotEmpty(), // 🎯 标识是否有数据变动
                    'query_info' => [
                        'time_range' => '24小时',
                        'filter_applied' => 'records_processed > 0',
                        'data_count' => $records->count(),
                        'pagination' => '无分页限制，确保数据完整性'
                    ],
                    'debug_info' => $debugInfo // 🔍 添加调试信息
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取同步记录失败：' . $e->getMessage(),
                'data' => [
                    'records' => [],
                    'stats' => [],
                    'has_changes' => false
                ],
                'debug_info' => $debugInfo ?? []
            ], 500);
        }
    }

} 