<?php

namespace App\Http\Controllers;

use App\Models\BOM;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Material;
use App\Models\MaterialTranslation;
use App\Models\SyncChangeLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * 第三方系统API控制器
 * 
 * 为第三方系统提供安全的数据访问接口
 * 支持获取BOM、categories、customers、material_translations、materials等表数据
 * 
 * 安全特性：
 * - 使用Laravel Sanctum Token认证
 * - 数据只读访问，不允许修改
 * - 分页和过滤支持
 * - 详细的访问日志
 */
class ExternalApiController extends Controller
{
    /**
     * 获取物料分类列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCategories(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_code' => 'string|max:10',
                'locale' => 'string|in:zh_CN,en_US,th_TH',
                'per_page' => 'integer|min:1|max:1000',
                'page' => 'integer|min:1',
                'search' => 'string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $companyCode = $request->input('company_code', 'TB');
            $locale = $request->input('locale', 'zh_CN');
            $perPage = $request->input('per_page', 20);
            $search = $request->input('search');

            $query = Category::where('company_code', $companyCode)
                ->where('locale', $locale);

            // 搜索功能
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('category_code', 'like', "%{$search}%")
                      ->orWhere('category_name', 'like', "%{$search}%");
                });
            }

            $categories = $query->orderBy('category_code')
                ->paginate($perPage);

            // 记录访问日志
            Log::info('第三方系统访问分类数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'company_code' => $companyCode,
                'locale' => $locale,
                'count' => $categories->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $categories->items(),
                'pagination' => [
                    'current_page' => $categories->currentPage(),
                    'last_page' => $categories->lastPage(),
                    'per_page' => $categories->perPage(),
                    'total' => $categories->total(),
                    'from' => $categories->firstItem(),
                    'to' => $categories->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取分类数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取分类数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取客户列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCustomers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_code' => 'string|max:10',
                'per_page' => 'integer|min:1|max:1000',
                'page' => 'integer|min:1',
                'search' => 'string|max:100',
                'status' => 'string|in:Y,N',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $companyCode = $request->input('company_code', 'TB');
            $perPage = $request->input('per_page', 20);
            $search = $request->input('search');
            $status = $request->input('status');

            $query = Customer::where('company_code', $companyCode)
                ->with(['currency', 'taxType', 'tradeTerm']);

            // 状态过滤
            if ($status) {
                $query->where('status', $status);
            }

            // 搜索功能
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('code', 'like', "%{$search}%")
                      ->orWhere('short_name', 'like', "%{$search}%")
                      ->orWhere('full_name', 'like', "%{$search}%");
                });
            }

            $customers = $query->orderBy('code')
                ->paginate($perPage);

            // 记录访问日志
            Log::info('第三方系统访问客户数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'company_code' => $companyCode,
                'count' => $customers->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $customers->items(),
                'pagination' => [
                    'current_page' => $customers->currentPage(),
                    'last_page' => $customers->lastPage(),
                    'per_page' => $customers->perPage(),
                    'total' => $customers->total(),
                    'from' => $customers->firstItem(),
                    'to' => $customers->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取客户数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取客户数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取物料列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMaterials(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_code' => 'string|max:10',
                'per_page' => 'integer|min:1|max:1000',
                'page' => 'integer|min:1',
                'search' => 'string|max:100',
                'category_code' => 'string|max:10',
                'status' => 'string|in:Y,N',
                'include_translations' => 'in:true,false,1,0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $companyCode = $request->input('company_code', 'TB');
            $perPage = $request->input('per_page', 20);
            $search = $request->input('search');
            $categoryCode = $request->input('category_code');
            $status = $request->input('status', 'Y');
            $includeTranslations = $request->boolean('include_translations', true);

            $query = Material::where('company_code', $companyCode);

            // 状态过滤
            if ($status) {
                $query->where('status', $status);
            }

            // 分类过滤
            if ($categoryCode) {
                $query->where('category_code', $categoryCode);
            }

            // 是否包含翻译信息
            if ($includeTranslations) {
                $query->with(['translations']);
            }

            // 搜索功能
            if ($search) {
                $query->where(function($q) use ($search, $includeTranslations) {
                    $q->where('material_code', 'like', "%{$search}%")
                      ->orWhere('figure', 'like', "%{$search}%");
                    
                    // 如果包含翻译，也搜索翻译字段
                    if ($includeTranslations) {
                        $q->orWhereHas('translations', function($translationQuery) use ($search) {
                            $translationQuery->where('product_name', 'like', "%{$search}%")
                                ->orWhere('specification', 'like', "%{$search}%");
                        });
                    }
                });
            }

            $materials = $query->orderBy('category_code')
                ->orderBy('material_code')
                ->paginate($perPage);

            // 记录访问日志
            Log::info('第三方系统访问物料数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'company_code' => $companyCode,
                'category_code' => $categoryCode,
                'count' => $materials->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $materials->items(),
                'pagination' => [
                    'current_page' => $materials->currentPage(),
                    'last_page' => $materials->lastPage(),
                    'per_page' => $materials->perPage(),
                    'total' => $materials->total(),
                    'from' => $materials->firstItem(),
                    'to' => $materials->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取物料数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取物料数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取物料翻译列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMaterialTranslations(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_code' => 'string|max:10',
                'locale' => 'string|in:zh_CN,en_US,th_TH',
                'per_page' => 'integer|min:1|max:1000',
                'page' => 'integer|min:1',
                'search' => 'string|max:100',
                'material_code' => 'string|max:20',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $companyCode = $request->input('company_code', 'TB');
            $locale = $request->input('locale', 'zh_CN');
            $perPage = $request->input('per_page', 20);
            $search = $request->input('search');
            $materialCode = $request->input('material_code');

            $query = MaterialTranslation::where('company_code', $companyCode)
                ->where('locale', $locale)
                ->with(['material']);

            // 物料编码过滤
            if ($materialCode) {
                $query->where('material_code', $materialCode);
            }

            // 搜索功能
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('material_code', 'like', "%{$search}%")
                      ->orWhere('product_name', 'like', "%{$search}%")
                      ->orWhere('specification', 'like', "%{$search}%");
                });
            }

            $translations = $query->orderBy('material_code')
                ->paginate($perPage);

            // 记录访问日志
            Log::info('第三方系统访问物料翻译数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'company_code' => $companyCode,
                'locale' => $locale,
                'count' => $translations->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $translations->items(),
                'pagination' => [
                    'current_page' => $translations->currentPage(),
                    'last_page' => $translations->lastPage(),
                    'per_page' => $translations->perPage(),
                    'total' => $translations->total(),
                    'from' => $translations->firstItem(),
                    'to' => $translations->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取物料翻译数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取物料翻译数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取BOM结构数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getBOM(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_code' => 'string|max:10',
                'per_page' => 'integer|min:1|max:1000',
                'page' => 'integer|min:1',
                'parent_material_code' => 'string|max:20',
                'child_material_code' => 'string|max:20',
                'customer_code' => 'string|max:20',
                'status' => 'string|in:Y,N',
                'is_optional' => 'string|in:Y,N',
                'effective_date' => 'date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $companyCode = $request->input('company_code', 'TB');
            $perPage = $request->input('per_page', 20);
            $parentMaterialCode = $request->input('parent_material_code');
            $childMaterialCode = $request->input('child_material_code');
            $customerCode = $request->input('customer_code');
            $status = $request->input('status', 'Y');
            $isOptional = $request->input('is_optional');
            $effectiveDate = $request->input('effective_date', Carbon::now()->format('Y-m-d'));

            $query = BOM::where('company_code', $companyCode)
                ->where('status', $status)
                ->where('effective_time', '<=', $effectiveDate)
                ->where(function($q) use ($effectiveDate) {
                    $q->whereNull('failure_time')
                      ->orWhere('failure_time', '>=', $effectiveDate);
                });

            // 父料件过滤
            if ($parentMaterialCode) {
                $query->where('parent_material_code', $parentMaterialCode);
            }

            // 子料件过滤
            if ($childMaterialCode) {
                $query->where('child_material_code', $childMaterialCode);
            }

            // 客户过滤
            if ($customerCode) {
                $query->where('customer_code', $customerCode);
            }

            // 可选件过滤
            if ($isOptional) {
                $query->where('is_optional', $isOptional);
            }

            $BOM = $query->orderBy('parent_material_code')
                ->orderBy('child_material_code')
                ->paginate($perPage);

            // 记录访问日志
            Log::info('第三方系统访问BOM数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'company_code' => $companyCode,
                'parent_material_code' => $parentMaterialCode,
                'count' => $BOM->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $BOM->items(),
                'pagination' => [
                    'current_page' => $BOM->currentPage(),
                    'last_page' => $BOM->lastPage(),
                    'per_page' => $BOM->perPage(),
                    'total' => $BOM->total(),
                    'from' => $BOM->firstItem(),
                    'to' => $BOM->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取BOM数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取BOM数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }



    /**
     * 获取API使用统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getApiStats(Request $request): JsonResponse
    {
        try {
            // 记录访问日志
            Log::info('第三方系统访问API统计', [
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'message' => '第三方系统API接口正常运行',
                    'timestamp' => now()->toISOString(),
                    'user_id' => auth('sanctum')->user()?->id
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取API统计失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取API统计失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取增量变更记录
     * 
     * 从本地MySQL的sync_change_log表获取变更记录列表
     * 只返回本地业务表（bom、categories、customers、material_translations、materials）的变更
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getIncrementalChanges(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'table_name' => 'string|in:BOM,categories,customers,material_translations,materials',
                'change_type' => 'string|in:insert,update,delete',
                'since_time' => 'date_format:Y-m-d H:i:s',
                'until_time' => 'date_format:Y-m-d H:i:s',
                'limit' => 'integer|min:1|max:10000',
                'last_access_time' => 'date_format:Y-m-d H:i:s', // 第三方最近一次访问时间
                'include_summary' => 'boolean', // 是否包含数据摘要
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $tableName = $request->input('table_name');
            $changeType = $request->input('change_type');
            $sinceTime = $request->input('since_time');
            $untilTime = $request->input('until_time');
            $lastAccessTime = $request->input('last_access_time'); // 第三方最近一次访问时间
            $limit = $request->input('limit', 1000);
            $includeSummary = $request->boolean('include_summary', false); // 默认不包含摘要

            // 如果提供了last_access_time，优先使用它作为起始时间
            if ($lastAccessTime) {
                $sinceTime = $lastAccessTime;
            }

            // 使用SyncChangeLog模型查询本地MySQL数据
            $changes = SyncChangeLog::getChangesAfter(
                $sinceTime,
                $tableName,
                $changeType,
                $limit
            );

            // 如果设置了结束时间，进一步过滤
            if ($untilTime) {
                $changes = $changes->filter(function($change) use ($untilTime) {
                    return $change->created_at <= $untilTime;
                });
            }

            // 转换为标准格式，主要提供变更记录信息供第二步查询使用
            $processedChanges = $changes->map(function($change) use ($includeSummary) {
                $changeInfo = [
                    'id' => $change->id,
                    'table_name' => $change->table_name,
                    'change_type' => $change->change_type,
                    'pk_json' => $change->pk_json,
                    'pk_old_json' => $change->pk_old_json,
                    'change_time' => $change->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $change->updated_at->format('Y-m-d H:i:s'),
                ];

                // 如果需要包含数据摘要（可选，用于快速预览）
                if ($includeSummary) {
                    $summary = $this->getDataSummaryForLocalTable($change);
                    if ($summary) {
                        $changeInfo['data_summary'] = $summary;
                    }
                }

                return $changeInfo;
            })->values()->toArray();

            // 按表名分组统计
            $tableStats = collect($processedChanges)
                ->groupBy('table_name')
                ->map(function($items, $tableName) {
                    return [
                        'table_name' => $tableName,
                        'total_changes' => $items->count(),
                        'change_types' => $items->groupBy('change_type')->map->count(),
                    ];
                })
                ->values()
                ->toArray();

            // 记录访问日志
            Log::info('第三方系统获取本地表增量变更记录列表', [
                'user_id' => auth('sanctum')->user()?->id,
                'table_name' => $tableName,
                'change_type' => $changeType,
                'since_time' => $sinceTime,
                'until_time' => $untilTime,
                'last_access_time' => $lastAccessTime,
                'include_summary' => $includeSummary,
                'record_count' => count($processedChanges)
            ]);

            return response()->json([
                'success' => true,
                'data' => $processedChanges,
                'meta' => [
                    'record_count' => count($processedChanges),
                    'limit' => $limit,
                    'include_summary' => $includeSummary,
                    'table_stats' => $tableStats,
                    'last_query_time' => now()->format('Y-m-d H:i:s'), // 返回当前查询时间，供第三方下次使用
                    'filters' => [
                        'table_name' => $tableName,
                        'change_type' => $changeType,
                        'since_time' => $sinceTime,
                        'until_time' => $untilTime,
                        'last_access_time' => $lastAccessTime
                    ],
                    'usage_guide' => [
                        'step_1' => '当前接口返回本地业务表变更记录列表',
                        'step_2' => '使用change_id调用/api/external/incremental/record-data获取具体数据',
                        'supported_tables' => ['BOM', 'categories', 'customers', 'material_translations', 'materials']
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取本地表增量变更记录失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取增量变更记录失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取指定变更记录的完整数据
     * 
     * 根据变更记录ID，从对应的MySQL本地表获取具体的业务数据
     * 返回表的完整字段信息，确保第三方系统获得完整的数据结构
     * 
     * 支持的业务表：
     * - bom：BOM结构数据，包含所有字段(id, company_code, parent_material_code, child_material_code, customer_code, quantity, unit, sequence, effective_time, failure_time, is_optional, remark, status, created_at, updated_at, deleted_at)
     * - categories：物料分类数据，包含所有字段(id, company_code, category_code, category_name, locale, status, created_at, updated_at, deleted_at)
     * - customers：客户数据，包含所有字段(id, company_code, code, short_name, full_name, currency_code, tax_type_code, trade_term_code, payment_term_code, status, created_at, updated_at, deleted_at)
     * - material_translations：物料翻译数据，包含所有字段(id, company_code, material_code, locale, product_name, specification, status, created_at, updated_at, deleted_at)
     * - materials：物料主数据，包含所有字段(id, company_code, material_code, figure, unit, category_code, gross_weight, net_weight, paint_area, work_hours, status, created_at, updated_at, deleted_at)
     * 
     * @param Request $request
     * @return JsonResponse 返回record_data字段包含表的所有字段
     */
    public function getChangeRecordData(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'change_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $changeId = $request->input('change_id');

            // 从本地MySQL获取变更记录
            $change = SyncChangeLog::find($changeId);

            if (!$change) {
                return response()->json([
                    'success' => false,
                    'message' => '变更记录不存在'
                ], 404);
            }

            // 验证表名是否支持
            $supportedTables = ['BOM', 'categories', 'customers', 'material_translations', 'materials'];
            if (!in_array($change->table_name, $supportedTables)) {
                return response()->json([
                    'success' => false,
                    'message' => "不支持的表：{$change->table_name}"
                ], 400);
            }

            $result = [
                'change_info' => [
                    'id' => $change->id,
                    'table_name' => $change->table_name,
                    'change_type' => $change->change_type,
                    'pk_json' => $change->pk_json,
                    'pk_old_json' => $change->pk_old_json,
                    'change_time' => $change->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $change->updated_at->format('Y-m-d H:i:s'),
                    'description' => $this->getTableDescription($change->table_name)
                ],
                'record_data' => null,
                'old_record_data' => null,
            ];

            // 根据变更类型获取相应的数据
            try {
                switch (strtolower($change->change_type)) {
                    case 'insert':
                        // INSERT操作：获取新插入的记录
                        $result['record_data'] = $this->fetchLocalTableRecordData($change->table_name, $change->pk_json);
                        $result['message'] = '新增记录数据';
                        break;

                    case 'update':
                        // UPDATE操作：获取当前记录和旧记录（如果有pk_old_json）
                        $result['record_data'] = $this->fetchLocalTableRecordData($change->table_name, $change->pk_json);
                        if ($change->pk_old_json && $change->pk_old_json !== $change->pk_json) {
                            // 主键有变化，获取旧记录
                            $result['old_record_data'] = $this->fetchLocalTableRecordData($change->table_name, $change->pk_old_json);
                            $result['message'] = '更新记录数据（包含主键变更）';
                        } else {
                            $result['message'] = '更新记录数据';
                        }
                        break;

                    case 'delete':
                        // DELETE操作：尝试从pk_old_json获取被删除的记录信息
                        $pkData = $change->pk_old_json ?: $change->pk_json;
                        if ($pkData) {
                            // 注意：记录可能已被删除，这里提供删除前的主键信息
                            $result['old_record_data'] = $pkData;
                            $result['message'] = 'DELETE操作，记录已被删除，提供删除前的主键信息';
                        } else {
                            $result['message'] = 'DELETE操作，无法获取被删除记录的详细信息';
                        }
                        break;
                }

            } catch (\Exception $e) {
                $result['data_fetch_error'] = $e->getMessage();
                $result['message'] = '获取业务数据时发生错误';
                
                Log::warning('获取MySQL业务数据失败', [
                    'change_id' => $changeId,
                    'table_name' => $change->table_name,
                    'error' => $e->getMessage()
                ]);
            }

            // 记录访问日志
            Log::info('第三方系统获取变更记录具体数据', [
                'user_id' => auth('sanctum')->user()?->id,
                'change_id' => $changeId,
                'table_name' => $change->table_name,
                'change_type' => $change->change_type
            ]);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('获取变更记录数据失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取变更记录数据失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }

    /**
     * 获取增量同步统计信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getIncrementalStats(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'since_time' => 'date_format:Y-m-d H:i:s',
                'until_time' => 'date_format:Y-m-d H:i:s',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sinceTime = $request->input('since_time');
            $untilTime = $request->input('until_time');

            // 使用SyncChangeLog模型获取统计信息
            $stats = SyncChangeLog::getChangeStats($sinceTime, $untilTime);

            // 构建汇总统计
            $summary = [
                'total_changes' => $stats['total_changes'],
                'table_stats' => $stats['table_stats']->toArray(),
                'hourly_stats' => $stats['hourly_stats']->toArray(),
            ];

            // 记录访问日志
            Log::info('第三方系统获取增量统计信息', [
                'user_id' => auth('sanctum')->user()?->id,
                'since_time' => $sinceTime,
                'until_time' => $untilTime,
                'total_changes' => $summary['total_changes']
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'table_stats' => $summary['table_stats'],
                    'hourly_stats' => $summary['hourly_stats'],
                    'query_params' => [
                        'since_time' => $sinceTime,
                        'until_time' => $untilTime
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取增量统计信息失败', [
                'error' => $e->getMessage(),
                'user_id' => auth('sanctum')->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取增量统计信息失败',
                'error' => app()->environment('local') ? $e->getMessage() : '内部服务器错误'
            ], 500);
        }
    }





    /**
     * 格式化记录标识符
     * 
     * @param array $pkData
     * @return string
     */
    private function formatRecordIdentifier(array $pkData): string
    {
        $parts = [];
        foreach ($pkData as $key => $value) {
            $parts[] = "{$key}={$value}";
        }
        return implode(', ', $parts);
    }



    /**
     * 获取本地表的变更记录数据摘要
     * 
     * @param SyncChangeLog $change
     * @return array|null
     */
    private function getDataSummaryForLocalTable(SyncChangeLog $change): ?array
    {
        try {
            $summary = [
                'table_name' => $change->table_name,
                'change_type' => $change->change_type,
            ];

            // 根据变更类型提供不同的摘要信息
            switch (strtolower($change->change_type)) {
                case 'insert':
                    $summary['description'] = "新增记录到 {$change->table_name} 表";
                    break;
                case 'update':
                    $summary['description'] = "更新 {$change->table_name} 表中的记录";
                    break;
                case 'delete':
                    $summary['description'] = "删除 {$change->table_name} 表中的记录";
                    break;
            }

            // 添加主键信息摘要
            if ($change->pk_json) {
                $summary['primary_keys'] = array_keys($change->pk_json);
                $summary['record_identifier'] = $this->formatRecordIdentifier($change->pk_json);
            }

            return $summary;

        } catch (\Exception $e) {
            Log::warning('生成本地表数据摘要失败', [
                'change_id' => $change->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取本地表的变更记录数据
     * 
     * 使用 SELECT * 查询，确保返回表的所有字段信息
     * 包括：业务字段、主键字段、时间戳字段(created_at, updated_at)、软删除字段(deleted_at)等
     * 
     * @param string $tableName MySQL表名（来自sync_change_log）
     * @param array $pkData 主键数据数组
     * @return array|null 返回完整的记录数组，包含所有字段
     */
    private function fetchLocalTableRecordData(string $tableName, array $pkData): ?array
    {
        // 表名映射：sync_change_log中的表名 -> 实际数据库中的表名
        // 现在sync_change_log中的表名已与实际数据库表名保持一致
        $tableMapping = [
            'BOM' => 'BOM',
            'categories' => 'categories',
            'customers' => 'customers', 
            'material_translations' => 'material_translations',
            'materials' => 'materials'
        ];
        
        // 获取实际的数据库表名
        $actualTableName = $tableMapping[$tableName] ?? $tableName;
        
        // 构建WHERE条件，使用MySQL字段名
        $whereConditions = [];
        $bindings = [];
        
        foreach ($pkData as $field => $value) {
            $whereConditions[] = "{$field} = ?";
            $bindings[] = $value;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // 使用实际表名和 SELECT * 获取表的所有字段，确保数据完整性
        $record = DB::connection('mysql')->selectOne(
            "SELECT * FROM `{$actualTableName}` WHERE {$whereClause}",
            $bindings
        );
        
        // 返回完整的记录数组，包含该表的所有字段
        return $record ? (array) $record : null;
    }

    /**
     * 获取表的描述信息
     * 
     * @param string $tableName
     * @return string
     */
    private function getTableDescription(string $tableName): string
    {
        $descriptions = [
            'BOM' => 'BOM结构数据',
            'categories' => '物料分类数据',
            'customers' => '客户数据',
            'material_translations' => '物料翻译数据',
            'materials' => '物料主数据',
        ];

        return $descriptions[$tableName] ?? "表 {$tableName} 的数据";
    }
} 