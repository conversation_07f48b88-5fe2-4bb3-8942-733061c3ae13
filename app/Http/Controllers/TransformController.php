<?php

namespace App\Http\Controllers;

use App\Jobs\TransformDataJob;
use App\Services\DataTransformService;
use App\Services\QueueManagementService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 数据转化控制器
 * 提供数据转化的API接口
 */
class TransformController extends Controller
{
    private DataTransformService $transformService;
    private QueueManagementService $queueService;

    public function __construct(
        DataTransformService $transformService,
        QueueManagementService $queueService
    ) {
        $this->transformService = $transformService;
        $this->queueService = $queueService;
    }

    /**
     * 获取转化统计信息
     */
    public function getStats(Request $request): JsonResponse
    {
        try {
            $companyCode = $request->input('company_code', 'TB');
            $stats = $this->transformService->getTransformStats($companyCode);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'company_code' => $companyCode
            ]);

        } catch (Exception $e) {
            Log::error("获取转化统计失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取转化统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 执行数据转化（同步）
     */
    public function transform(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:category,customer,bom,material',
                'company_code' => 'string|max:10',
                'locale' => 'string|max:10',
                'version' => 'string|max:20'
            ]);

            $type = $validated['type'];
            $companyCode = $validated['company_code'] ?? 'TB';
            $options = array_filter([
                'locale' => $validated['locale'] ?? 'zh_CN',
                'version' => $validated['version'] ?? null
            ]);

            $result = $this->transformService->transform($type, $companyCode, $options);

            return response()->json($result);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);

        } catch (Exception $e) {
            Log::error("数据转化失败", [
                'type' => $request->input('type'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '数据转化失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量数据转化（异步）
     */
    public function batchTransform(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'types' => 'required|array|min:1',
                'types.*' => 'string|in:category,customer,bom,material',
                'company_code' => 'string|max:10',
                'locale' => 'string|max:10',
                'version' => 'string|max:20',
                'async' => 'boolean'
            ]);

            $types = $validated['types'];
            $companyCode = $validated['company_code'] ?? 'TB';
            $options = array_filter([
                'locale' => $validated['locale'] ?? 'zh_CN',
                'version' => $validated['version'] ?? null
            ]);
            $async = filter_var($validated['async'] ?? true, FILTER_VALIDATE_BOOLEAN);

            // 检查队列工作进程（如果是异步执行）
            if ($async) {
                $queueCheck = $this->ensureQueueWorkers($request);
                if (!$queueCheck['success']) {
                    return response()->json([
                        'success' => false,
                        'message' => '队列工作进程启动失败',
                        'error' => $queueCheck['message']
                    ], 500);
                }
            }

            if ($async) {
                // 异步执行
                $jobId = TransformDataJob::dispatch($types, $companyCode, $options);
                
                return response()->json([
                    'success' => true,
                    'message' => '批量转化任务已提交到队列',
                    'job_id' => $jobId,
                    'types' => $types,
                    'company_code' => $companyCode,
                    'async' => true
                ]);
            } else {
                // 同步执行
                $result = $this->transformService->batchTransform($types, $companyCode, $options);
                return response()->json($result);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);

        } catch (Exception $e) {
            Log::error("批量转化失败", [
                'types' => $request->input('types'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量转化失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取支持的转化类型
     */
    public function getTransformTypes(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'type' => 'category',
                    'name' => '分类转化',
                    'description' => '从RTAXL_T表转化分类数据',
                    'source_tables' => ['RTAXL_T']
                ],
                [
                    'type' => 'customer',
                    'name' => '客户转化',
                    'description' => '从PMAB_T和PMAAL_T表转化客户数据',
                    'source_tables' => ['PMAB_T', 'PMAAL_T']
                ],
                [
                    'type' => 'bom',
                    'name' => 'BOM转化',
                    'description' => '从BMBA_T表转化BOM数据',
                    'source_tables' => ['BMBA_T']
                ],
                [
                    'type' => 'material',
                    'name' => '物料转化',
                    'description' => '从IMAA_T、IMAAL_T、IMAF_T、BMAA_T表转化物料数据',
                    'source_tables' => ['IMAA_T', 'IMAAL_T', 'IMAF_T', 'BMAA_T']
                ]
            ]
        ]);
    }

    /**
     * 检查和确保队列工作进程
     */
    private function ensureQueueWorkers(Request $request): array
    {
        try {
            $autoStart = filter_var($request->input('auto_start_queue', true), FILTER_VALIDATE_BOOLEAN);
            $hasWorkers = $this->queueService->hasActiveWorkers();
            $workerCount = $this->queueService->getWorkerCount();

            if (!$hasWorkers || $workerCount === 0) {
                if ($autoStart) {
                    // 尝试自动启动队列工作进程
                    $startResult = $this->queueService->startQueueWorker();
                    if ($startResult['success']) {
                        // 给新进程1秒时间准备
                        usleep(1000000);
                        return [
                            'success' => true,
                            'message' => '队列工作进程已自动启动',
                            'auto_started' => true
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => '队列工作进程自动启动失败: ' . $startResult['message']
                        ];
                    }
                } else {
                    return [
                        'success' => false,
                        'message' => '没有活跃的队列工作进程，请先启动队列工作进程',
                        'manual_start_required' => true,
                        'instructions' => [
                            '手动启动命令:',
                            'php artisan queue:work --daemon',
                            '或使用脚本: ./start_queue.sh'
                        ]
                    ];
                }
            }

            return [
                'success' => true,
                'message' => '队列状态良好',
                'worker_count' => $workerCount
            ];

        } catch (Exception $e) {
            Log::error("队列检查失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '队列检查失败: ' . $e->getMessage()
            ];
        }
    }
} 