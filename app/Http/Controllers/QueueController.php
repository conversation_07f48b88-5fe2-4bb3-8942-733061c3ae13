<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Process;
use App\Models\SyncLog;

class QueueController extends Controller
{
    /**
     * 获取队列状态
     */
    public function getQueueStatus(): JsonResponse
    {
        try {
            // 检查待处理任务
            $pendingJobs = DB::table('jobs')->count();
            
            // 检查失败任务
            $failedJobs = DB::table('failed_jobs')->count();
            
            // 检查正在运行的同步任务
            $runningSyncs = SyncLog::where('status', SyncLog::STATUS_RUNNING)->count();
            
            // 检查队列工作进程
            $queueWorkers = $this->getQueueWorkers();
            
            // 获取最近的队列任务信息
            $recentJobs = DB::table('jobs')
                ->select('queue', 'payload', 'created_at', 'available_at')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($job) {
                    $payload = json_decode($job->payload, true);
                    return [
                        'queue' => $job->queue,
                        'job_name' => $payload['displayName'] ?? 'Unknown',
                        'created_at' => $job->created_at,
                        'available_at' => $job->available_at,
                        'delay' => $job->available_at - time(),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'pending_jobs' => $pendingJobs,
                    'failed_jobs' => $failedJobs,
                    'running_syncs' => $runningSyncs,
                    'queue_workers' => $queueWorkers,
                    'recent_jobs' => $recentJobs,
                    'is_healthy' => $pendingJobs < 10 && count($queueWorkers) > 0,
                    'recommendations' => $this->getRecommendations($pendingJobs, count($queueWorkers), $failedJobs)
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '获取队列状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 启动队列工作进程
     */
    public function startQueueWorker(): JsonResponse
    {
        try {
            // 检查当前是否已有工作进程
            $workers = $this->getQueueWorkers();
            
            if (count($workers) >= 2) {
                return response()->json([
                    'success' => false,
                    'message' => '已有足够的队列工作进程在运行',
                    'workers' => $workers
                ]);
            }

            // 启动新的队列工作进程
            $command = 'nohup php artisan queue:work --timeout=3600 --memory=512 --tries=3 --max-jobs=1000 --max-time=3600 > /dev/null 2>&1 & echo $!';
            
            $result = Process::run($command);
            
            if ($result->successful()) {
                return response()->json([
                    'success' => true,
                    'message' => '队列工作进程启动成功',
                    'pid' => trim($result->output())
                ]);
            } else {
                throw new \Exception('启动命令执行失败: ' . $result->errorOutput());
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '启动队列工作进程失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重启队列工作进程
     */
    public function restartQueueWorkers(): JsonResponse
    {
        try {
            // 发送重启信号给所有队列工作进程
            Artisan::call('queue:restart');
            
            // 等待一下让进程重启
            sleep(2);
            
            // 检查重启后的状态
            $workers = $this->getQueueWorkers();
            
            return response()->json([
                'success' => true,
                'message' => '队列工作进程重启成功',
                'workers' => $workers,
                'artisan_output' => Artisan::output()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '重启队列工作进程失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清理失败的队列任务
     */
    public function clearFailedJobs(): JsonResponse
    {
        try {
            // 清理所有失败的任务
            Artisan::call('queue:flush');
            
            $failedCount = DB::table('failed_jobs')->count();
            
            return response()->json([
                'success' => true,
                'message' => '失败队列任务清理完成',
                'cleared_count' => $failedCount,
                'artisan_output' => Artisan::output()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '清理失败队列任务失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 强制处理待处理任务
     */
    public function processPendingJobs(): JsonResponse
    {
        try {
            $pendingBefore = DB::table('jobs')->count();
            
            if ($pendingBefore === 0) {
                return response()->json([
                    'success' => true,
                    'message' => '没有待处理的队列任务'
                ]);
            }

            // 检查队列工作进程
            $workers = $this->getQueueWorkers();
            
            if (count($workers) === 0) {
                return response()->json([
                    'success' => false,
                    'message' => '没有队列工作进程在运行，请先启动队列工作进程',
                    'pending_jobs' => $pendingBefore
                ]);
            }

            // 等待一段时间让队列工作进程处理任务
            sleep(5);
            
            $pendingAfter = DB::table('jobs')->count();
            $processed = $pendingBefore - $pendingAfter;
            
            return response()->json([
                'success' => true,
                'message' => $processed > 0 ? "已处理 {$processed} 个队列任务" : '队列任务处理中，请稍后查看状态',
                'before' => $pendingBefore,
                'after' => $pendingAfter,
                'processed' => $processed,
                'workers' => count($workers)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '处理待处理任务失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取队列工作进程信息
     */
    private function getQueueWorkers(): array
    {
        try {
            $result = Process::run('ps aux | grep "queue:work" | grep -v grep');
            
            if (!$result->successful()) {
                return [];
            }
            
            $lines = explode("\n", trim($result->output()));
            $workers = [];
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                $parts = preg_split('/\s+/', trim($line));
                if (count($parts) >= 2) {
                    $workers[] = [
                        'user' => $parts[0],
                        'pid' => $parts[1],
                        'cpu' => $parts[2] ?? 'N/A',
                        'memory' => $parts[3] ?? 'N/A',
                        'status' => $parts[7] ?? 'N/A',
                        'start_time' => $parts[8] ?? 'N/A',
                        'runtime' => $parts[9] ?? 'N/A',
                    ];
                }
            }
            
            return $workers;
            
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取队列健康建议
     */
    private function getRecommendations(int $pendingJobs, int $workerCount, int $failedJobs): array
    {
        $recommendations = [];
        
        if ($workerCount === 0) {
            $recommendations[] = [
                'type' => 'critical',
                'message' => '没有队列工作进程在运行，请立即启动队列工作进程',
                'action' => 'start_worker'
            ];
        } elseif ($workerCount === 1 && $pendingJobs > 5) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => '队列任务较多但只有1个工作进程，建议启动更多工作进程',
                'action' => 'start_worker'
            ];
        }
        
        if ($pendingJobs > 10) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => "有 {$pendingJobs} 个待处理任务，可能需要检查队列工作进程状态",
                'action' => 'restart_workers'
            ];
        }
        
        if ($failedJobs > 0) {
            $recommendations[] = [
                'type' => 'info',
                'message' => "有 {$failedJobs} 个失败任务，可以选择清理或重试",
                'action' => 'clear_failed'
            ];
        }
        
        if (empty($recommendations)) {
            $recommendations[] = [
                'type' => 'success',
                'message' => '队列系统运行正常',
                'action' => null
            ];
        }
        
        return $recommendations;
    }
} 