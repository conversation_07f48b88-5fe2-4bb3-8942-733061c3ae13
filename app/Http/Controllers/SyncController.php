<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\DataSyncService;
use App\Models\SyncLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class SyncController extends Controller
{
    private DataSyncService $dataSyncService;

    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * 显示同步监控页面
     */
    public function index(): View
    {
        $stats = $this->dataSyncService->getSyncStats();
        
        // 获取最近的同步记录
        $recentLogs = SyncLog::with([])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        // 计算整体统计
        $totalTables = count(config('sync.tables', []));
        $syncedTables = collect($stats)->filter(fn($stat) => $stat['latest_sync'])->count();
        $runningTasks = SyncLog::where('status', SyncLog::STATUS_RUNNING)->count();
        $failedTasks = SyncLog::where('status', SyncLog::STATUS_FAILED)
            ->where('created_at', '>=', now()->subDay())
            ->count();

        return view('sync.index', compact(
            'stats',
            'recentLogs',
            'totalTables',
            'syncedTables',
            'runningTasks',
            'failedTasks'
        ));
    }

    /**
     * 获取同步统计数据（API）
     */
    public function stats(): JsonResponse
    {
        $stats = $this->dataSyncService->getSyncStats();
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 获取同步日志（API）
     */
    public function logs(Request $request): JsonResponse
    {
        $query = SyncLog::query();

        // 过滤条件
        if ($request->has('table_name') && $request->table_name) {
            $query->where('table_name', $request->table_name);
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('sync_type') && $request->sync_type) {
            $query->where('sync_type', $request->sync_type);
        }

        // 时间范围过滤
        if ($request->has('date_from') && $request->date_from) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $logs = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $logs
        ]);
    }

    /**
     * 手动触发同步（API）
     */
    public function triggerSync(Request $request): JsonResponse
    {
        $request->validate([
            'table_name' => 'nullable|string|max:100',
            'sync_type' => 'required|in:full,incremental'
        ]);

        try {
            $tableName = $request->table_name;
            $syncType = $request->sync_type;

            if ($tableName) {
                // 同步指定表
                $result = $this->dataSyncService->syncTable($tableName, $syncType);
                
                return response()->json([
                    'success' => $result['success'],
                    'message' => $result['success'] ? '同步任务已启动' : '同步任务启动失败',
                    'data' => $result
                ]);
            } else {
                // 同步所有表
                $results = $this->dataSyncService->syncAllTables($syncType);
                $allSuccess = collect($results)->every(fn($result) => $result['success'] ?? false);
                
                return response()->json([
                    'success' => $allSuccess,
                    'message' => $allSuccess ? '所有表同步任务已启动' : '部分表同步任务启动失败',
                    'data' => $results
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '同步任务启动失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取表详情（API）
     */
    public function tableDetail(string $tableName): JsonResponse
    {
        try {
            // 获取表的最新同步记录
            $latestSync = SyncLog::getLatestSync($tableName);
            
            // 获取表的历史记录
            $history = SyncLog::where('table_name', $tableName)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // 获取表的统计信息
            $totalSyncs = SyncLog::where('table_name', $tableName)->count();
            $successfulSyncs = SyncLog::where('table_name', $tableName)
                ->where('status', SyncLog::STATUS_SUCCESS)
                ->count();
            $successRate = $totalSyncs > 0 ? round(($successfulSyncs / $totalSyncs) * 100, 2) : 0;

            $avgProcessed = SyncLog::where('table_name', $tableName)
                ->where('status', SyncLog::STATUS_SUCCESS)
                ->avg('records_processed') ?? 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'table_name' => $tableName,
                    'latest_sync' => $latestSync,
                    'history' => $history,
                    'statistics' => [
                        'total_syncs' => $totalSyncs,
                        'successful_syncs' => $successfulSyncs,
                        'success_rate' => $successRate,
                        'avg_records_processed' => round($avgProcessed)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取表详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取同步配置（API）
     */
    public function config(): JsonResponse
    {
        $config = config('sync');
        
        return response()->json([
            'success' => true,
            'data' => $config
        ]);
    }

    /**
     * 获取实时状态（API）
     */
    public function status(): JsonResponse
    {
        $runningTasks = SyncLog::where('status', SyncLog::STATUS_RUNNING)->count();
        $pendingTasks = SyncLog::where('status', SyncLog::STATUS_PENDING)->count();
        $recentFailures = SyncLog::where('status', SyncLog::STATUS_FAILED)
            ->where('created_at', '>=', now()->subHour())
            ->count();

        $lastActivity = SyncLog::orderBy('created_at', 'desc')->first();

        return response()->json([
            'success' => true,
            'data' => [
                'running_tasks' => $runningTasks,
                'pending_tasks' => $pendingTasks,
                'recent_failures' => $recentFailures,
                'last_activity' => $lastActivity?->created_at,
                'system_status' => $runningTasks > 0 ? 'active' : 'idle'
            ]
        ]);
    }
} 