<?php

namespace App\Providers;

use App\Services\DatabaseService;
use App\Services\DataSyncService;
use App\Services\DataTransformService;
use App\Services\QueueManagementService;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 注册数据库服务
        $this->app->singleton(DatabaseService::class, function ($app) {
            return new DatabaseService();
        });

        // 注册数据同步服务
        $this->app->singleton(DataSyncService::class, function ($app) {
            return new DataSyncService($app->make(DatabaseService::class));
        });

        // 注册数据转化服务
        $this->app->singleton(DataTransformService::class, function ($app) {
            return new DataTransformService($app->make(DatabaseService::class));
        });

        // 注册队列管理服务
        $this->app->singleton(QueueManagementService::class, function ($app) {
            return new QueueManagementService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Vite::prefetch(concurrency: 3);
    }
}
