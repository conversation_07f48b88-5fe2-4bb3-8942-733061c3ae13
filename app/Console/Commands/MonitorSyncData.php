<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MonitorSyncData extends Command
{
    protected $signature = 'monitor:sync-data {action}';
    protected $description = '监控同步数据';

    public function handle()
    {
        $action = $this->argument('action');
        
        try {
            switch ($action) {
                case 'running':
                    $this->showRunningSync();
                    break;
                case 'recent':
                    $this->showRecentSync();
                    break;
                case 'stats':
                    $this->showTodayStats();
                    break;
                case 'tables':
                    $this->showTableStatus();
                    break;
                default:
                    $this->error('Unknown action');
            }
        } catch (\Exception $e) {
            $this->error('数据库连接错误: ' . $e->getMessage());
        }
    }

    private function showRunningSync()
    {
        $running = DB::table('sync_logs')
            ->where('status', 'running')
            ->orderBy('start_time')
            ->get(['table_name', 'sync_type', 'start_time']);

        if ($running->isEmpty()) {
            $this->info('   ✅ 没有正在运行的同步任务');
        } else {
            foreach ($running as $sync) {
                $minutes = now()->diffInMinutes($sync->start_time);
                $this->info("   {$sync->table_name} ({$sync->sync_type}) - 已运行 {$minutes} 分钟");
            }
        }
    }

    private function showRecentSync()
    {
        $recent = DB::table('sync_logs')
            ->whereIn('status', ['completed', 'failed'])
            ->whereDate('created_at', today())
            ->orderBy('end_time', 'desc')
            ->limit(5)
            ->get(['table_name', 'sync_type', 'status', 'records_processed', 'end_time']);

        if ($recent->isEmpty()) {
            $this->info('   ℹ️  今日暂无完成的同步记录');
        } else {
            foreach ($recent as $sync) {
                $time = $sync->end_time ? date('H:i:s', strtotime($sync->end_time)) : '未知';
                $records = number_format($sync->records_processed ?? 0);
                $this->info("   {$time} - {$sync->table_name} ({$sync->sync_type}) - {$records} 条记录 - {$sync->status}");
            }
        }
    }

    private function showTodayStats()
    {
        $stats = DB::table('sync_logs')
            ->whereDate('created_at', today())
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = "running" THEN 1 ELSE 0 END) as running,
                COALESCE(SUM(records_processed), 0) as total_records
            ')
            ->first();

        if ($stats && $stats->total > 0) {
            $this->info("   总同步次数: {$stats->total} | 成功: {$stats->completed} | 失败: {$stats->failed} | 运行中: {$stats->running} | 总处理记录: " . number_format($stats->total_records));
        } else {
            $this->info('   ℹ️  暂无统计数据');
        }
    }

    private function showTableStatus()
    {
        $latestPerTable = DB::table('sync_logs as sl1')
            ->join(DB::raw('(SELECT table_name, MAX(created_at) as max_created_at FROM sync_logs WHERE DATE(created_at) = CURDATE() GROUP BY table_name) as sl2'), function($join) {
                $join->on('sl1.table_name', '=', 'sl2.table_name')
                     ->on('sl1.created_at', '=', 'sl2.max_created_at');
            })
            ->orderBy('sl1.table_name')
            ->get(['sl1.table_name', 'sl1.status', 'sl1.start_time', 'sl1.end_time']);

        if ($latestPerTable->isEmpty()) {
            $this->info('   ℹ️  暂无表状态数据');
        } else {
            $this->info('   表名         | 状态       | 时间/持续时间');
            $this->info('   ----------------------------------------');
            foreach ($latestPerTable as $table) {
                $tableName = str_pad($table->table_name, 12);
                $status = str_pad($table->status, 10);
                
                if ($table->status === 'running') {
                    $minutes = now()->diffInMinutes($table->start_time);
                    $timeInfo = "运行中 {$minutes}min";
                } elseif ($table->end_time) {
                    $timeInfo = date('H:i:s', strtotime($table->end_time));
                } else {
                    $timeInfo = '未知';
                }
                
                $this->info("   {$tableName} | {$status} | {$timeInfo}");
            }
        }
    }
}
