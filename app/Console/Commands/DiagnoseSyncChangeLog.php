<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class DiagnoseSyncChangeLog extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:diagnose-sync-change-log 
                            {--table= : 指定要诊断的表名}
                            {--test-update : 测试更新sync_status字段}
                            {--show-details : 显示详细的记录信息}';

    /**
     * The console command description.
     */
    protected $description = '诊断sync_change_log表的sync_status字段更新问题';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info("🔍 开始诊断sync_change_log表的sync_status字段问题...");

        try {
            $tableName = $this->option('table');
            $testUpdate = $this->option('test-update');
            $showDetails = $this->option('show-details');

            // 检查Oracle连接
            if (!$this->checkOracleConnection()) {
                return Command::FAILURE;
            }

            // 显示sync_change_log表的基本信息
            $this->showTableInfo($tableName);

            // 检查sync_status字段的状态分布
            $this->checkSyncStatusDistribution($tableName);

            // 显示详细记录信息
            if ($showDetails) {
                $this->showDetailedRecords($tableName);
            }

            // 测试更新功能
            if ($testUpdate) {
                $this->testSyncStatusUpdate($tableName);
            }

            // 检查可能的问题
            $this->checkPotentialIssues($tableName);

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error("❌ 诊断失败: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 检查Oracle连接
     */
    private function checkOracleConnection(): bool
    {
        try {
            DB::connection('oracle')->select("SELECT 1 FROM dual");
            $this->info("✅ Oracle数据库连接正常");
            return true;
        } catch (Exception $e) {
            $this->error("❌ Oracle数据库连接失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 显示表基本信息
     */
    private function showTableInfo(?string $tableName): void
    {
        $this->info("\n📊 sync_change_log表基本信息:");

        try {
            // 总记录数
            $totalRecords = DB::connection('oracle')->select("SELECT COUNT(*) as count FROM sync_change_log")[0]->count ?? 0;
            $this->info("  📝 总记录数: {$totalRecords}");

            // 按表分组统计
            $whereClause = $tableName ? "WHERE table_name = ?" : "";
            $bindings = $tableName ? [$tableName] : [];

            $tableStats = DB::connection('oracle')->select("
                SELECT table_name, COUNT(*) as count
                FROM sync_change_log 
                {$whereClause}
                GROUP BY table_name
                ORDER BY count DESC
            ", $bindings);

            if (!empty($tableStats)) {
                $this->info("  📋 按表统计:");
                foreach ($tableStats as $stat) {
                    $this->info("    - {$stat->table_name}: {$stat->count} 条");
                }
            }

        } catch (Exception $e) {
            $this->error("获取表信息失败: " . $e->getMessage());
        }
    }

    /**
     * 检查sync_status字段的状态分布
     */
    private function checkSyncStatusDistribution(?string $tableName): void
    {
        $this->info("\n🔍 sync_status字段状态分布:");

        try {
            $whereClause = $tableName ? "WHERE table_name = ?" : "";
            $bindings = $tableName ? [$tableName] : [];

            $statusStats = DB::connection('oracle')->select("
                SELECT sync_status, COUNT(*) as count
                FROM sync_change_log 
                {$whereClause}
                GROUP BY sync_status
                ORDER BY sync_status
            ", $bindings);

            if (!empty($statusStats)) {
                foreach ($statusStats as $stat) {
                    $statusText = match((int)$stat->sync_status) {
                        0 => '未同步',
                        1 => '已同步',
                        2 => '失败',
                        default => '未知状态'
                    };
                    
                    $icon = match((int)$stat->sync_status) {
                        0 => '⏳',
                        1 => '✅',
                        2 => '❌',
                        default => '❓'
                    };
                    
                    $this->info("  {$icon} {$statusText} (sync_status={$stat->sync_status}): {$stat->count} 条");
                }
            } else {
                $this->info("  📭 没有找到记录");
            }

            // 按操作类型和状态统计
            $operationStats = DB::connection('oracle')->select("
                SELECT operation, sync_status, COUNT(*) as count
                FROM sync_change_log 
                {$whereClause}
                GROUP BY operation, sync_status
                ORDER BY operation, sync_status
            ", $bindings);

            if (!empty($operationStats)) {
                $this->info("\n📈 按操作类型和状态统计:");
                $currentOperation = '';
                foreach ($operationStats as $stat) {
                    if ($currentOperation !== $stat->operation) {
                        $currentOperation = $stat->operation;
                        $operationIcon = match(strtoupper($stat->operation)) {
                            'INSERT' => '➕',
                            'UPDATE' => '✏️',
                            'DELETE' => '🗑️',
                            default => '❓'
                        };
                        $this->info("  {$operationIcon} {$stat->operation}:");
                    }
                    
                    $statusText = match((int)$stat->sync_status) {
                        0 => '未同步',
                        1 => '已同步',
                        2 => '失败',
                        default => '未知'
                    };
                    
                    $this->info("    - {$statusText}: {$stat->count} 条");
                }
            }

        } catch (Exception $e) {
            $this->error("检查状态分布失败: " . $e->getMessage());
        }
    }

    /**
     * 显示详细记录信息
     */
    private function showDetailedRecords(?string $tableName): void
    {
        $this->info("\n📋 详细记录信息 (最近10条):");

        try {
            $whereClause = $tableName ? "WHERE table_name = ?" : "";
            $bindings = $tableName ? [$tableName] : [];

            $records = DB::connection('oracle')->select("
                SELECT id, table_name, operation, pk_json, pk_old_json, 
                       change_time, sync_status
                FROM sync_change_log 
                {$whereClause}
                ORDER BY change_time DESC
                FETCH FIRST 10 ROWS ONLY
            ", $bindings);

            if (!empty($records)) {
                foreach ($records as $record) {
                    $operationIcon = match(strtoupper($record->operation)) {
                        'INSERT' => '➕',
                        'UPDATE' => '✏️',
                        'DELETE' => '🗑️',
                        default => '❓'
                    };
                    
                    $statusIcon = match((int)$record->sync_status) {
                        0 => '⏳',
                        1 => '✅',
                        2 => '❌',
                        default => '❓'
                    };
                    
                    $this->info("  ID {$record->id}: {$operationIcon} {$record->operation} | {$statusIcon} Status={$record->sync_status} | {$record->change_time}");
                    
                    // 显示主键信息
                    $pkData = json_decode($record->pk_json, true);
                    if ($pkData) {
                        $pkStr = implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pkData), $pkData));
                        $this->info("    🔑 主键: {$pkStr}");
                    }
                    
                    // 如果是UPDATE操作且有旧主键，显示旧主键
                    if (strtoupper($record->operation) === 'UPDATE' && !empty($record->pk_old_json)) {
                        $pkOldData = json_decode($record->pk_old_json, true);
                        if ($pkOldData) {
                            $pkOldStr = implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pkOldData), $pkOldData));
                            $this->info("    🔄 旧主键: {$pkOldStr}");
                        }
                    }
                }
            } else {
                $this->info("  📭 没有找到记录");
            }

        } catch (Exception $e) {
            $this->error("显示详细记录失败: " . $e->getMessage());
        }
    }

    /**
     * 测试sync_status字段更新功能
     */
    private function testSyncStatusUpdate(?string $tableName): void
    {
        $this->info("\n🧪 测试sync_status字段更新功能:");

        try {
            // 找一条sync_status=0的记录进行测试
            $whereClause = $tableName ? "AND table_name = ?" : "";
            $bindings = $tableName ? [$tableName] : [];

            $testRecord = DB::connection('oracle')->select("
                SELECT id, table_name, operation, sync_status
                FROM sync_change_log 
                WHERE sync_status = 0 {$whereClause}
                AND ROWNUM = 1
            ", $bindings);

            if (empty($testRecord)) {
                $this->warn("⚠️  没有找到sync_status=0的记录用于测试");
                return;
            }

            $record = $testRecord[0];
            $testId = $record->id;
            
            $this->info("  📋 测试记录: ID={$testId}, Table={$record->table_name}, Operation={$record->operation}");
            $this->info("  📊 当前sync_status: {$record->sync_status}");

            // 测试更新为status=1
            $this->info("  🔄 测试更新sync_status为1...");
            
            $updateResult = DB::connection('oracle')->update(
                "UPDATE sync_change_log SET sync_status = 1 WHERE id = ?",
                [$testId]
            );
            
            $this->info("  📈 更新影响行数: {$updateResult}");

            // 验证更新结果
            $updatedRecord = DB::connection('oracle')->select(
                "SELECT sync_status FROM sync_change_log WHERE id = ?",
                [$testId]
            )[0] ?? null;

            if ($updatedRecord) {
                $this->info("  ✅ 更新后sync_status: {$updatedRecord->sync_status}");
                
                if ((int)$updatedRecord->sync_status === 1) {
                    $this->info("  🎉 sync_status字段更新功能正常！");
                } else {
                    $this->error("  ❌ sync_status字段更新失败，期望值1，实际值{$updatedRecord->sync_status}");
                }
            } else {
                $this->error("  ❌ 无法查询更新后的记录");
            }

            // 恢复原始状态
            $this->info("  🔄 恢复测试记录的原始状态...");
            DB::connection('oracle')->update(
                "UPDATE sync_change_log SET sync_status = 0 WHERE id = ?",
                [$testId]
            );
            $this->info("  ✅ 已恢复测试记录的原始状态");

        } catch (Exception $e) {
            $this->error("测试更新功能失败: " . $e->getMessage());
        }
    }

    /**
     * 检查可能的问题
     */
    private function checkPotentialIssues(?string $tableName): void
    {
        $this->info("\n🔍 检查可能的问题:");

        try {
            // 检查是否有大量未同步记录
            $whereClause = $tableName ? "AND table_name = ?" : "";
            $bindings = $tableName ? [$tableName] : [];

            $pendingCount = DB::connection('oracle')->select("
                SELECT COUNT(*) as count 
                FROM sync_change_log 
                WHERE sync_status = 0 {$whereClause}
            ", $bindings)[0]->count ?? 0;

            if ($pendingCount > 100) {
                $this->warn("⚠️  发现大量未同步记录: {$pendingCount} 条");
                $this->info("💡 建议检查同步进程是否正常运行");
            } else {
                $this->info("✅ 未同步记录数量正常: {$pendingCount} 条");
            }

            // 检查是否有很旧的未同步记录
            $oldPendingRecords = DB::connection('oracle')->select("
                SELECT COUNT(*) as count 
                FROM sync_change_log 
                WHERE sync_status = 0 
                AND change_time < SYSDATE - 1 {$whereClause}
            ", $bindings)[0]->count ?? 0;

            if ($oldPendingRecords > 0) {
                $this->warn("⚠️  发现超过1天的未同步记录: {$oldPendingRecords} 条");
                $this->info("💡 这些记录可能存在同步问题");
            } else {
                $this->info("✅ 没有发现长时间未同步的记录");
            }

            // 检查最近的同步活动
            $recentSyncedCount = DB::connection('oracle')->select("
                SELECT COUNT(*) as count 
                FROM sync_change_log 
                WHERE sync_status = 1 
                AND change_time > SYSDATE - 1 {$whereClause}
            ", $bindings)[0]->count ?? 0;

            $this->info("📊 最近24小时已同步记录: {$recentSyncedCount} 条");

            // 检查Oracle连接和权限
            $this->info("\n🔧 检查Oracle数据库环境:");
            
            // 检查当前用户
            $currentUser = DB::connection('oracle')->select("SELECT USER as username FROM dual")[0]->username ?? 'Unknown';
            $this->info("  👤 当前Oracle用户: {$currentUser}");
            
            // 检查表权限
            $tableExists = DB::connection('oracle')->select("
                SELECT COUNT(*) as count 
                FROM user_tables 
                WHERE table_name = 'SYNC_CHANGE_LOG'
            ")[0]->count ?? 0;
            
            if ($tableExists > 0) {
                $this->info("  ✅ sync_change_log表存在且可访问");
            } else {
                $this->error("  ❌ sync_change_log表不存在或无权限访问");
            }

        } catch (Exception $e) {
            $this->error("检查潜在问题失败: " . $e->getMessage());
        }
    }
} 