<?php

namespace App\Console\Commands;

use App\Services\DataTransformService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TransformData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'transform:data 
                            {type=all : 转化类型 (material|customer|category|bom|all)}
                            {--company-code=TB : 公司代码}
                            {--mode=incremental : 转化模式 (incremental|full)}
                            {--batch-size=1000 : 批量大小}
                            {--locale=zh_CN : 语言代码}
                            {--trigger-table= : 触发表名}
                            {--queue : 使用队列异步执行}
                            {--concurrent : 启用并发转化}
                            {--stats : 显示转化统计信息}
                            {--types : 显示支持的转化类型}
                            {--check-tables : 检查本地同步表状态}';

    /**
     * The console command description.
     */
    protected $description = '执行数据转化，支持增量转化、批量处理、并发执行等功能';

    private DataTransformService $transformService;

    public function __construct(DataTransformService $transformService)
    {
        parent::__construct();
        $this->transformService = $transformService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $type = $this->argument('type');
            $companyCode = $this->option('company-code');
            $mode = $this->option('mode');
            $batchSize = (int) $this->option('batch-size');
            $locale = $this->option('locale');
            $triggerTable = $this->option('trigger-table');
            $useQueue = $this->option('queue');
            $concurrent = $this->option('concurrent');
            $showStats = $this->option('stats');
            $showTypes = $this->option('types');
            $checkTables = $this->option('check-tables');

            // 显示转化统计信息
            if ($showStats) {
                $this->showStats($companyCode);
                return 0;
            }

            // 显示支持的转化类型
            if ($showTypes) {
                $this->showTypes();
                return 0;
            }

            // 检查本地同步表状态
            if ($checkTables) {
                $this->checkTables();
                return 0;
            }

            // 如果没有指定类型，显示帮助
            if (!$type) {
                $this->showHelp();
                return 0;
            }

            $this->info("🚀 开始数据转化");
            $this->info("类型: {$type}");
            $this->info("公司代码: {$companyCode}");
            $this->info("模式: {$mode}");
            $this->info("批量大小: {$batchSize}");
            $this->info("语言: {$locale}");
            $this->info("并发模式: " . ($concurrent ? '是' : '否'));

            // 🔧 新增：并发转化支持
            if ($concurrent) {
                return $this->handleConcurrentTransform($this->transformService, $type, $companyCode, $mode, $batchSize, $locale, $triggerTable, $useQueue);
            }

            // 准备转化选项
            $options = [
                'incremental' => ($mode === 'incremental'),
                'batch_size' => $batchSize,
                'locale' => $locale,
                'trigger_table' => $triggerTable
            ];

            // 确定转化类型
            $types = $type === 'all' ? ['category', 'customer', 'bom', 'material'] : [$type];

            if ($useQueue) {
                return $this->handleQueueTransform($types, $companyCode, $options);
            } else {
                return $this->handleDirectTransform($this->transformService, $types, $companyCode, $options);
            }

        } catch (Exception $e) {
            $this->error("转化失败: " . $e->getMessage());
            Log::error("数据转化命令失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 🔧 新增：处理并发转化
     */
    private function handleConcurrentTransform(
        DataTransformService $transformService,
        string $type,
        string $companyCode,
        string $mode,
        int $batchSize,
        string $locale,
        ?string $triggerTable,
        bool $useQueue
    ): int {
        $this->info("🔄 启用并发转化模式");

        // 准备转化选项
        $options = [
            'incremental' => ($mode === 'incremental'),
            'batch_size' => $batchSize,
            'locale' => $locale,
            'trigger_table' => $triggerTable,
            'concurrent' => true // 启用并发
        ];

        // 确定转化类型
        $types = $type === 'all' ? ['category', 'customer', 'bom', 'material'] : [$type];

        if (count($types) === 1) {
            $this->warn("只有一个转化类型，并发模式无效，改为直接执行");
            return $this->handleDirectTransform($transformService, $types, $companyCode, $options);
        }

        $this->info("并发转化类型: " . implode(', ', $types));

        if ($useQueue) {
            $this->info("使用队列并发执行...");
            return $this->handleQueueTransform($types, $companyCode, $options);
        } else {
            $this->info("使用直接并发执行...");
            $startTime = microtime(true);

            // 使用DataTransformService的并发转化功能
            $result = $transformService->batchTransform($types, $companyCode, $options);

            $duration = round(microtime(true) - $startTime, 2);

            if ($result['success']) {
                $this->info("✅ 并发转化完成，耗时: {$duration}秒");
                $this->displayBatchResult($result);
                return 0;
            } else {
                $this->error("❌ 并发转化失败: " . ($result['error'] ?? '未知错误'));
                return 1;
            }
        }
    }

    /**
     * 单个类型转化
     */
    private function singleTransform(string $type, string $companyCode, array $options): void
    {
        $supportedTypes = ['category', 'customer', 'bom', 'material'];
        
        if (!in_array($type, $supportedTypes)) {
            $this->error("不支持的转化类型: {$type}");
            $this->info("支持的类型: " . implode(', ', $supportedTypes));
            return;
        }

        $this->info("开始执行 {$type} 转化 (公司: {$companyCode})...");
        $this->newLine();

        $startTime = microtime(true);
        $result = $this->transformService->transform($type, $companyCode, $options);
        $endTime = microtime(true);

        $this->displayResult($result, $endTime - $startTime);
    }

    /**
     * 批量转化所有类型
     */
    private function batchTransform(string $companyCode, array $options): void
    {
        $types = ['category', 'customer', 'bom', 'material'];
        
        $this->info("开始执行批量数据转化 (公司: {$companyCode})...");
        $this->info("转化类型: " . implode(', ', $types));
        $this->newLine();

        $startTime = microtime(true);
        $result = $this->transformService->batchTransform($types, $companyCode, $options);
        $endTime = microtime(true);

        $this->displayBatchResult($result, $endTime - $startTime);
    }

    /**
     * 显示单个转化结果
     */
    private function displayResult(array $result, float $duration): void
    {
        if ($result['success']) {
            $this->info("✅ 转化成功");
            $this->table(['项目', '值'], [
                ['类型', $result['type']],
                ['处理记录数', $result['processed'] ?? 0],
                ['错误记录数', $result['errors'] ?? 0],
                ['耗时', round($duration, 2) . ' 秒'],
                ['消息', $result['message'] ?? '']
            ]);
        } else {
            $this->error("❌ 转化失败");
            $this->error("错误: " . ($result['error'] ?? '未知错误'));
        }
    }

    /**
     * 显示批量转化结果
     */
    private function displayBatchResult(array $result, ?float $duration = null): void
    {
        if ($result['success']) {
            $this->info("✅ 批量转化完成");
            $this->newLine();

            // 显示汇总信息
            $summary = $result['summary'];
            $this->table(['项目', '值'], [
                ['转化类型数量', $summary['total_types']],
                ['总处理记录数', $summary['total_processed']],
                ['总错误记录数', $summary['total_errors']],
                ['总耗时', $summary['total_duration'] . ' 秒'],
                ['公司代码', $summary['company_code']]
            ]);

            $this->newLine();

            // 显示各类型详细结果
            $this->info("📊 详细结果:");
            $tableData = [];
            foreach ($result['results'] as $type => $typeResult) {
                $tableData[] = [
                    $type,
                    $typeResult['success'] ? '✅' : '❌',
                    $typeResult['processed'] ?? 0,
                    $typeResult['errors'] ?? 0,
                    isset($typeResult['duration']) ? round($typeResult['duration'], 2) . 's' : '-'
                ];
            }

            $this->table(['类型', '状态', '处理数', '错误数', '耗时'], $tableData);

        } else {
            $this->error("❌ 批量转化失败");
            $this->error("错误: " . ($result['error'] ?? '未知错误'));
        }
    }

    /**
     * 处理直接转化（非队列）
     */
    private function handleDirectTransform(DataTransformService $transformService, array $types, string $companyCode, array $options): int
    {
        if (count($types) === 1) {
            // 单个类型转化
            $type = $types[0];
            $this->info("开始执行 {$type} 转化 (公司: {$companyCode})...");
            $this->newLine();

            $startTime = microtime(true);
            $result = $transformService->transform($type, $companyCode, $options);
            $endTime = microtime(true);

            $this->displayResult($result, $endTime - $startTime);
            return $result['success'] ? 0 : 1;
        } else {
            // 批量转化
            $this->info("开始执行批量数据转化 (公司: {$companyCode})...");
            $this->info("转化类型: " . implode(', ', $types));
            $this->newLine();

            $startTime = microtime(true);
            $result = $transformService->batchTransform($types, $companyCode, $options);
            $endTime = microtime(true);

            $this->displayBatchResult($result, $endTime - $startTime);
            return $result['success'] ? 0 : 1;
        }
    }

    /**
     * 处理队列转化
     */
    private function handleQueueTransform(array $types, string $companyCode, array $options): int
    {
        $this->info("🔄 使用队列执行转化任务...");
        
        foreach ($types as $type) {
            $this->info("正在排队转化任务: {$type}");
            
            // 创建队列任务
            $job = new \App\Jobs\TransformDataJob([$type], $companyCode, $options);
            dispatch($job)->onQueue('transform');
        }

        $this->info("✅ 已将 " . count($types) . " 个转化任务加入队列");
        $this->info("请使用 'php artisan queue:work transform' 处理任务");
        
        return 0;
    }

    /**
     * 显示转化统计信息
     */
    private function showStats(string $companyCode): void
    {
        $this->info("📊 数据转化统计信息 (公司: {$companyCode})");
        $this->newLine();

        $stats = $this->transformService->getTransformStats($companyCode);

        if (empty($stats)) {
            $this->warn("无法获取统计信息");
            return;
        }

        $tableData = [];
        foreach ($stats as $table => $count) {
            $tableData[] = [ucfirst($table), number_format($count)];
        }

        $this->table(['数据类型', '记录数量'], $tableData);
    }

    /**
     * 显示支持的转化类型
     */
    private function showTypes(): void
    {
        $this->info("🔧 支持的数据转化类型:");
        $this->newLine();

        $types = [
            ['category', '分类转化', 'RTAXL_T'],
            ['customer', '客户转化', 'PMAB_T, PMAAL_T'],
            ['bom', 'BOM转化', 'BMBA_T'],
            ['material', '物料转化', 'IMAA_T, IMAAL_T, IMAF_T, BMAA_T'],
            ['all', '全部转化', '上述所有表']
        ];

        $this->table(['类型', '说明', '源表'], $types);
    }

    /**
     * 显示帮助信息
     */
    private function showHelp(): void
    {
        $this->info("🔧 数据转化工具");
        $this->newLine();
        
        $this->info("用法示例:");
        $this->info("  php artisan transform:data category              # 转化分类数据");
        $this->info("  php artisan transform:data customer             # 转化客户数据");
        $this->info("  php artisan transform:data bom                  # 转化BOM数据");
        $this->info("  php artisan transform:data material             # 转化物料数据");
        $this->info("  php artisan transform:data all                  # 转化所有数据");
        $this->info("  php artisan transform:data --stats              # 显示统计信息");
        $this->info("  php artisan transform:data --types              # 显示支持的类型");
        $this->newLine();
        
        $this->info("选项:");
        $this->info("  --company-code=TB    指定公司代码");
        $this->info("  --locale=zh_CN       指定语言代码");
        $this->info("  --mode=incremental   指定转化模式");
        $this->info("  --batch-size=1000    指定批量大小");
        $this->info("  --trigger-table=      指定触发表名");
        $this->info("  --queue              使用队列异步执行");
        $this->info("  --concurrent         启用并发转化");
        $this->info("  --check-tables       检查本地同步表状态");
    }

    /**
     * 检查本地同步表状态
     */
    private function checkTables(): void
    {
        $this->info("🔍 检查本地同步表状态...");
        $this->newLine();

        $result = $this->transformService->checkLocalSyncTables();

        if ($result['success']) {
            $this->info("📊 本地同步表状态检查结果:");
            $this->newLine();

            // 汇总信息
            $this->table(['项目', '值'], [
                ['总表数', $result['total_tables']],
                ['可用表数', $result['available_tables']],
                ['检查时间', $result['check_time']]
            ]);

            $this->newLine();

            // 详细表状态
            $tableData = [];
            foreach ($result['tables'] as $tableName => $tableInfo) {
                $status = match($tableInfo['status']) {
                    'available' => '✅ 可用',
                    'empty' => '⚠️  空表',
                    'not_exist' => '❌ 不存在',
                    default => '❓ 未知'
                };

                $tableData[] = [
                    $tableName,
                    $tableInfo['description'],
                    $status,
                    number_format($tableInfo['record_count']),
                    $tableInfo['error'] ?? '-'
                ];
            }

            $this->table(['表名', '描述', '状态', '记录数', '错误信息'], $tableData);

            // 状态提醒
            $availableCount = $result['available_tables'];
            $totalCount = $result['total_tables'];

            if ($availableCount === $totalCount) {
                $this->info("✅ 所有同步表都可用，可以进行数据转化");
            } elseif ($availableCount > 0) {
                $this->warn("⚠️  部分同步表不可用，可能影响某些转化功能");
            } else {
                $this->error("❌ 没有可用的同步表，请先执行数据同步");
                $this->info("💡 提示：使用 php artisan oracle:sync 命令进行数据同步");
            }

        } else {
            $this->error("❌ 检查本地同步表失败");
        }
    }
} 