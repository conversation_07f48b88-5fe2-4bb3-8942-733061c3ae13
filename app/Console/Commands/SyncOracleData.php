<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\DataSyncService;
use App\Models\SyncLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class SyncOracleData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:sync 
                            {--table= : 指定要同步的表名，不指定则同步所有表}
                            {--type=incremental : 同步类型：full（全量）或 incremental（增量）}
                            {--force : 强制执行同步，即使上次同步失败}';

    /**
     * The console command description.
     */
    protected $description = '从Oracle数据库同步数据到本地MySQL数据库';

    private DataSyncService $dataSyncService;

    public function __construct(DataSyncService $dataSyncService)
    {
        parent::__construct();
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tableName = $this->option('table');
        $syncType = $this->option('type');
        $force = $this->option('force');

        // 添加分布式锁机制防止重复执行
        $lockKey = $this->generateLockKey($tableName, $syncType);
        
        if (!$force && Cache::has($lockKey)) {
            $this->warn("检测到同步任务正在运行中，跳过本次执行");
            $this->info("如需强制执行，请使用 --force 选项");
            Log::warning("同步任务重复执行检测", [
                'table' => $tableName,
                'sync_type' => $syncType,
                'lock_key' => $lockKey,
                'time' => now()->toDateTimeString()
            ]);
            return Command::SUCCESS; // 返回成功状态，避免调度器认为任务失败
        }

        // 设置执行锁，有效期5分钟
        Cache::put($lockKey, [
            'started_at' => now()->toDateTimeString(),
            'table' => $tableName,
            'sync_type' => $syncType,
            'pid' => getmypid()
        ], 300);

        // 验证同步类型
        if (!in_array($syncType, [SyncLog::TYPE_FULL, SyncLog::TYPE_INCREMENTAL])) {
            Cache::forget($lockKey); // 清理锁
            $this->error("无效的同步类型: {$syncType}");
            $this->info("支持的同步类型: full, incremental");
            return Command::FAILURE;
        }

        $this->info("开始数据同步...");
        $this->info("同步类型: " . ($syncType === SyncLog::TYPE_FULL ? '全量同步' : '增量同步'));

        try {
            if ($tableName) {
                // 同步指定表
                $this->info("同步表: {$tableName}");
                
                // 检查是否有正在运行的同步任务
                if (!$force && $this->hasRunningSyncTask($tableName)) {
                    Cache::forget($lockKey); // 清理锁
                    $this->warn("表 {$tableName} 有正在运行的同步任务，使用 --force 选项强制执行");
                    return Command::FAILURE;
                }

                $result = $this->dataSyncService->syncTable($tableName, $syncType);
                $this->displaySyncResult($tableName, $result);
                
                Cache::forget($lockKey); // 执行完成后清理锁
                return $result['success'] ? Command::SUCCESS : Command::FAILURE;
            } else {
                // 同步所有表
                $this->info("同步所有表");
                
                // 检查是否有正在运行的同步任务
                if (!$force && $this->hasRunningGlobalSyncTask()) {
                    Cache::forget($lockKey); // 清理锁
                    $this->warn("有正在运行的全局同步任务，使用 --force 选项强制执行");
                    return Command::FAILURE;
                }

                $results = $this->dataSyncService->syncAllTables($syncType);
                $this->displayAllSyncResults($results);
                
                // 如果所有表都同步成功，返回成功状态
                $allSuccess = collect($results)->every(fn($result) => $result['success'] ?? false);
                Cache::forget($lockKey); // 执行完成后清理锁
                return $allSuccess ? Command::SUCCESS : Command::FAILURE;
            }

        } catch (Exception $e) {
            Cache::forget($lockKey); // 异常时清理锁
            $this->error("同步过程中发生错误: " . $e->getMessage());
            Log::error("数据同步命令执行失败", [
                'table' => $tableName,
                'sync_type' => $syncType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }

    /**
     * 生成同步锁的Key
     */
    private function generateLockKey(?string $tableName, string $syncType): string
    {
        $currentMinute = date('Y-m-d-H-i');
        if ($tableName) {
            return "oracle_sync_lock_{$tableName}_{$syncType}_{$currentMinute}";
        } else {
            return "oracle_sync_lock_all_{$syncType}_{$currentMinute}";
        }
    }

    /**
     * 检查是否有正在运行的同步任务
     */
    private function hasRunningSyncTask(string $tableName): bool
    {
        return SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_RUNNING)
            ->exists();
    }

    /**
     * 检查是否有正在运行的全局同步任务
     */
    private function hasRunningGlobalSyncTask(): bool
    {
        return SyncLog::where('status', SyncLog::STATUS_RUNNING)
            ->exists();
    }

    /**
     * 显示单个表的同步结果
     */
    private function displaySyncResult(string $tableName, array $result): void
    {
        if ($result['success']) {
            $this->info("✅ 表 {$tableName} 同步成功");
            $this->table(['指标', '数量'], [
                ['处理记录数', $result['records_processed'] ?? 0],
                ['新增记录数', $result['records_inserted'] ?? 0],
                ['更新记录数', $result['records_updated'] ?? 0],
                ['删除记录数', $result['records_deleted'] ?? 0],
            ]);
            
            if (isset($result['message'])) {
                $this->info("消息: " . $result['message']);
            }
        } else {
            $this->error("❌ 表 {$tableName} 同步失败");
            if (isset($result['error'])) {
                $this->error("错误: " . $result['error']);
            }
        }
    }

    /**
     * 显示所有表的同步结果
     */
    private function displayAllSyncResults(array $results): void
    {
        $tableData = [];
        $totalProcessed = 0;
        $totalInserted = 0;
        $totalUpdated = 0;
        $successCount = 0;

        foreach ($results as $tableName => $result) {
            $status = $result['success'] ? '✅ 成功' : '❌ 失败';
            $processed = $result['records_processed'] ?? 0;
            $inserted = $result['records_inserted'] ?? 0;
            $updated = $result['records_updated'] ?? 0;
            $error = $result['error'] ?? '';

            $tableData[] = [
                $tableName,
                $status,
                $processed,
                $inserted,
                $updated,
                $error ? substr($error, 0, 50) . '...' : ''
            ];

            if ($result['success']) {
                $successCount++;
                $totalProcessed += $processed;
                $totalInserted += $inserted;
                $totalUpdated += $updated;
            }
        }

        $this->table([
            '表名',
            '状态',
            '处理记录数',
            '新增记录数',
            '更新记录数',
            '错误信息'
        ], $tableData);

        // 显示汇总信息
        $this->info("\n📊 同步汇总:");
        $this->info("成功表数: {$successCount}/" . count($results));
        $this->info("总处理记录数: {$totalProcessed}");
        $this->info("总新增记录数: {$totalInserted}");
        $this->info("总更新记录数: {$totalUpdated}");

        if ($successCount === count($results)) {
            $this->info("🎉 所有表同步完成！");
        } else {
            $this->warn("⚠️  部分表同步失败，请检查错误日志");
        }
    }
} 