<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckOracleEnvironment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'oracle:check-env';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查Oracle环境配置';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 检查Oracle环境配置...');
        $this->newLine();

        // 检查PHP扩展
        $this->checkPhpExtensions();
        
        // 检查环境变量
        $this->checkEnvironmentVariables();
        
        // 检查Oracle客户端库
        $this->checkOracleClient();
        
        // 检查配置文件
        $this->checkConfiguration();
        
        $this->newLine();
        $this->info('✅ Oracle环境检查完成');
        
        return Command::SUCCESS;
    }
    
    /**
     * 检查PHP扩展
     */
    private function checkPhpExtensions(): void
    {
        $this->info('1. PHP扩展检查:');
        
        $extensions = ['oci8', 'pdo', 'pdo_oci'];
        
        foreach ($extensions as $extension) {
            if (extension_loaded($extension)) {
                $this->info("   ✅ {$extension} - 已安装");
            } else {
                $this->warn("   ⚠️  {$extension} - 未安装");
            }
        }
        
        $this->newLine();
    }
    
    /**
     * 检查环境变量
     */
    private function checkEnvironmentVariables(): void
    {
        $this->info('2. 环境变量检查:');
        
        $envVars = [
            'ORACLE_HOME',
            'LD_LIBRARY_PATH',
            'TNS_ADMIN',
            'NLS_LANG'
        ];
        
        foreach ($envVars as $var) {
            $value = getenv($var);
            if ($value !== false && !empty($value)) {
                $this->info("   ✅ {$var} = {$value}");
            } else {
                $this->warn("   ⚠️  {$var} - 未设置");
            }
        }
        
        $this->newLine();
    }
    
    /**
     * 检查Oracle客户端库
     */
    private function checkOracleClient(): void
    {
        $this->info('3. Oracle客户端库检查:');
        
        // 检查函数是否存在
        if (function_exists('oci_connect')) {
            $this->info('   ✅ oci_connect 函数可用');
            
            // 尝试获取版本信息（使用子进程避免段错误）
            $this->info('   正在检查客户端版本...');
            
            $command = 'php -r "try { echo oci_client_version(); } catch (Exception \$e) { echo \'ERROR: \' . \$e->getMessage(); }"';
            $output = shell_exec($command);
            
            if ($output && !str_contains($output, 'ERROR')) {
                $this->info("   ✅ Oracle客户端版本: {$output}");
            } else {
                $this->error('   ❌ Oracle客户端库存在问题: ' . ($output ?: '无法获取版本信息'));
                $this->warn('   建议: 重新安装Oracle Instant Client');
            }
        } else {
            $this->error('   ❌ oci_connect 函数不可用');
        }
        
        $this->newLine();
    }
    
    /**
     * 检查配置文件
     */
    private function checkConfiguration(): void
    {
        $this->info('4. 数据库配置检查:');
        
        $config = config('database.connections.oracle');
        
        if ($config) {
            $this->info('   ✅ Oracle连接配置存在');
            $this->info('   主机: ' . ($config['host'] ?? 'N/A'));
            $this->info('   端口: ' . ($config['port'] ?? 'N/A'));
            $this->info('   数据库: ' . ($config['database'] ?? 'N/A'));
            $this->info('   用户名: ' . ($config['username'] ?? 'N/A'));
            
            // 检查环境变量
            $envVars = [
                'DB_HOST_ORACLE',
                'DB_PORT_ORACLE',
                'DB_DATABASE_ORACLE',
                'DB_USERNAME_ORACLE',
                'DB_PASSWORD_ORACLE'
            ];
            
            $this->newLine();
            $this->info('   环境变量配置:');
            foreach ($envVars as $var) {
                $value = env($var);
                if ($value !== null) {
                    $displayValue = str_contains($var, 'PASSWORD') ? '***' : $value;
                    $this->info("   ✅ {$var} = {$displayValue}");
                } else {
                    $this->warn("   ⚠️  {$var} - 未设置");
                }
            }
        } else {
            $this->error('   ❌ Oracle连接配置不存在');
        }
        
        $this->newLine();
    }
} 