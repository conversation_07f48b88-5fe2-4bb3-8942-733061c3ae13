<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CreateOracleSyncChangeLogTable extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:create-sync-change-log-table 
                            {--force : 强制重新创建表（删除现有表）}
                            {--update : 仅更新表结构（添加缺失字段）}';

    /**
     * The console command description.
     */
    protected $description = '创建或更新Oracle数据库中的sync_change_log表';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info("🔧 开始处理Oracle sync_change_log表...");

        try {
            $force = $this->option('force');
            $update = $this->option('update');

            // 检查表是否存在
            $tableExists = $this->checkTableExists();

            if ($tableExists && $force) {
                $this->info("⚠️  强制模式：删除现有表...");
                $this->dropTable();
                $tableExists = false;
            }

            if ($tableExists && $update) {
                $this->info("🔄 更新模式：检查并添加缺失字段...");
                return $this->updateTableStructure();
            }

            if ($tableExists && !$force && !$update) {
                $this->info("ℹ️  sync_change_log表已存在");
                $this->info("💡 使用 --update 更新表结构或 --force 重新创建表");
                return Command::SUCCESS;
            }

            if (!$tableExists) {
                $this->info("📋 创建sync_change_log表...");
                $this->createTable();
            }

            $this->info("✅ sync_change_log表处理完成");
            $this->displayTableInfo();

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error("❌ 处理失败: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 检查表是否存在
     */
    private function checkTableExists(): bool
    {
        try {
            $tables = DB::connection('oracle')->select("
                SELECT table_name FROM user_tables 
                WHERE table_name = 'SYNC_CHANGE_LOG'
            ");

            return !empty($tables);

        } catch (Exception $e) {
            $this->error("检查表存在性失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 删除表
     */
    private function dropTable(): void
    {
        try {
            DB::connection('oracle')->statement("DROP TABLE sync_change_log");
            $this->info("✅ 已删除现有表");

        } catch (Exception $e) {
            $this->error("删除表失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建表
     */
    private function createTable(): void
    {
        try {
            $sql = "
                CREATE TABLE sync_change_log (
                    id            NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
                    table_name    VARCHAR2(64),
                    operation     VARCHAR2(10),
                    pk_json       CLOB,                  -- JSON 格式存储联合主键
                    pk_old_json   CLOB,                  -- JSON 格式存储联合主键(数据变动前)
                    change_time   TIMESTAMP DEFAULT SYSTIMESTAMP,
                    sync_status   NUMBER DEFAULT 0       -- 0=未同步, 1=已同步, 2=失败
                )
            ";

            DB::connection('oracle')->statement($sql);
            $this->info("✅ 表创建成功");

            // 创建索引
            $this->createIndexes();

        } catch (Exception $e) {
            $this->error("创建表失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 更新表结构
     */
    private function updateTableStructure(): int
    {
        try {
            // 获取当前表结构
            $columns = DB::connection('oracle')->select("
                SELECT column_name, data_type, data_length
                FROM user_tab_columns 
                WHERE table_name = 'SYNC_CHANGE_LOG'
                ORDER BY column_id
            ");

            $existingColumns = array_column($columns, 'COLUMN_NAME');
            $this->info("📋 当前字段: " . implode(', ', $existingColumns));

            $requiredColumns = [
                'ID' => 'NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY',
                'TABLE_NAME' => 'VARCHAR2(64)',
                'OPERATION' => 'VARCHAR2(10)',
                'PK_JSON' => 'CLOB',
                'PK_OLD_JSON' => 'CLOB',
                'CHANGE_TIME' => 'TIMESTAMP DEFAULT SYSTIMESTAMP',
                'SYNC_STATUS' => 'NUMBER DEFAULT 0'
            ];

            $columnsAdded = false;

            foreach ($requiredColumns as $columnName => $columnDefinition) {
                if (!in_array($columnName, $existingColumns)) {
                    $this->info("🔧 添加缺失字段: {$columnName}");
                    
                    // 为新字段添加特殊处理
                    if ($columnName === 'PK_OLD_JSON') {
                        // PK_OLD_JSON字段可以为NULL，不设置默认值
                        DB::connection('oracle')->statement("ALTER TABLE sync_change_log ADD ({$columnName} {$columnDefinition})");
                    } else {
                        DB::connection('oracle')->statement("ALTER TABLE sync_change_log ADD ({$columnName} {$columnDefinition})");
                    }
                    
                    $columnsAdded = true;
                }
            }

            if ($columnsAdded) {
                $this->info("✅ 表结构更新完成");
                
                // 创建缺失的索引
                $this->createIndexes();
                
                return Command::SUCCESS;
            } else {
                $this->info("ℹ️  表结构已是最新，无需更新");
                return Command::SUCCESS;
            }

        } catch (Exception $e) {
            $this->error("更新表结构失败: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 创建索引
     */
    private function createIndexes(): void
    {
        try {
            $indexes = [
                'idx_scl_table_status' => 'CREATE INDEX idx_scl_table_status ON sync_change_log (table_name, sync_status)',
                'idx_scl_change_time' => 'CREATE INDEX idx_scl_change_time ON sync_change_log (change_time)',
                'idx_scl_table_time' => 'CREATE INDEX idx_scl_table_time ON sync_change_log (table_name, change_time)'
            ];

            foreach ($indexes as $indexName => $indexSql) {
                try {
                    DB::connection('oracle')->statement($indexSql);
                    $this->info("✅ 创建索引: {$indexName}");
                } catch (Exception $e) {
                    // 索引可能已存在，忽略错误
                    if (strpos($e->getMessage(), 'name is already used') !== false) {
                        $this->info("ℹ️  索引已存在: {$indexName}");
                    } else {
                        $this->warn("⚠️  创建索引失败: {$indexName} - " . $e->getMessage());
                    }
                }
            }

        } catch (Exception $e) {
            $this->warn("⚠️  创建索引时出现问题: " . $e->getMessage());
        }
    }

    /**
     * 显示表信息
     */
    private function displayTableInfo(): void
    {
        try {
            // 显示表结构
            $columns = DB::connection('oracle')->select("
                SELECT column_name, data_type, data_length, nullable, data_default
                FROM user_tab_columns 
                WHERE table_name = 'SYNC_CHANGE_LOG'
                ORDER BY column_id
            ");

            $this->info("\n📋 表结构信息:");
            $this->table(
                ['字段名', '数据类型', '长度', '允许空值', '默认值'],
                array_map(function ($column) {
                    return [
                        $column->column_name,
                        $column->data_type,
                        $column->data_length ?? '-',
                        $column->nullable === 'Y' ? '是' : '否',
                        $column->data_default ?? '-'
                    ];
                }, $columns)
            );

            // 显示记录统计
            $totalRecords = DB::connection('oracle')->select("SELECT COUNT(*) as count FROM sync_change_log")[0]->count ?? 0;
            $pendingRecords = DB::connection('oracle')->select("SELECT COUNT(*) as count FROM sync_change_log WHERE sync_status = 0")[0]->count ?? 0;
            
            $this->info("\n📊 数据统计:");
            $this->info("  📝 总记录数: {$totalRecords}");
            $this->info("  ⏳ 待同步记录: {$pendingRecords}");

            if ($pendingRecords > 0) {
                // 显示按表分组的待同步记录
                $tableStats = DB::connection('oracle')->select("
                    SELECT table_name, operation, COUNT(*) as count
                    FROM sync_change_log 
                    WHERE sync_status = 0
                    GROUP BY table_name, operation
                    ORDER BY table_name, operation
                ");

                if (!empty($tableStats)) {
                    $this->info("\n📈 待同步记录按表统计:");
                    foreach ($tableStats as $stat) {
                        $operationIcon = match(strtoupper($stat->operation)) {
                            'INSERT' => '➕',
                            'UPDATE' => '✏️',
                            'DELETE' => '🗑️',
                            default => '❓'
                        };
                        $this->info("  {$operationIcon} {$stat->table_name}: {$stat->operation} = {$stat->count} 条");
                    }
                }
            }

        } catch (Exception $e) {
            $this->warn("⚠️  获取表信息失败: " . $e->getMessage());
        }
    }
} 