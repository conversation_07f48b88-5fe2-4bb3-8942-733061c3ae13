<?php

namespace App\Console\Commands;

use App\Services\DataTransformService;
use App\Jobs\TransformDataJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Exception;

class TestTransformOptimization extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:transform-optimization 
                            {type=material : 转化类型 (material|customer|category|bom|all)}
                            {--company-code=TB : 公司代码}
                            {--mode=incremental : 转化模式 (incremental|full)}
                            {--batch-size=500 : 批量大小}
                            {--test-deduplication : 测试任务去重功能}
                            {--clear-cache : 清理缓存}
                            {--dry-run : 仅模拟，不实际执行}
                            {--concurrent : 并发转化测试}';

    /**
     * The console command description.
     */
    protected $description = '测试优化后的数据转化功能，包括增量转化、任务去重、分批处理等';

    /**
     * Execute the console command.
     */
    public function handle(DataTransformService $transformService): int
    {
        try {
            $type = $this->argument('type');
            $companyCode = $this->option('company-code');
            $mode = $this->option('mode');
            $batchSize = (int) $this->option('batch-size');
            $testDeduplication = $this->option('test-deduplication');
            $dryRun = $this->option('dry-run');
            $concurrent = $this->option('concurrent');

            $this->info("🚀 开始转化优化测试");
            $this->info("类型: {$type}");
            $this->info("公司代码: {$companyCode}");
            $this->info("模式: {$mode}");
            $this->info("批量大小: {$batchSize}");
            $this->info("并发模式: " . ($concurrent ? '是' : '否'));

            if ($concurrent) {
                return $this->testConcurrentTransform($transformService, $type, $companyCode, $mode, $batchSize, $dryRun);
            }

            if ($testDeduplication) {
                return $this->testTaskDeduplication($transformService, $type, $companyCode);
            }

            if ($dryRun) {
                return $this->simulateTransform($transformService, $type, $companyCode, $mode, $batchSize);
            }

            return $this->performTransform($transformService, $type, $companyCode, $mode, $batchSize);

        } catch (Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 执行转化测试
     */
    private function executeTransformTest(DataTransformService $transformService, string $type, string $companyCode, string $mode, int $batchSize): int
    {
        try {
            $this->info("📊 检查当前数据状态...");
            $this->displayCurrentStats($companyCode);

            $types = $type === 'all' ? ['material', 'customer', 'category', 'bom'] : [$type];
            
            foreach ($types as $transformType) {
                $this->info("\n🔄 测试 {$transformType} 转化...");
                
                $options = [
                    'incremental' => ($mode === 'incremental'),
                    'batch_size' => $batchSize,
                    'trigger_table' => $this->getTriggerTable($transformType),
                    'locale' => 'zh_CN'
                ];

                $startTime = microtime(true);
                $result = $transformService->transform($transformType, $companyCode, $options);
                $duration = round(microtime(true) - $startTime, 2);

                if ($result['success']) {
                    $this->info("✅ {$transformType} 转化成功");
                    $this->displayTransformResult($result, $duration);
                } else {
                    $this->error("❌ {$transformType} 转化失败: " . ($result['error'] ?? '未知错误'));
                }
            }

            $this->info("\n📊 转化后数据状态:");
            $this->displayCurrentStats($companyCode);

            return 0;

        } catch (\Exception $e) {
            $this->error("测试执行失败: " . $e->getMessage());
            Log::error("转化测试失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 测试任务去重功能
     */
    private function testDeduplicationFeature(string $type, string $companyCode): int
    {
        $this->info("🔍 测试任务去重功能...");

        try {
            $options = [
                'incremental' => true,
                'trigger_table' => $this->getTriggerTable($type),
                'locale' => 'zh_CN'
            ];

            // 快速连续派发3个相同任务
            for ($i = 1; $i <= 3; $i++) {
                $this->info("派发第 {$i} 个任务...");
                TransformDataJob::dispatch([$type], $companyCode, $options)
                    ->onQueue('transform');
            }

            $this->info("✅ 已派发3个相同任务到队列");
            $this->info("📝 请检查日志确认去重功能是否正常工作");

            // 检查队列中的任务数量
            $jobCount = DB::table('jobs')
                ->where('queue', 'transform')
                ->where('payload', 'LIKE', '%' . $type . '%')
                ->count();

            $this->info("📊 队列中相关任务数量: {$jobCount}");

            return 0;

        } catch (\Exception $e) {
            $this->error("去重测试失败: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 模拟转化过程
     */
    private function simulateTransform(DataTransformService $transformService, string $type, string $companyCode, string $mode, int $batchSize): int
    {
        $this->info("🔍 模拟转化过程...");

        try {
            // 检查源数据量
            $sourceCount = $this->getSourceDataCount($type);
            $this->info("📊 源数据量: {$sourceCount} 条");

            if ($mode === 'incremental') {
                $triggerTable = $this->getTriggerTable($type);
                $recentChanges = $this->getRecentChangesCount($triggerTable);
                $this->info("📊 最近10分钟变更: {$recentChanges} 条");
                
                if ($recentChanges === 0) {
                    $this->info("⏭️  增量模式：无最近变更，将跳过转化");
                    return 0;
                }
            }

            $estimatedBatches = ceil($sourceCount / $batchSize);
            $estimatedTime = $estimatedBatches * 2; // 假设每批2秒

            $this->info("📊 预估批次数: {$estimatedBatches}");
            $this->info("📊 预估耗时: {$estimatedTime} 秒");

            return 0;

        } catch (\Exception $e) {
            $this->error("模拟失败: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 显示当前数据统计
     */
    private function displayCurrentStats(string $companyCode): void
    {
        try {
            $stats = [
                'materials' => DB::table('materials')->where('company_code', $companyCode)->count(),
                'customers' => DB::table('customers')->where('company_code', $companyCode)->count(),
                'categories' => DB::table('categories')->where('company_code', $companyCode)->count(),
            ];

            $this->table(['表名', '记录数'], [
                ['materials', $stats['materials']],
                ['customers', $stats['customers']],
                ['categories', $stats['categories']],
            ]);

        } catch (\Exception $e) {
            $this->warn("无法获取统计信息: " . $e->getMessage());
        }
    }

    /**
     * 显示转化结果
     */
    private function displayTransformResult(array $result, float $duration): void
    {
        $this->table(['指标', '数值'], [
            ['处理记录数', $result['processed'] ?? 0],
            ['新建记录数', $result['created'] ?? 0],
            ['更新记录数', $result['updated'] ?? 0],
            ['跳过记录数', $result['skipped'] ?? 0],
            ['错误记录数', $result['errors'] ?? 0],
            ['执行时间', $duration . ' 秒'],
            ['转化模式', $result['mode'] ?? 'unknown'],
        ]);
    }

    /**
     * 获取源数据数量
     */
    private function getSourceDataCount(string $type): int
    {
        $tableMappings = [
            'material' => 'IMAA_T',
            'customer' => 'PMAB_T',
            'category' => 'RTAXL_T',
            'bom' => 'BMBA_T'
        ];

        $table = $tableMappings[$type] ?? null;
        if (!$table) {
            return 0;
        }

        try {
            return DB::table($table)->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 获取最近变更数量
     */
    private function getRecentChangesCount(string $tableName): int
    {
        try {
            $cutoffTime = now()->subMinutes(10);
            return DB::connection('oracle')
                ->table('sync_change_log')
                ->where('table_name', $tableName)
                ->where('sync_status', 1)
                ->where('change_time', '>=', $cutoffTime)
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 获取触发表名
     */
    private function getTriggerTable(string $type): string
    {
        $mappings = [
            'material' => 'IMAA_T',
            'customer' => 'PMAB_T',
            'category' => 'RTAXL_T',
            'bom' => 'BMBA_T'
        ];

        return $mappings[$type] ?? 'UNKNOWN';
    }

    /**
     * 清理转化相关缓存
     */
    private function clearTransformCaches(): void
    {
        $this->info("🧹 清理转化相关缓存...");

        try {
            $types = ['material', 'customer', 'category', 'bom'];
            $companyCode = 'TB';
            $triggerTables = ['IMAA_T', 'PMAB_T', 'RTAXL_T', 'BMBA_T'];

            foreach ($types as $type) {
                foreach ($triggerTables as $table) {
                    Cache::forget("transform_dedup_{$type}_{$companyCode}_{$table}");
                    Cache::forget("transform_status_{$type}_{$companyCode}_{$table}");
                }
            }

            $this->info("✅ 缓存清理完成");

        } catch (\Exception $e) {
            $this->warn("缓存清理失败: " . $e->getMessage());
        }
    }

    /**
     * 🔧 新增：测试并发转化
     */
    private function testConcurrentTransform(
        DataTransformService $transformService,
        string $type,
        string $companyCode,
        string $mode,
        int $batchSize,
        bool $dryRun
    ): int {
        $this->info("🔄 开始并发转化测试");
        
        // 确定要并发执行的类型
        $types = [];
        if ($type === 'all') {
            $types = ['category', 'customer', 'bom', 'material'];
        } else {
            // 为单个类型添加其他类型进行并发测试
            $types = [$type];
            $allTypes = ['category', 'customer', 'bom', 'material'];
            $otherTypes = array_diff($allTypes, [$type]);
            
            // 随机选择1-2个其他类型进行并发测试
            $additionalTypes = array_slice($otherTypes, 0, rand(1, 2));
            $types = array_merge($types, $additionalTypes);
        }
        
        $this->info("并发转化类型: " . implode(', ', $types));
        
        if ($dryRun) {
            $this->info("🔍 [模拟] 并发转化测试");
            $this->info("将同时执行 " . count($types) . " 个转化任务");
            $this->info("预期效果: 转化时间减少 " . round((1 - 1/count($types)) * 100, 1) . "%");
            return 0;
        }
        
        // 🔧 串行转化基准测试
        $this->info("📊 开始串行转化基准测试...");
        $serialStartTime = microtime(true);
        
        $serialOptions = [
            'incremental' => $mode === 'incremental',
            'batch_size' => $batchSize,
            'concurrent' => false // 强制串行
        ];
        
        $serialResult = $transformService->batchTransform($types, $companyCode, $serialOptions);
        $serialEndTime = microtime(true);
        $serialDuration = round($serialEndTime - $serialStartTime, 2);
        
        $this->info("串行转化完成，耗时: {$serialDuration}秒");
        
        // 🔧 并发转化测试
        $this->info("🚀 开始并发转化测试...");
        $concurrentStartTime = microtime(true);
        
        $concurrentOptions = [
            'incremental' => $mode === 'incremental',
            'batch_size' => $batchSize,
            'concurrent' => true // 启用并发
        ];
        
        $concurrentResult = $transformService->batchTransform($types, $companyCode, $concurrentOptions);
        $concurrentEndTime = microtime(true);
        $concurrentDuration = round($concurrentEndTime - $concurrentStartTime, 2);
        
        $this->info("并发转化完成，耗时: {$concurrentDuration}秒");
        
        // 🔧 性能对比分析
        $this->info("📈 性能对比分析:");
        $this->info("串行转化: {$serialDuration}秒");
        $this->info("并发转化: {$concurrentDuration}秒");
        
        if ($serialDuration > 0) {
            $speedup = round($serialDuration / $concurrentDuration, 2);
            $improvement = round((1 - $concurrentDuration / $serialDuration) * 100, 1);
            
            $this->info("性能提升: {$speedup}x 加速");
            $this->info("时间节省: {$improvement}%");
            
            if ($improvement > 20) {
                $this->info("✅ 并发转化效果显著！");
            } else if ($improvement > 0) {
                $this->info("⚠️ 并发转化有轻微提升");
            } else {
                $this->warn("❌ 并发转化未显示性能优势");
            }
        }
        
        // 🔧 详细结果对比
        $this->info("📋 详细结果对比:");
        $this->displayTransformResults("串行", $serialResult);
        $this->displayTransformResults("并发", $concurrentResult);
        
        return 0;
    }

    /**
     * 🔧 新增：显示转化结果
     */
    private function displayTransformResults(string $mode, array $result): void
    {
        $this->info("--- {$mode}转化结果 ---");
        
        if (isset($result['summary'])) {
            $summary = $result['summary'];
            $this->info("总类型数: " . ($summary['total_types'] ?? 0));
            $this->info("总处理数: " . ($summary['total_processed'] ?? 0));
            $this->info("总错误数: " . ($summary['total_errors'] ?? 0));
            $this->info("总耗时: " . ($summary['total_duration'] ?? 0) . "秒");
            $this->info("执行模式: " . ($summary['mode'] ?? 'unknown'));
        }
        
        if (isset($result['results'])) {
            foreach ($result['results'] as $type => $typeResult) {
                $processed = $typeResult['processed'] ?? 0;
                $errors = $typeResult['errors'] ?? 0;
                $duration = $typeResult['duration'] ?? 0;
                $this->info("  {$type}: {$processed}条记录, {$errors}个错误, {$duration}秒");
            }
        }
    }
} 