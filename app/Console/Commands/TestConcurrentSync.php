<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\DataSyncService;
use App\Models\SyncLog;
use Illuminate\Console\Command;
use Exception;

class TestConcurrentSync extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:test-concurrent {--tables=* : 指定要测试的表名}';

    /**
     * The console command description.
     */
    protected $description = '测试并发同步功能';

    private DataSyncService $syncService;

    public function __construct(DataSyncService $syncService)
    {
        parent::__construct();
        $this->syncService = $syncService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 开始测试并发同步功能...');
        
        // 获取要测试的表
        $tables = $this->option('tables');
        
        if (empty($tables)) {
            // 使用默认的两个表进行测试
            $tables = ['IMAA_T', 'BMAA_T'];
        }
        
        $this->info("📋 测试表: " . implode(', ', $tables));
        
        try {
            // 记录开始时间
            $startTime = microtime(true);
            
            $this->info("⚡ 启动并发同步...");
            
            // 执行并发同步
            $results = $this->syncService->syncTablesParallel($tables, SyncLog::TYPE_INCREMENTAL);
            
            // 记录结束时间
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info("⏱️  并发同步完成，耗时: {$duration} 秒");
            
            // 显示结果
            $this->displayResults($results);
            
            // 统计成功和失败数量
            $successCount = 0;
            $failureCount = 0;
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }
            
            $this->info("📊 同步结果统计:");
            $this->info("   ✅ 成功: {$successCount}");
            $this->info("   ❌ 失败: {$failureCount}");
            
            if ($failureCount === 0) {
                $this->info("🎉 所有表并发同步成功！");
                return Command::SUCCESS;
            } else {
                $this->warn("⚠️  部分表同步失败");
                return Command::FAILURE;
            }
            
        } catch (Exception $e) {
            $this->error("❌ 并发同步测试失败: " . $e->getMessage());
            $this->error("堆栈跟踪: " . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
    
    /**
     * 显示同步结果
     */
    private function displayResults(array $results): void
    {
        $this->info("📄 详细结果:");
        
        foreach ($results as $tableName => $result) {
            $status = $result['success'] ? '✅' : '❌';
            $this->info("   {$status} {$tableName}:");
            
            if ($result['success']) {
                $processed = $result['records_processed'] ?? 0;
                $inserted = $result['records_inserted'] ?? 0;
                $updated = $result['records_updated'] ?? 0;
                
                $this->info("      📈 处理记录: {$processed}");
                $this->info("      ➕ 插入: {$inserted}");
                $this->info("      ✏️  更新: {$updated}");
                
                if (isset($result['start_time']) && isset($result['end_time'])) {
                    $this->info("      🕐 开始: {$result['start_time']}");
                    $this->info("      🕑 结束: {$result['end_time']}");
                }
                
                if (isset($result['batch_id'])) {
                    $this->info("      🏷️  批次ID: {$result['batch_id']}");
                }
            } else {
                $error = $result['error'] ?? '未知错误';
                $this->error("      💥 错误: {$error}");
            }
            
            $this->info("");
        }
    }
}
