<?php

namespace App\Console\Commands;

use App\Services\DataTransformService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class TestInvalidUpdateFix extends Command
{
    /**bom
     * The name and signature of the console command.
     */
    protected $signature = 'test:invalid-update-fix 
                            {--type=material : 转化类型 (material|bom|category|customer)}
                            {--count=10 : 测试记录数量}
                            {--production : 使用生产环境数据库}';

    /**
     * The console command description.
     */
    protected $description = '测试无效UPDATE修复效果';

    private DataTransformService $transformService;

    public function __construct(DataTransformService $transformService)
    {
        parent::__construct();
        $this->transformService = $transformService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $count = (int) $this->option('count');
        $production = $this->option('production');

        if ($production) {
            // 配置生产环境数据库连接
            config(['database.connections.production' => [
                'driver' => 'mysql',
                'host' => '***********',
                'port' => '3306',
                'database' => 'Synchronism',
                'username' => 'root',
                'password' => '768594aaa',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
            ]]);
            
            // 设置默认连接为生产环境
            config(['database.default' => 'production']);
            DB::purge('mysql');
            
            $this->info("🌐 使用生产环境数据库 (***********)");
        }

        switch ($type) {
            case 'material':
                return $this->testMaterialTransform($count);
            case 'bom':
                return $this->testBomTransform($count);
            default:
                $this->error("不支持的类型: {$type}");
                $this->info("支持的类型: material, bom");
                return 1;
        }
    }

    private function testMaterialTransform(int $count): int
    {
        $this->info("🧪 测试物料转化的无效UPDATE修复效果");
        $this->info("测试记录数: {$count}");
        
        // 记录开始时的sync_change_log数量
        $beforeCount = DB::table('sync_change_log')
            ->where('table_name', 'materials')
            ->where('change_type', 'UPDATE')
            ->count();
            
        $this->info("转化前 sync_change_log 中 materials UPDATE 记录数: " . number_format($beforeCount));
        
        try {
            // 执行物料转化测试
            $transformService = app(DataTransformService::class);
            $result = $transformService->transform('material', 'TB', [
                'locale' => 'zh_CN',
                'test_mode' => true,
                'limit' => $count
            ]);
            
            // 记录结束时的sync_change_log数量
            $afterCount = DB::table('sync_change_log')
                ->where('table_name', 'materials')
                ->where('change_type', 'UPDATE')
                ->count();
                
            $newRecords = $afterCount - $beforeCount;
            
            $this->info("\n📊 转化结果:");
            $this->info("✅ 处理记录数: " . number_format($result['processed'] ?? 0));
            $this->info("🆕 新建记录数: " . number_format($result['created'] ?? 0));
            $this->info("🔄 更新记录数: " . number_format($result['updated'] ?? 0));
            $this->info("⏭️  跳过记录数: " . number_format($result['skipped'] ?? 0));
            $this->info("❌ 错误记录数: " . number_format($result['errors'] ?? 0));
            
            $this->info("\n📈 sync_change_log 变化:");
            $this->info("转化后 materials UPDATE 记录数: " . number_format($afterCount));
            $this->info("新增 UPDATE 记录数: " . number_format($newRecords));
            
            if ($newRecords == 0) {
                $this->info("🎉 完美！没有产生无效的UPDATE记录");
            } else {
                $this->warn("⚠️  仍有 {$newRecords} 条UPDATE记录产生");
            }
            
            return 0;
            
        } catch (Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
            return 1;
        }
    }

    private function testBomTransform(int $count): int
    {
        $this->info("🧪 测试BOM转化的无效UPDATE修复效果");
        $this->info("测试记录数: {$count}");
        
        // 记录开始时的sync_change_log数量
        // 注意：sync_change_log中记录的表名现在已统一为大写'BOM'，与实际数据库表名保持一致
        $beforeCount = DB::table('sync_change_log')
            ->where('table_name', 'BOM')
            ->where('change_type', 'UPDATE')
            ->count();
            
        $this->info("转化前 sync_change_log 中 bom UPDATE 记录数: " . number_format($beforeCount));
        
        try {
            // 执行BOM转化测试
            $transformService = app(DataTransformService::class);
            $result = $transformService->transform('bom', 'TB', [
                'version' => Carbon::now()->format('Ymd'), // 使用当前日期作为版本
                'test_mode' => true,
                'limit' => $count
            ]);
            
            // 记录结束时的sync_change_log数量
            // 注意：sync_change_log中记录的表名现在已统一为大写'BOM'，与实际数据库表名保持一致
            $afterCount = DB::table('sync_change_log')
                ->where('table_name', 'BOM')
                ->where('change_type', 'UPDATE')
                ->count();
                
            $newRecords = $afterCount - $beforeCount;
            
            $this->info("\n📊 转化结果:");
            $this->info("✅ 处理记录数: " . number_format($result['processed'] ?? 0));
            $this->info("🆕 新建记录数: " . number_format($result['created'] ?? 0));
            $this->info("🔄 更新记录数: " . number_format($result['updated'] ?? 0));
            $this->info("⏭️  跳过记录数: " . number_format($result['skipped'] ?? 0));
            $this->info("❌ 错误记录数: " . number_format($result['errors'] ?? 0));
            
            $this->info("\n📈 sync_change_log 变化:");
            $this->info("转化后 bom UPDATE 记录数: " . number_format($afterCount));
            $this->info("新增 UPDATE 记录数: " . number_format($newRecords));
            
            if ($newRecords == 0) {
                $this->info("🎉 完美！没有产生无效的UPDATE记录");
            } else {
                $this->warn("⚠️  仍有 {$newRecords} 条UPDATE记录产生");
            }
            
            return 0;
            
        } catch (Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 获取sync_change_log统计信息
     */
    private function getSyncChangeLogStats(): array
    {
        $stats = [];

        // 按表和操作类型统计
        $distribution = DB::table('sync_change_log')
            ->select('table_name', 'change_type', DB::raw('COUNT(*) as count'))
            ->groupBy('table_name', 'change_type')
            ->get();

        foreach ($distribution as $item) {
            $stats[$item->table_name][$item->change_type] = $item->count;
        }

        // 总计
        $stats['total'] = DB::table('sync_change_log')->count();

        // 最近1分钟的记录
        $stats['recent'] = DB::table('sync_change_log')
            ->where('created_at', '>=', now()->subMinute())
            ->count();

        return $stats;
    }

    /**
     * 显示统计信息
     */
    private function displayStats(string $label, array $stats): void
    {
        $this->info("\n📊 {$label}sync_change_log统计:");
        $this->line("  📝 总记录数: " . ($stats['total'] ?? 0));
        $this->line("  🕐 最近1分钟: " . ($stats['recent'] ?? 0));

        // 显示各表的详细统计
        foreach ($stats as $table => $operations) {
            if ($table === 'total' || $table === 'recent') {
                continue;
            }

            $this->line("  📋 {$table}表:");
            if (is_array($operations)) {
                foreach ($operations as $operation => $count) {
                    $icon = match(strtolower($operation)) {
                        'insert' => '➕',
                        'update' => '✏️',
                        'delete' => '🗑️',
                        default => '❓'
                    };
                    $this->line("    {$icon} {$operation}: {$count} 条");
                }
            }
        }
    }

    /**
     * 分析前后差异
     */
    private function analyzeDifference(array $before, array $after, array $result): void
    {
        $this->info("\n🔍 差异分析:");

        $totalDiff = ($after['total'] ?? 0) - ($before['total'] ?? 0);
        $recentDiff = ($after['recent'] ?? 0) - ($before['recent'] ?? 0);

        $this->line("  📈 总记录增加: {$totalDiff} 条");
        $this->line("  🕐 最近1分钟新增: {$recentDiff} 条");

        // 分析各表的变化
        $tables = array_unique(array_merge(array_keys($before), array_keys($after)));
        foreach ($tables as $table) {
            if ($table === 'total' || $table === 'recent') {
                continue;
            }

            $beforeTable = $before[$table] ?? [];
            $afterTable = $after[$table] ?? [];

            $operations = array_unique(array_merge(array_keys($beforeTable), array_keys($afterTable)));
            foreach ($operations as $operation) {
                $beforeCount = $beforeTable[$operation] ?? 0;
                $afterCount = $afterTable[$operation] ?? 0;
                $diff = $afterCount - $beforeCount;

                if ($diff > 0) {
                    $icon = match(strtolower($operation)) {
                        'insert' => '➕',
                        'update' => '✏️',
                        'delete' => '🗑️',
                        default => '❓'
                    };
                    $this->line("  {$icon} {$table}.{$operation}: +{$diff} 条");
                }
            }
        }

        // 评估修复效果
        $this->evaluateFixEffectiveness($result, $totalDiff, $recentDiff);
    }

    /**
     * 评估修复效果
     */
    private function evaluateFixEffectiveness(array $result, int $totalDiff, int $recentDiff): void
    {
        $this->info("\n📈 修复效果评估:");

        $processed = $result['processed'] ?? 0;
        $updated = $result['updated'] ?? 0;
        $skipped = $result['skipped'] ?? 0;

        $this->line("  📊 转化统计: 处理{$processed}条，更新{$updated}条，跳过{$skipped}条");
        $this->line("  📝 sync_change_log新增: {$recentDiff} 条");

        // 理想情况下，sync_change_log的新增记录应该等于实际更新的记录数
        if ($recentDiff <= $updated) {
            $this->info("  ✅ 修复效果良好：sync_change_log记录数({$recentDiff}) <= 实际更新数({$updated})");
        } elseif ($recentDiff <= $updated * 1.2) {
            $this->warn("  ⚠️  修复效果一般：sync_change_log记录数({$recentDiff})略高于实际更新数({$updated})");
        } else {
            $this->error("  ❌ 修复效果不佳：sync_change_log记录数({$recentDiff})明显高于实际更新数({$updated})");
            $this->line("    建议检查触发器逻辑或变更检测机制");
        }

        // 分析跳过记录的效果
        if ($skipped > 0) {
            $skipRatio = round(($skipped / $processed) * 100, 1);
            $this->info("  🎯 变更检测效果：跳过了{$skipped}条无变化记录，占比{$skipRatio}%");
            
            if ($skipRatio > 50) {
                $this->info("    ✅ 变更检测机制工作良好，有效避免了无效更新");
            } elseif ($skipRatio > 20) {
                $this->info("    👍 变更检测机制基本有效");
            } else {
                $this->warn("    ⚠️  变更检测机制效果有限，可能需要进一步优化");
            }
        }
    }
} 