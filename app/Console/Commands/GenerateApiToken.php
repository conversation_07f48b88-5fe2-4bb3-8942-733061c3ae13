<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class GenerateApiToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sanctum:generate-token {user_id? : User ID to generate token for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate API token for testing';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $userId = $this->argument('user_id');
        
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }
        } else {
            // 使用第一个用户或创建一个测试用户
            $user = User::first();
            if (!$user) {
                $this->info('No users found. Creating a test user...');
                $user = User::create([
                    'name' => 'Test User',
                    'username' => 'testuser',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]);
                $this->info("Created test user: {$user->email}");
            }
        }
        
        // 删除旧的token（如果存在）
        $user->tokens()->delete();
        
        // 生成新的token
        $token = $user->createToken('API Token', ['*'])->plainTextToken;
        
        $this->info("API Token generated for user: {$user->name} ({$user->email})");
        $this->info("User ID: {$user->id}");
        $this->line('');
        $this->line("Token: {$token}");
        $this->line('');
        $this->info('You can use this token in the Authorization header:');
        $this->line("Authorization: Bearer {$token}");
        $this->line('');
        $this->info('Example curl command:');
        $this->line("curl -H 'Authorization: Bearer {$token}' http://synchronism.local/api/dashboard/stats");
        
        return 0;
    }
}
