<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\SyncLog;
use App\Services\DataSyncService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CheckBmbaSync extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'bmba:check-sync {--status : 仅显示状态} {--progress : 显示详细进度}';

    /**
     * The console command description.
     */
    protected $description = '检查BMBA_T表的同步状态和进度';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 检查BMBA_T表同步状态...');
        $this->newLine();

        // 检查最新的同步记录
        $latestSync = SyncLog::where('table_name', 'BMBA_T')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$latestSync) {
            $this->error('❌ 没有找到BMBA_T表的同步记录');
            return 1;
        }

        // 显示基本状态
        $this->info("📋 最新同步记录 (ID: {$latestSync->id})");
        $this->table(
            ['属性', '值'],
            [
                ['状态', $this->getStatusDisplay($latestSync->status)],
                ['同步类型', $latestSync->sync_type === 'full' ? '全量同步' : '增量同步'],
                ['开始时间', $latestSync->start_time?->format('Y-m-d H:i:s') ?? '未知'],
                ['结束时间', $latestSync->end_time?->format('Y-m-d H:i:s') ?? '进行中'],
                ['已处理记录', number_format($latestSync->records_processed ?? 0)],
                ['插入记录', number_format($latestSync->records_inserted ?? 0)],
                ['更新记录', number_format($latestSync->records_updated ?? 0)],
            ]
        );

        // 如果任务正在运行，显示详细进度
        if ($latestSync->status === SyncLog::STATUS_RUNNING) {
            $this->newLine();
            $this->info('⏳ 任务正在运行中，获取详细进度...');
            
            try {
                // 获取Oracle表总记录数
                $totalRecords = DB::connection('oracle')
                    ->table('BMBA_T')
                    ->where('BMBAENT', 40)
                    ->count();

                $processedRecords = $latestSync->records_processed ?? 0;
                $percentage = $totalRecords > 0 ? round(($processedRecords / $totalRecords) * 100, 2) : 0;
                
                // 计算运行时间
                $runningTime = $latestSync->start_time 
                    ? $latestSync->start_time->diffInSeconds(Carbon::now())
                    : 0;
                
                $runningMinutes = floor($runningTime / 60);
                $runningSeconds = $runningTime % 60;

                // 估算剩余时间
                $estimatedRemaining = null;
                if ($percentage > 0 && $percentage < 100) {
                    $estimatedTotalTime = ($runningTime / $percentage) * 100;
                    $estimatedRemaining = $estimatedTotalTime - $runningTime;
                }

                $this->table(
                    ['进度信息', '数值'],
                    [
                        ['总记录数', number_format($totalRecords)],
                        ['已处理', number_format($processedRecords)],
                        ['进度百分比', "{$percentage}%"],
                        ['运行时间', "{$runningMinutes} 分 {$runningSeconds} 秒"],
                        ['预估剩余时间', $estimatedRemaining ? floor($estimatedRemaining / 60) . ' 分钟' : '计算中...'],
                        ['处理速度', $runningTime > 0 ? round($processedRecords / ($runningTime / 60), 0) . ' 条/分钟' : '计算中...'],
                    ]
                );

                // 显示进度条
                $this->newLine();
                $progressBar = $this->output->createProgressBar($totalRecords);
                $progressBar->setProgress($processedRecords);
                $progressBar->display();
                $this->newLine(2);

            } catch (\Exception $e) {
                $this->error("获取进度信息失败: " . $e->getMessage());
            }
        }

        // 显示错误信息（如果有）
        if ($latestSync->error_message) {
            $this->newLine();
            $this->error('❌ 错误信息:');
            $this->line($latestSync->error_message);
        }

        // 显示最近的同步历史
        if ($this->option('progress')) {
            $this->newLine();
            $this->info('📈 最近5次同步历史:');
            
            $recentSyncs = SyncLog::where('table_name', 'BMBA_T')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            $historyData = $recentSyncs->map(function ($sync) {
                $duration = $sync->start_time && $sync->end_time 
                    ? $sync->end_time->diffInMinutes($sync->start_time) . ' 分钟'
                    : ($sync->status === SyncLog::STATUS_RUNNING ? '进行中' : '未知');

                return [
                    $sync->created_at->format('m-d H:i'),
                    $this->getStatusDisplay($sync->status),
                    $sync->sync_type === 'full' ? '全量' : '增量',
                    number_format($sync->records_processed ?? 0),
                    $duration,
                ];
            });

            $this->table(
                ['时间', '状态', '类型', '处理记录', '耗时'],
                $historyData->toArray()
            );
        }

        return 0;
    }

    /**
     * 获取状态显示文本
     */
    private function getStatusDisplay(string $status): string
    {
        return match ($status) {
            SyncLog::STATUS_PENDING => '🟡 等待中',
            SyncLog::STATUS_RUNNING => '🔄 运行中',
            SyncLog::STATUS_SUCCESS => '✅ 成功',
            SyncLog::STATUS_FAILED => '❌ 失败',
            default => '❓ 未知',
        };
    }
} 