<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Schema;

class DiagnoseDataTransform extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diagnose:data-transform 
                            {--table= : 检查特定表的转化问题}
                            {--hours=24 : 检查最近N小时的数据，默认24小时}
                            {--show-details : 显示详细信息}
                            {--check-triggers : 检查触发器是否正常}
                            {--production : 检查生产环境}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '诊断数据转化问题：检查sync_change_log表的异常UPDATE记录';

    private $isProduction = false;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->isProduction = $this->option('production');
        
        if ($this->isProduction) {
            $this->info('🌐 正在检查生产环境...');
            $this->configureProductionConnection();
        } else {
            $this->info('🔍 正在检查本地开发环境...');
        }
        
        $this->newLine();

        $table = $this->option('table');
        $hours = (int) $this->option('hours');
        $showDetails = $this->option('show-details');
        $checkTriggers = $this->option('check-triggers');

        // 1. 检查sync_change_log表的UPDATE记录统计
        $this->checkSyncChangeLogStats($table, $hours);

        // 2. 检查sync_logs表的同步记录
        $this->checkSyncLogsStats($hours);

        // 3. 检查队列任务执行情况
        $this->checkQueueStats($hours);

        // 4. 检查materials表的UPDATE频率
        $this->checkMaterialsUpdateFrequency($hours);

        // 5. 检查触发器状态
        if ($checkTriggers) {
            $this->checkTriggerStatus();
        }

        // 6. 分析可能的问题原因
        $this->analyzeProblems($table, $hours, $showDetails);

        // 新增：分析MySQL sync_change_log问题
        $this->analyzeMySQLSyncChangeLog();

        // 重点分析BOM表和Materials表
        $this->analyzeBOMAndMaterialsUpdates();

        $this->newLine();
        $this->info('✅ 诊断完成');

        return Command::SUCCESS;
    }

    /**
     * 检查sync_change_log表的统计信息
     */
    private function checkSyncChangeLogStats(?string $table, int $hours): void
    {
        $this->info('📊 检查 sync_change_log 表统计信息...');

        try {
            $timeCondition = "change_time >= SYSTIMESTAMP - INTERVAL '{$hours}' HOUR";
            $tableCondition = $table ? "AND table_name = '{$table}'" : '';

            // 按操作类型统计
            $operationStats = DB::connection('oracle')->select("
                SELECT 
                    operation,
                    sync_status,
                    COUNT(*) as count
                FROM sync_change_log 
                WHERE {$timeCondition} {$tableCondition}
                GROUP BY operation, sync_status
                ORDER BY operation, sync_status
            ");

            $this->table(['操作类型', '同步状态', '记录数'], array_map(function($row) {
                return [
                    $row->operation,
                    match($row->sync_status) {
                        0 => '未同步',
                        1 => '已同步',
                        2 => '失败',
                        default => '未知'
                    },
                    number_format($row->count)
                ];
            }, $operationStats));

            // 按表统计
            if (!$table) {
                $tableStats = DB::connection('oracle')->select("
                    SELECT 
                        table_name,
                        operation,
                        COUNT(*) as count
                    FROM sync_change_log 
                    WHERE {$timeCondition}
                    GROUP BY table_name, operation
                    ORDER BY count DESC
                ");

                $this->newLine();
                $this->info('📋 按表分组统计：');
                $this->table(['表名', '操作类型', '记录数'], array_map(function($row) {
                    return [$row->table_name, $row->operation, number_format($row->count)];
                }, $tableStats));
            }

        } catch (\Exception $e) {
            $this->error("检查sync_change_log失败: " . $e->getMessage());
        }
    }

    /**
     * 检查sync_logs表的同步记录
     */
    private function checkSyncLogsStats(int $hours): void
    {
        $this->newLine();
        $this->info('📈 检查 sync_logs 表同步记录...');

        try {
            $cutoffTime = Carbon::now()->subHours($hours);

                         $syncStats = DB::connection($this->getDbConnection())->table('sync_logs')
                ->where('created_at', '>=', $cutoffTime)
                ->selectRaw('
                    table_name,
                    sync_type,
                    COUNT(*) as sync_count,
                    SUM(records_processed) as total_processed,
                    SUM(records_inserted) as total_inserted,
                    SUM(records_updated) as total_updated,
                    SUM(records_deleted) as total_deleted,
                    AVG(CASE WHEN status = "success" THEN 1 ELSE 0 END) * 100 as success_rate
                ')
                ->groupBy('table_name', 'sync_type')
                ->orderBy('total_processed', 'desc')
                ->get();

            if ($syncStats->isEmpty()) {
                $this->warn("最近{$hours}小时内没有同步记录");
                return;
            }

            $this->table(
                ['表名', '类型', '同步次数', '处理记录', '插入', '更新', '删除', '成功率'],
                                 $syncStats->map(function($row) {
                     return [
                         $row->table_name,
                         $row->sync_type,
                         $row->sync_count,
                         number_format($row->total_processed ?? 0),
                         number_format($row->total_inserted ?? 0),
                         number_format($row->total_updated ?? 0),
                         number_format($row->total_deleted ?? 0),
                         round($row->success_rate, 1) . '%'
                     ];
                 })->toArray()
            );

        } catch (\Exception $e) {
            $this->error("检查sync_logs失败: " . $e->getMessage());
        }
    }

    /**
     * 检查队列任务执行情况
     */
    private function checkQueueStats(int $hours): void
    {
        $this->newLine();
        $this->info('⚙️ 检查队列任务执行情况...');

        try {
            $cutoffTime = Carbon::now()->subHours($hours);

                         // 检查当前队列中的任务
             $queueJobs = DB::connection($this->getDbConnection())->table('jobs')
                ->where('queue', 'transform')
                ->where('created_at', '>=', $cutoffTime)
                ->count();

             $this->line("Transform队列中待处理任务: {$queueJobs}");

             // 检查失败的任务
             $failedJobs = DB::connection($this->getDbConnection())->table('failed_jobs')
                ->where('queue', 'transform')
                ->where('failed_at', '>=', $cutoffTime)
                ->count();

            $this->line("Transform队列失败任务: {$failedJobs}");

                         // 检查最近的转化任务日志
             $dbConfig = config('database.connections.' . $this->getDbConnection());
             $recentTransformLogs = DB::connection($this->getDbConnection())
                ->table('information_schema.tables')
                ->where('table_schema', $dbConfig['database'])
                ->where('table_name', 'job_batches')
                ->exists();

             if ($recentTransformLogs) {
                 $batchStats = DB::connection($this->getDbConnection())->table('job_batches')
                    ->where('name', 'like', '%transform%')
                    ->where('created_at', '>=', $cutoffTime)
                    ->selectRaw('
                        COUNT(*) as batch_count,
                        SUM(total_jobs) as total_jobs,
                        SUM(pending_jobs) as pending_jobs,
                        SUM(failed_jobs) as failed_jobs
                    ')
                    ->first();

                if ($batchStats && $batchStats->batch_count > 0) {
                    $this->line("转化任务批次: {$batchStats->batch_count}");
                    $this->line("总任务数: {$batchStats->total_jobs}");
                    $this->line("待处理: {$batchStats->pending_jobs}");
                    $this->line("失败: {$batchStats->failed_jobs}");
                }
            }

        } catch (\Exception $e) {
            $this->error("检查队列统计失败: " . $e->getMessage());
        }
    }

    /**
     * 检查materials表的UPDATE频率
     */
    private function checkMaterialsUpdateFrequency(int $hours): void
    {
        $this->newLine();
        $this->info('🔄 检查 materials 表的UPDATE频率...');

        try {
            $cutoffTime = Carbon::now()->subHours($hours);

                         // 检查materials表的更新频率
             $materialsStats = DB::connection($this->getDbConnection())->table('materials')
                ->where('updated_at', '>=', $cutoffTime)
                ->selectRaw('
                    COUNT(*) as updated_count,
                    COUNT(DISTINCT material_code) as unique_materials,
                    MIN(updated_at) as first_update,
                    MAX(updated_at) as last_update
                ')
                ->first();

            if ($materialsStats->updated_count > 0) {
                $this->line("最近{$hours}小时materials表更新记录: " . number_format($materialsStats->updated_count));
                $this->line("涉及物料数量: " . number_format($materialsStats->unique_materials));
                $this->line("首次更新时间: {$materialsStats->first_update}");
                $this->line("最后更新时间: {$materialsStats->last_update}");

                                 // 检查是否有频繁更新的物料
                 $frequentUpdates = DB::connection($this->getDbConnection())->table('materials')
                    ->where('updated_at', '>=', $cutoffTime)
                    ->selectRaw('material_code, COUNT(*) as update_count')
                    ->groupBy('material_code')
                    ->having('update_count', '>', 1)
                    ->orderBy('update_count', 'desc')
                    ->limit(10)
                    ->get();

                if ($frequentUpdates->isNotEmpty()) {
                    $this->warn("发现频繁更新的物料（可能存在重复更新问题）:");
                    $this->table(['物料编码', '更新次数'], $frequentUpdates->map(function($row) {
                        return [$row->material_code, $row->update_count];
                    })->toArray());
                }
            } else {
                $this->line("最近{$hours}小时materials表没有更新记录");
            }

                         // 检查material_translations表
             $translationStats = DB::connection($this->getDbConnection())->table('material_translations')
                ->where('updated_at', '>=', $cutoffTime)
                ->count();

            $this->line("material_translations表更新记录: " . number_format($translationStats));

        } catch (\Exception $e) {
            $this->error("检查materials表更新频率失败: " . $e->getMessage());
        }
    }

    /**
     * 检查触发器状态
     */
    private function checkTriggerStatus(): void
    {
        $this->newLine();
        $this->info('🔧 检查触发器状态...');

        try {
                         // 检查MySQL触发器
             $dbConfig = config('database.connections.' . $this->getDbConnection());
             $triggers = DB::connection($this->getDbConnection())->select("
                 SELECT 
                     TRIGGER_NAME,
                     EVENT_MANIPULATION,
                     EVENT_OBJECT_TABLE
                 FROM information_schema.TRIGGERS 
                 WHERE TRIGGER_SCHEMA = ?
                 AND EVENT_OBJECT_TABLE IN ('materials', 'material_translations')
                 ORDER BY EVENT_OBJECT_TABLE, EVENT_MANIPULATION
             ", [$dbConfig['database']]);

            if (empty($triggers)) {
                $this->warn("没有找到materials相关的触发器");
                return;
            }

            $this->table(['触发器名', '事件', '表名'], array_map(function($trigger) {
                return [
                    $trigger->TRIGGER_NAME,
                    $trigger->EVENT_MANIPULATION,
                    $trigger->EVENT_OBJECT_TABLE
                ];
            }, $triggers));

                         // 简化检查，只显示触发器存在性
             $updateTriggers = array_filter($triggers, function($trigger) {
                 return $trigger->EVENT_MANIPULATION === 'UPDATE';
             });
             
             if (count($updateTriggers) > 0) {
                 $this->info("找到 " . count($updateTriggers) . " 个UPDATE触发器");
             } else {
                 $this->warn("没有找到UPDATE触发器，这可能是问题所在");
             }

        } catch (\Exception $e) {
            $this->error("检查触发器状态失败: " . $e->getMessage());
        }
    }

    /**
     * 分析可能的问题原因
     */
    private function analyzeProblems(?string $table, int $hours, bool $showDetails): void
    {
        $this->newLine();
        $this->info('🔍 问题分析和建议...');

        try {
            // 获取sync_change_log中的UPDATE统计
            $timeCondition = "change_time >= SYSTIMESTAMP - INTERVAL '{$hours}' HOUR";
            $updateStats = DB::connection('oracle')->selectOne("
                SELECT COUNT(*) as update_count
                FROM sync_change_log 
                WHERE {$timeCondition} AND operation = 'UPDATE'
            ");

                         // 获取sync_logs中的处理统计
             $cutoffTime = Carbon::now()->subHours($hours);
             $syncProcessed = DB::connection($this->getDbConnection())->table('sync_logs')
                ->where('created_at', '>=', $cutoffTime)
                ->sum('records_processed') ?? 0;

            $updateCount = $updateStats->update_count ?? 0;

            $this->line("🔢 数据对比:");
            $this->line("  - sync_change_log UPDATE记录: " . number_format($updateCount));
            $this->line("  - sync_logs 处理记录总数: " . number_format($syncProcessed));

            if ($updateCount > $syncProcessed * 10) {
                $this->error("⚠️  发现异常：sync_change_log的UPDATE记录数量远超sync_logs的处理记录数量！");
                
                $this->newLine();
                $this->warn("🔍 可能的原因分析：");
                $this->line("1. 数据转化系统频繁执行，每次都更新大量物料记录");
                $this->line("2. updateOrCreate方法即使数据相同也会更新updated_at字段");
                $this->line("3. 触发器记录了所有UPDATE操作，包括无意义的时间戳更新");
                $this->line("4. 转化任务可能存在重复执行或全量处理的问题");

                $this->newLine();
                $this->info("🛠️  建议的解决方案：");
                $this->line("1. 在DataTransformService中添加变更检测机制");
                $this->line("2. 优化触发器，排除只有updated_at变化的UPDATE");
                $this->line("3. 实施增量转化策略，只处理真正变更的记录");
                $this->line("4. 添加转化任务去重机制，避免重复执行");

            } else {
                $this->info("✅ 数据量对比正常，没有发现明显异常");
            }

                         // 检查是否有转化任务在运行
             $runningJobs = DB::connection($this->getDbConnection())->table('jobs')
                ->where('queue', 'transform')
                ->count();

            if ($runningJobs > 10) {
                $this->warn("⚠️  Transform队列中有 {$runningJobs} 个待处理任务，可能存在积压");
            }

        } catch (\Exception $e) {
            $this->error("问题分析失败: " . $e->getMessage());
        }
    }

    /**
     * 配置生产环境数据库连接
     */
    private function configureProductionConnection(): void
    {
        try {
            // 配置生产环境MySQL连接
            config(['database.connections.mysql_prod' => [
                'driver' => 'mysql',
                'host' => '***********',
                'port' => '3306',
                'database' => 'Synchronism',
                'username' => 'root',
                'password' => '768594aaa',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
                'prefix' => '',
                'strict' => true,
                'engine' => null,
            ]]);

            // 测试连接
            DB::connection('mysql_prod')->getPdo();
            $this->info('✅ 生产环境MySQL连接成功');

        } catch (\Exception $e) {
            $this->error('❌ 生产环境连接失败: ' . $e->getMessage());
            exit(1);
        }
    }

    /**
     * 获取数据库连接名称
     */
    private function getDbConnection(): string
    {
        return $this->isProduction ? 'mysql_prod' : 'mysql';
    }

    /**
     * 分析MySQL sync_change_log表中的UPDATE记录问题
     */
    private function analyzeMySQLSyncChangeLog(): void
    {
        $this->newLine();
        $this->info("🔍 分析MySQL sync_change_log表中的UPDATE记录问题...");

        try {
            // 检查MySQL sync_change_log表的记录分布
            $distribution = DB::table('sync_change_log')
                ->select('table_name', 'change_type', DB::raw('COUNT(*) as count'))
                ->groupBy('table_name', 'change_type')
                ->orderBy('count', 'desc')
                ->get();

            $this->line("📊 MySQL sync_change_log表记录分布：");
            $totalUpdates = 0;
            $allTables = [];
            
            foreach ($distribution as $item) {
                $icon = match($item->change_type) {
                    'INSERT' => '➕',
                    'UPDATE' => '✏️',
                    'DELETE' => '🗑️',
                    default => '❓'
                };
                
                $this->line("  {$icon} {$item->table_name} - {$item->change_type}: " . number_format($item->count) . " 条");
                
                if (strtoupper($item->change_type) === 'UPDATE') {
                    $totalUpdates += $item->count;
                }
                
                $allTables[] = $item->table_name;
            }

            $this->newLine();
            $this->warn("⚠️  总UPDATE记录数: " . number_format($totalUpdates));

            // 检查是否有超过20万的记录
            $largeUpdateTables = $distribution->filter(function($item) {
                return strtoupper($item->change_type) === 'UPDATE' && $item->count > 200000;
            });

            if ($largeUpdateTables->count() > 0) {
                $this->error("🚨 发现超过20万UPDATE记录的表：");
                foreach ($largeUpdateTables as $table) {
                    $this->line("  🔥 {$table->table_name}: " . number_format($table->count) . " 条UPDATE");
                }
            }

            // 重点分析materials表（如果存在）
            $materialsUpdate = $distribution->where('table_name', 'materials')
                ->where('change_type', 'UPDATE')
                ->first();

            if ($materialsUpdate) {
                $this->line("📋 materials表详细统计：");
                $materialsStats = DB::table('sync_change_log')
                    ->where('table_name', 'materials')
                    ->select('change_type', DB::raw('COUNT(*) as count'))
                    ->groupBy('change_type')
                    ->get();

                foreach ($materialsStats as $stat) {
                    $icon = match($stat->change_type) {
                        'INSERT' => '➕',
                        'UPDATE' => '✏️',
                        'DELETE' => '🗑️',
                        default => '❓'
                    };
                    $this->line("  {$icon} {$stat->change_type}: " . number_format($stat->count) . " 条");
                }

                // 显示最近的materials UPDATE记录
                $recentMaterials = DB::table('sync_change_log')
                    ->where('table_name', 'materials')
                    ->where('change_type', 'UPDATE')
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get(['pk_json', 'created_at']);

                $this->line("📋 materials表最近10条UPDATE记录：");
                foreach ($recentMaterials as $record) {
                    $pk = json_decode($record->pk_json, true);
                    $materialCode = $pk['material_code'] ?? 'unknown';
                    $this->line("  ✏️  材料代码: {$materialCode} | {$record->created_at}");
                }
            }

            // 重点分析BOM相关表
            $bomTables = $distribution->filter(function($item) {
                return stripos($item->table_name, 'BOM') !== false;
            });

            if ($bomTables->count() > 0) {
                $this->newLine();
                $this->info("📋 BOM相关表统计：");
                foreach ($bomTables as $table) {
                    $icon = match($table->change_type) {
                        'INSERT' => '➕',
                        'UPDATE' => '✏️',
                        'DELETE' => '🗑️',
                        default => '❓'
                    };
                    $this->line("  {$icon} {$table->table_name} - {$table->change_type}: " . number_format($table->count) . " 条");
                    
                    // 如果是UPDATE记录且数量很大，显示详细信息
                    if (strtoupper($table->change_type) === 'UPDATE' && $table->count > 1000) {
                        $recentBom = DB::table('sync_change_log')
                            ->where('table_name', $table->table_name)
                            ->where('change_type', 'UPDATE')
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get(['pk_json', 'created_at']);

                        $this->line("    📋 最近5条UPDATE记录：");
                        foreach ($recentBom as $record) {
                            $pk = json_decode($record->pk_json, true);
                            $pkStr = $pk ? implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pk), $pk)) : '解析失败';
                            $this->line("      {$pkStr} | {$record->created_at}");
                        }
                    }
                }
            } else {
                $this->line("📋 未找到BOM相关表的记录");
            }

            // 检查最近24小时和1小时的UPDATE活动
            $recent24h = DB::table('sync_change_log')
                ->where('table_name', 'materials')
                ->where('change_type', 'UPDATE')
                ->where('created_at', '>=', now()->subHours(24))
                ->count();

            $recent1h = DB::table('sync_change_log')
                ->where('table_name', 'materials')
                ->where('change_type', 'UPDATE')
                ->where('created_at', '>=', now()->subHour())
                ->count();

            $this->warn("⚠️  materials表最近24小时UPDATE记录: {$recent24h} 条");
            $this->warn("⚠️  materials表最近1小时UPDATE记录: {$recent1h} 条");

            if ($totalUpdates > 50000) {
                $this->error("🚨 发现严重异常：MySQL sync_change_log表中有 " . number_format($totalUpdates) . " 条UPDATE记录！");
                
                $this->newLine();
                $this->info("🔍 可能的原因分析：");
                $this->line("1. 🔄 数据转化系统频繁执行全量更新");
                $this->line("2. ⚡ updateOrCreate即使数据相同也会更新updated_at字段");
                $this->line("3. 📝 触发器记录了所有UPDATE操作，包括无意义的时间戳更新");
                $this->line("4. 🔁 转化任务可能存在重复执行或全量处理的问题");

                $this->newLine();
                $this->info("🛠️  建议的解决方案：");
                $this->line("1. 在DataTransformService中添加变更检测机制");
                $this->line("2. 优化MySQL触发器，排除只有updated_at变化的UPDATE");
                $this->line("3. 实施增量转化策略，只处理真正变更的记录");
                $this->line("4. 添加转化任务去重机制，避免重复执行");
            }

        } catch (Exception $e) {
            $this->error("分析MySQL sync_change_log失败: " . $e->getMessage());
        }
    }

    /**
     * 重点分析BOM表和Materials表的UPDATE记录问题
     */
    private function analyzeBOMAndMaterialsUpdates(): void
    {
        $this->newLine();
        $this->info("🔍 重点分析BOM表和Materials表的UPDATE记录问题...");

        try {
            // 分析materials表
            $this->analyzeTableUpdates('materials', 'Materials表');
            
            // 分析BOM表（注意：sync_change_log中记录的表名现在已统一为大写'BOM'，与实际数据库表名保持一致）
            $this->analyzeTableUpdates('BOM', 'BOM表');
            
            // 分析material_translations表
            $this->analyzeTableUpdates('material_translations', 'Materials翻译表');

            // 检查转化任务执行频率
            $this->analyzeTransformTaskFrequency();

        } catch (Exception $e) {
            $this->error("分析BOM和Materials表失败: " . $e->getMessage());
        }
    }

    /**
     * 分析指定表的UPDATE记录
     */
    private function analyzeTableUpdates(string $tableName, string $displayName): void
    {
        $this->newLine();
        $this->info("📊 分析{$displayName}的UPDATE记录...");

        // 获取该表的基本统计
        $totalRecords = DB::table('sync_change_log')
            ->where('table_name', $tableName)
            ->count();

        $updateRecords = DB::table('sync_change_log')
            ->where('table_name', $tableName)
            ->where('change_type', 'UPDATE')
            ->count();

        $insertRecords = DB::table('sync_change_log')
            ->where('table_name', $tableName)
            ->where('change_type', 'INSERT')
            ->count();

        $this->line("  📝 总记录数: {$totalRecords}");
        $this->line("  ✏️  UPDATE记录: {$updateRecords}");
        $this->line("  ➕ INSERT记录: {$insertRecords}");

        if ($updateRecords > 0) {
            // 分析UPDATE记录的时间分布
            $timeDistribution = DB::table('sync_change_log')
                ->where('table_name', $tableName)
                ->where('change_type', 'UPDATE')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date', 'desc')
                ->limit(7)
                ->get();

            $this->line("  📅 最近7天UPDATE分布:");
            foreach ($timeDistribution as $day) {
                $this->line("    {$day->date}: {$day->count} 条");
            }

            // 分析小时级别的分布（最近24小时）
            $hourlyDistribution = DB::table('sync_change_log')
                ->where('table_name', $tableName)
                ->where('change_type', 'UPDATE')
                ->where('created_at', '>=', now()->subHours(24))
                ->select(
                    DB::raw('HOUR(created_at) as hour'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy(DB::raw('HOUR(created_at)'))
                ->orderBy('hour')
                ->get();

            if ($hourlyDistribution->count() > 0) {
                $this->line("  🕐 最近24小时UPDATE分布:");
                foreach ($hourlyDistribution as $hour) {
                    $this->line("    {$hour->hour}:00 - {$hour->count} 条");
                }
            }

            // 检查是否存在大量相同记录的UPDATE
            $duplicateUpdates = DB::table('sync_change_log')
                ->where('table_name', $tableName)
                ->where('change_type', 'UPDATE')
                ->select('pk_json', DB::raw('COUNT(*) as count'))
                ->groupBy('pk_json')
                ->having('count', '>', 5)
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get();

            if ($duplicateUpdates->count() > 0) {
                $this->warn("  ⚠️  发现重复UPDATE的记录:");
                foreach ($duplicateUpdates as $duplicate) {
                    $pk = json_decode($duplicate->pk_json, true);
                    $pkStr = $pk ? implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pk), $pk)) : '解析失败';
                    $this->line("    {$pkStr}: {$duplicate->count} 次UPDATE");
                }
            }

            // 检查最近的UPDATE记录样本
            $recentUpdates = DB::table('sync_change_log')
                ->where('table_name', $tableName)
                ->where('change_type', 'UPDATE')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['pk_json', 'created_at']);

            $this->line("  📋 最近5条UPDATE记录:");
            foreach ($recentUpdates as $update) {
                $pk = json_decode($update->pk_json, true);
                $pkStr = $pk ? implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pk), $pk)) : '解析失败';
                $this->line("    {$pkStr} | {$update->created_at}");
            }
        }

        // 检查该表在数据库中的实际记录数
        // 🔧 处理表名映射：sync_change_log中的表名与实际数据库表名可能不一致
        $actualTableName = $this->getActualTableName($tableName);
        if (Schema::hasTable($actualTableName)) {
            $actualRecords = DB::table($actualTableName)->count();
            $this->line("  🗃️  表中实际记录数: {$actualRecords}");
            
            // 计算UPDATE记录与实际记录的比例
            if ($actualRecords > 0 && $updateRecords > 0) {
                $ratio = round($updateRecords / $actualRecords, 2);
                $this->line("  📊 UPDATE/实际记录比例: {$ratio}");
                
                if ($ratio > 10) {
                    $this->error("  🚨 异常：UPDATE记录数是实际记录数的{$ratio}倍！");
                    $this->line("    这表明可能存在频繁的无效更新操作");
                } elseif ($ratio > 2) {
                    $this->warn("  ⚠️  UPDATE记录数较多，可能存在重复更新");
                }
            }
        } else {
            $this->warn("  ⚠️  表 {$actualTableName} 不存在，跳过实际记录数检查");
        }
    }

    /**
     * 分析转化任务执行频率
     */
    private function analyzeTransformTaskFrequency(): void
    {
        $this->newLine();
        $this->info("🔄 分析转化任务执行频率...");

        // 检查队列中的转化任务
        $queueJobs = DB::table('jobs')
            ->where('queue', 'transform')
            ->count();

        $this->line("📦 当前队列中转化任务: {$queueJobs} 个");

        // 检查失败的转化任务
        $failedJobs = DB::table('failed_jobs')
            ->where('payload', 'LIKE', '%TransformDataJob%')
            ->count();

        $this->line("❌ 失败的转化任务: {$failedJobs} 个");

        // 分析最近的转化任务执行情况（如果有相关日志表）
        if (Schema::hasTable('transform_logs')) {
            $recentTransforms = DB::table('transform_logs')
                ->where('created_at', '>=', now()->subHours(24))
                ->select('transform_type', DB::raw('COUNT(*) as count'))
                ->groupBy('transform_type')
                ->orderBy('count', 'desc')
                ->get();

            if ($recentTransforms->count() > 0) {
                $this->line("📈 最近24小时转化任务执行统计:");
                foreach ($recentTransforms as $transform) {
                    $this->line("  {$transform->transform_type}: {$transform->count} 次");
                }
            }
        }

        // 检查是否存在转化任务积压
        if ($queueJobs > 100) {
            $this->error("🚨 转化任务严重积压：{$queueJobs} 个任务待处理");
            $this->line("建议检查：");
            $this->line("1. 队列工作进程是否正常运行");
            $this->line("2. 转化任务是否存在死循环或重复生成");
            $this->line("3. 考虑清理重复的转化任务");
        } elseif ($queueJobs > 20) {
            $this->warn("⚠️  转化任务较多：{$queueJobs} 个，需要关注");
        }
    }

    /**
     * 获取实际的数据库表名
     * 处理sync_change_log中的表名与实际数据库表名的映射关系
     * 
     * @param string $syncChangeLogTableName sync_change_log中记录的表名
     * @return string 实际数据库中的表名
     */
    private function getActualTableName(string $syncChangeLogTableName): string
    {
        // 表名映射：sync_change_log中的表名 -> 实际数据库中的表名
        $tableMapping = [
            'BOM' => 'BOM', // sync_change_log中记录的表名现在已统一为大写'BOM'，与实际表名保持一致
            'categories' => 'categories',
            'customers' => 'customers',
            'material_translations' => 'material_translations',
            'materials' => 'materials'
        ];
        
        return $tableMapping[$syncChangeLogTableName] ?? $syncChangeLogTableName;
    }
} 