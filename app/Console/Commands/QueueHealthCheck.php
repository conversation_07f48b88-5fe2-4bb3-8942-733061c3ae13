<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;
use App\Models\SyncLog;

class QueueHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:health-check 
                          {--fix : 自动修复发现的问题}
                          {--start-worker : 启动新的队列工作进程}
                          {--restart-workers : 重启所有队列工作进程}';

    /**
     * The console command description.
     */
    protected $description = '检查队列系统健康状态并提供修复建议';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 正在检查队列系统健康状态...');
        $this->newLine();

        // 检查待处理任务
        $pendingJobs = DB::table('jobs')->count();
        $this->line("📋 待处理任务: {$pendingJobs} 个");

        // 检查失败任务
        $failedJobs = DB::table('failed_jobs')->count();
        $this->line("❌ 失败任务: {$failedJobs} 个");

        // 检查正在运行的同步任务
        $runningSyncs = SyncLog::where('status', SyncLog::STATUS_RUNNING)->count();
        $this->line("🔄 正在运行的同步: {$runningSyncs} 个");

        // 检查队列工作进程
        $workers = $this->getQueueWorkers();
        $workerCount = count($workers);
        $this->line("👷 队列工作进程: {$workerCount} 个");

        $this->newLine();

        // 显示工作进程详情
        if ($workerCount > 0) {
            $this->info('📊 队列工作进程详情:');
            $headers = ['PID', 'CPU%', 'Memory%', '状态', '启动时间', '运行时长'];
            $rows = [];
            foreach ($workers as $worker) {
                $rows[] = [
                    $worker['pid'],
                    $worker['cpu'],
                    $worker['memory'],
                    $worker['status'],
                    $worker['start_time'],
                    $worker['runtime']
                ];
            }
            $this->table($headers, $rows);
            $this->newLine();
        }

        // 健康状态评估
        $issues = $this->assessHealth($pendingJobs, $workerCount, $failedJobs, $runningSyncs);
        
        if (empty($issues)) {
            $this->info('✅ 队列系统健康状态良好');
            return 0;
        }

        // 显示发现的问题
        $this->warn('⚠️  发现以下问题:');
        foreach ($issues as $issue) {
            $icon = $issue['severity'] === 'critical' ? '🚨' : ($issue['severity'] === 'warning' ? '⚠️' : 'ℹ️');
            $this->line("  {$icon} {$issue['message']}");
        }
        $this->newLine();

        // 处理选项
        if ($this->option('start-worker')) {
            $this->startQueueWorker();
        } elseif ($this->option('restart-workers')) {
            $this->restartQueueWorkers();
        } elseif ($this->option('fix')) {
            $this->autoFix($issues);
        } else {
            $this->info('💡 修复建议:');
            foreach ($issues as $issue) {
                if (!empty($issue['fix'])) {
                    $this->line("  • {$issue['fix']}");
                }
            }
            $this->newLine();
            $this->line('使用 --fix 选项自动修复问题');
            $this->line('使用 --start-worker 启动新的队列工作进程');
            $this->line('使用 --restart-workers 重启所有队列工作进程');
        }

        return 0;
    }

    /**
     * 评估队列健康状态
     */
    private function assessHealth(int $pendingJobs, int $workerCount, int $failedJobs, int $runningSyncs): array
    {
        $issues = [];

        // 检查工作进程
        if ($workerCount === 0) {
            $issues[] = [
                'severity' => 'critical',
                'message' => '没有队列工作进程在运行',
                'fix' => '运行: php artisan queue:work --daemon'
            ];
        } elseif ($workerCount === 1 && $pendingJobs > 5) {
            $issues[] = [
                'severity' => 'warning',
                'message' => '队列任务较多但只有1个工作进程',
                'fix' => '启动更多工作进程提高处理速度'
            ];
        }

        // 检查待处理任务
        if ($pendingJobs > 20) {
            $issues[] = [
                'severity' => 'warning',
                'message' => "待处理任务过多 ({$pendingJobs} 个)",
                'fix' => '检查队列工作进程是否正常运行'
            ];
        } elseif ($pendingJobs > 0 && $workerCount > 0) {
            $issues[] = [
                'severity' => 'info',
                'message' => "有 {$pendingJobs} 个任务正在等待处理",
                'fix' => '任务正在处理中，请稍等'
            ];
        }

        // 检查失败任务
        if ($failedJobs > 0) {
            $issues[] = [
                'severity' => 'info',
                'message' => "有 {$failedJobs} 个失败任务",
                'fix' => '运行: php artisan queue:flush 清理失败任务'
            ];
        }

        // 检查运行中的同步任务
        if ($runningSyncs > 5) {
            $issues[] = [
                'severity' => 'warning',
                'message' => "有 {$runningSyncs} 个同步任务标记为运行中",
                'fix' => '检查是否有僵尸同步任务'
            ];
        }

        return $issues;
    }

    /**
     * 自动修复问题
     */
    private function autoFix(array $issues): void
    {
        $this->info('🔧 正在自动修复发现的问题...');

        foreach ($issues as $issue) {
            if ($issue['severity'] === 'critical') {
                if (str_contains($issue['message'], '没有队列工作进程')) {
                    $this->startQueueWorker();
                }
            }
        }

        // 清理失败任务
        $failedJobs = DB::table('failed_jobs')->count();
        if ($failedJobs > 0) {
            $this->call('queue:flush');
            $this->info("✅ 清理了 {$failedJobs} 个失败任务");
        }

        $this->info('🎉 自动修复完成');
    }

    /**
     * 启动队列工作进程
     */
    private function startQueueWorker(): void
    {
        $this->info('🚀 正在启动队列工作进程...');
        
        $command = 'nohup php artisan queue:work --timeout=3600 --memory=512 --tries=3 --max-jobs=1000 --max-time=3600 > /dev/null 2>&1 & echo $!';
        
        $result = Process::run($command);
        
        if ($result->successful()) {
            $pid = trim($result->output());
            $this->info("✅ 队列工作进程启动成功 (PID: {$pid})");
        } else {
            $this->error('❌ 启动队列工作进程失败: ' . $result->errorOutput());
        }
    }

    /**
     * 重启队列工作进程
     */
    private function restartQueueWorkers(): void
    {
        $this->info('🔄 正在重启队列工作进程...');
        
        $this->call('queue:restart');
        
        sleep(2);
        
        $workers = $this->getQueueWorkers();
        $this->info("✅ 队列工作进程重启完成，当前有 " . count($workers) . " 个工作进程");
    }

    /**
     * 获取队列工作进程信息
     */
    private function getQueueWorkers(): array
    {
        try {
            $result = Process::run('ps aux | grep "queue:work" | grep -v grep');
            
            if (!$result->successful()) {
                return [];
            }
            
            $lines = explode("\n", trim($result->output()));
            $workers = [];
            
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                $parts = preg_split('/\s+/', trim($line));
                if (count($parts) >= 2) {
                    $workers[] = [
                        'user' => $parts[0],
                        'pid' => $parts[1],
                        'cpu' => $parts[2] ?? 'N/A',
                        'memory' => $parts[3] ?? 'N/A',
                        'status' => $parts[7] ?? 'N/A',
                        'start_time' => $parts[8] ?? 'N/A',
                        'runtime' => $parts[9] ?? 'N/A',
                    ];
                }
            }
            
            return $workers;
            
        } catch (\Exception $e) {
            return [];
        }
    }
} 