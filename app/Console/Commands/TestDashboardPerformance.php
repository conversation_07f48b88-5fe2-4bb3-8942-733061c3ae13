<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TestDashboardPerformance extends Command
{
    protected $signature = 'test:dashboard-performance {--iterations=5 : 测试迭代次数}';
    protected $description = '测试Dashboard API接口的性能';

    public function handle(): int
    {
        $iterations = (int) $this->option('iterations');
        
        $this->info("🚀 开始测试Dashboard API性能 (迭代次数: {$iterations})");
        $this->newLine();
        
        // 测试的API端点
        $endpoints = [
            'stats' => '/api/dashboard/stats',
            'table-data-counts' => '/api/dashboard/table-data-counts',
            'sync-records-24h' => '/api/dashboard/sync-records-24h?limit=50',
        ];
        
        $results = [];
        
        foreach ($endpoints as $name => $endpoint) {
            $this->info("📊 测试 {$name} 接口...");
            
            $times = [];
            $errors = 0;
            
            for ($i = 1; $i <= $iterations; $i++) {
                $startTime = microtime(true);
                
                try {
                    // 直接调用控制器方法而不是HTTP请求
                    $response = $this->callControllerMethod($name);
                    $endTime = microtime(true);
                    
                    // 检查响应是否成功（不同接口的成功标识不同）
                    $isSuccess = false;
                    if (isset($response['success']) && $response['success']) {
                        $isSuccess = true;
                    } elseif ($name === 'stats' && isset($response['stats'])) {
                        $isSuccess = true; // stats接口没有success字段，但有stats数据说明成功
                    }
                    
                    if ($isSuccess) {
                        $times[] = ($endTime - $startTime) * 1000; // 转换为毫秒
                        $this->line("  第{$i}次: " . number_format(($endTime - $startTime) * 1000, 2) . "ms");
                    } else {
                        $errors++;
                        $this->error("  第{$i}次: 请求失败");
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("  第{$i}次: " . $e->getMessage());
                    if ($this->output->isVerbose()) {
                        $this->line("    详细错误: " . $e->getTraceAsString());
                    }
                }
            }
            
            if (!empty($times)) {
                $results[$name] = [
                    'avg_time' => array_sum($times) / count($times),
                    'min_time' => min($times),
                    'max_time' => max($times),
                    'success_count' => count($times),
                    'error_count' => $errors
                ];
            }
            
            $this->newLine();
        }
        
        // 显示汇总结果
        $this->info("📈 性能测试结果汇总:");
        $this->table(
            ['接口', '平均响应时间', '最快响应', '最慢响应', '成功次数', '失败次数'],
            array_map(function ($name, $result) {
                return [
                    $name,
                    number_format($result['avg_time'], 2) . 'ms',
                    number_format($result['min_time'], 2) . 'ms',
                    number_format($result['max_time'], 2) . 'ms',
                    $result['success_count'],
                    $result['error_count']
                ];
            }, array_keys($results), $results)
        );
        
        // 显示优化建议
        $this->showOptimizationSuggestions($results);
        
        return 0;
    }
    
    private function callControllerMethod(string $name): array
    {
        $controller = app(\App\Http\Controllers\DashboardController::class);
        
        switch ($name) {
            case 'stats':
                $response = $controller->getSyncStats();
                break;
            case 'table-data-counts':
                $response = $controller->getTableDataCounts();
                break;
            case 'sync-records-24h':
                $request = new \Illuminate\Http\Request(['limit' => 50]);
                $response = $controller->getSyncRecords24h($request);
                break;
            default:
                throw new \Exception("未知的接口: {$name}");
        }
        
        return json_decode($response->getContent(), true);
    }
    
    private function showOptimizationSuggestions(array $results): void
    {
        $this->newLine();
        $this->info("💡 优化建议:");
        
        foreach ($results as $name => $result) {
            if ($result['avg_time'] > 1000) {
                $this->warn("⚠️  {$name} 接口平均响应时间超过1秒，建议进一步优化");
            } elseif ($result['avg_time'] > 500) {
                $this->comment("⚡ {$name} 接口响应时间可以进一步优化");
            } else {
                $this->info("✅ {$name} 接口性能良好");
            }
        }
        
        $this->newLine();
        $this->info("🔧 已实施的优化措施:");
        $this->line("  • 减少数据库查询次数 (批量查询)");
        $this->line("  • 添加数据库索引优化");
        $this->line("  • 实现缓存机制 (table-data-counts)");
        $this->line("  • 分页查询减少数据传输");
        $this->line("  • 只选择必要字段减少IO");
    }
} 