<?php

namespace App\Console\Commands;

use App\Services\DataSyncService;
use App\Services\DataTransformService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestMultiTableTransform extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:multi-table-transform 
                           {--type= : 测试的转化类型 (customer|material|all)}
                           {--integrity : 只进行数据完整性检查}
                           {--locale=zh_CN : 语言代码}';

    /**
     * The console command description.
     */
    protected $description = '测试多表关联数据转化的优化功能';

    private DataTransformService $transformService;
    private DataSyncService $syncService;

    public function __construct(DataTransformService $transformService, DataSyncService $syncService)
    {
        parent::__construct();
        $this->transformService = $transformService;
        $this->syncService = $syncService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type') ?? 'all';
        $locale = $this->option('locale');
        $integrityOnly = $this->option('integrity');

        $this->info("🧪 开始测试多表关联数据转化优化");
        $this->info("测试类型: {$type}");
        $this->info("语言代码: {$locale}");
        
        if ($integrityOnly) {
            return $this->testDataIntegrity($type, $locale);
        }

        // 检查表名大小写
        $this->testTableCasing();

        // 测试数据完整性检查
        $this->testDataIntegrity($type, $locale);

        // 测试转化延迟配置
        $this->testTransformDelayConfig();

        // 测试自动转化逻辑
        if (!$integrityOnly) {
            $this->testAutoTransformLogic($type);
        }

        $this->info("✅ 多表关联数据转化测试完成");
        return 0;
    }

    /**
     * 测试表名大小写
     */
    private function testTableCasing(): void
    {
        $this->info("\n📝 测试表名大小写规范...");
        
        $tables = ['RTAXL_T', 'PMAB_T', 'PMAAL_T', 'BMBA_T', 'IMAA_T', 'IMAAL_T', 'IMAF_T', 'BMAA_T'];
        
        foreach ($tables as $table) {
            try {
                $count = \DB::table($table)->count();
                $this->line("✓ {$table}: {$count} 条记录");
            } catch (\Exception $e) {
                $this->error("✗ {$table}: {$e->getMessage()}");
            }
        }
    }

    /**
     * 测试数据完整性检查
     */
    private function testDataIntegrity(string $type, string $locale): int
    {
        $this->info("\n🔍 测试数据完整性检查...");

        if ($type === 'customer' || $type === 'all') {
            $this->info("\n检查客户数据表完整性:");
            $result = $this->callPrivateMethod($this->transformService, 'checkCustomerTablesIntegrity', [$locale]);
            $this->displayIntegrityResult($result, 'customer');
        }

        if ($type === 'material' || $type === 'all') {
            $this->info("\n检查物料数据表完整性:");
            $result = $this->callPrivateMethod($this->transformService, 'checkMaterialTablesIntegrity', [$locale]);
            $this->displayIntegrityResult($result, 'material');
        }

        return 0;
    }

    /**
     * 显示完整性检查结果
     */
    private function displayIntegrityResult(array $result, string $type): void
    {
        if ($result['ready']) {
            $this->info("✅ {$type} 数据表完整性检查通过");
        } else {
            $this->warn("⚠️ {$type} 数据表完整性检查未通过");
        }

        if (isset($result['tables'])) {
            foreach ($result['tables'] as $table => $info) {
                $status = $info['status'] === 'ok' ? '✓' : '✗';
                $this->line("  {$status} {$table}: {$info['count']} 条记录 ({$info['status']})");
            }
        }

        if (!empty($result['warnings'])) {
            foreach ($result['warnings'] as $warning) {
                $this->warn("  ⚠️ {$warning}");
            }
        }

        if (isset($result['error'])) {
            $this->error("  ❌ 错误: {$result['error']}");
        }
    }

    /**
     * 测试转化延迟配置
     */
    private function testTransformDelayConfig(): void
    {
        $this->info("\n⏰ 测试转化延迟配置...");

        $typeDelays = config('sync.auto_transform.type_delays', []);
        $relatedTables = config('sync.auto_transform.related_tables', []);

        $this->table(
            ['转化类型', '延迟时间(秒)', '关联表数量', '关联表'],
            [
                ['category', $typeDelays['category'] ?? 10, 1, 'RTAXL_T'],
                ['bom', $typeDelays['bom'] ?? 10, 1, 'BMBA_T'],
                ['customer', $typeDelays['customer'] ?? 60, count($relatedTables['customer'] ?? []), implode(', ', $relatedTables['customer'] ?? [])],
                ['material', $typeDelays['material'] ?? 120, count($relatedTables['material'] ?? []), implode(', ', $relatedTables['material'] ?? [])],
            ]
        );

        // 测试智能延迟计算
        $this->info("\n测试智能延迟计算:");
        foreach (['customer', 'material'] as $transformType) {
            $delay = $this->callPrivateMethod($this->syncService, 'calculateTransformDelay', [$transformType, 'TEST_TABLE']);
            $this->line("  {$transformType}: {$delay}秒");
        }
    }

    /**
     * 测试自动转化逻辑
     */
    private function testAutoTransformLogic(string $type): void
    {
        $this->info("\n🔄 测试自动转化逻辑...");

        // 模拟同步结果
        $mockSyncResult = [
            'success' => true,
            'records_processed' => 100,
            'records_inserted' => 50,
            'records_updated' => 50,
            'records_deleted' => 0
        ];

        if ($type === 'customer' || $type === 'all') {
            $this->info("\n测试客户转化逻辑:");
            $this->testTransformForTables(['PMAB_T', 'PMAAL_T'], $mockSyncResult);
        }

        if ($type === 'material' || $type === 'all') {
            $this->info("\n测试物料转化逻辑:");
            $this->testTransformForTables(['IMAA_T', 'IMAAL_T', 'IMAF_T', 'BMAA_T'], $mockSyncResult);
        }
    }

    /**
     * 测试指定表的转化逻辑
     */
    private function testTransformForTables(array $tables, array $syncResult): void
    {
        foreach ($tables as $table) {
            $this->line("  模拟 {$table} 同步完成...");
            
            // 检查是否会触发转化
            $mapping = $this->callPrivateMethod($this->syncService, 'getTableTransformMapping');
            if (isset($mapping[$table])) {
                $transformTypes = $mapping[$table];
                $this->line("    → 将触发转化类型: " . implode(', ', $transformTypes));
                
                foreach ($transformTypes as $transformType) {
                    // 测试去重检查
                    $isDuplicate = $this->callPrivateMethod($this->syncService, 'isDuplicateTransformTask', [$transformType, 'TB']);
                    $duplicateStatus = $isDuplicate ? '重复(跳过)' : '不重复(执行)';
                    $this->line("    → {$transformType} 去重检查: {$duplicateStatus}");
                    
                    // 测试延迟计算
                    $delay = $this->callPrivateMethod($this->syncService, 'calculateTransformDelay', [$transformType, $table]);
                    $this->line("    → {$transformType} 延迟时间: {$delay}秒");
                }
            } else {
                $this->line("    → 无需转化");
            }
        }
    }

    /**
     * 调用私有方法的辅助函数
     */
    private function callPrivateMethod(object $object, string $method, array $args = [])
    {
        $reflection = new \ReflectionClass($object);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $args);
    }
} 