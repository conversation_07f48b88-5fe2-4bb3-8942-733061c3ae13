<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DatabaseService;
use Exception;

class TestOracleConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:test-oracle {--connection=oracle : 数据库连接名称}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试Oracle数据库连接';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $connection = $this->option('connection');
        $databaseService = new DatabaseService();
        
        $this->info("正在测试Oracle数据库连接: {$connection}");
        $this->newLine();

        // 使用DatabaseService测试连接
        $result = $databaseService->testConnection($connection);
        
        if ($result['success']) {
            $this->info('✅ 数据库连接测试成功！');
            $this->newLine();
            
            // 显示连接信息
            $this->displayConnectionInfo($result);
            
            // 显示数据库详细信息
            $this->displayDatabaseInfo($result['info']);
            
            return Command::SUCCESS;
        } else {
            $this->error('❌ 数据库连接测试失败！');
            $this->error('错误信息: ' . $result['error']);
            
            return Command::FAILURE;
        }
    }

    /**
     * 显示连接信息
     */
    private function displayConnectionInfo(array $result): void
    {
        $this->info('连接信息:');
        $this->info('  连接名称: ' . $result['connection']);
        $this->info('  数据库类型: ' . $result['driver']);
        $this->info('  主机地址: ' . $result['host']);
        $this->info('  数据库名: ' . $result['database']);
        $this->newLine();
    }

    /**
     * 显示数据库详细信息
     */
    private function displayDatabaseInfo(array $info): void
    {
        if (empty($info)) {
            return;
        }
        
        $this->info('数据库详细信息:');
        
        if (isset($info['version'])) {
            $this->info('  版本: ' . $info['version']);
        }
        
        if (isset($info['current_user'])) {
            $this->info('  当前用户: ' . $info['current_user']);
        }
        
        if (isset($info['server_time'])) {
            $this->info('  服务器时间: ' . $info['server_time']);
        }
        
        if (isset($info['query_test'])) {
            $this->info('  查询测试: ' . $info['query_test']);
        }
        
        if (isset($info['error'])) {
            $this->warn('  错误信息: ' . $info['error']);
        }
    }
} 