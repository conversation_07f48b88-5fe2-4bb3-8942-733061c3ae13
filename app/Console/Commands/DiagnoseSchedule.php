<?php

namespace App\Console\Commands;

use App\Models\SyncLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DiagnoseSchedule extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'schedule:diagnose 
                           {--check-last-run : 检查最后一次调度运行时间}
                           {--test-sync : 测试同步功能}
                           {--detail : 显示详细信息}';

    /**
     * The console command description.
     */
    protected $description = '诊断调度任务和同步功能';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info("🔧 Laravel调度任务诊断工具");
        $this->info("执行时间: " . now()->format('Y-m-d H:i:s'));
        $this->info("时区: " . config('app.timezone'));
        
        // 环境信息
        $this->checkEnvironment();
        
        // 检查调度配置
        $this->checkScheduleConfig();
        
        // 检查最后运行时间
        if ($this->option('check-last-run')) {
            $this->checkLastRun();
        }
        
        // 检查数据库连接
        $this->checkDatabaseConnections();
        
        // 检查同步记录
        $this->checkSyncLogs();
        
        // 测试同步功能
        if ($this->option('test-sync')) {
            $this->testSyncFunction();
        }
        
        // 检查文件权限
        $this->checkFilePermissions();
        
        $this->info("\n✅ 诊断完成");
        
        return 0;
    }

    private function checkEnvironment(): void
    {
        $this->info("\n📋 环境信息:");
        $this->table(['项目', '值'], [
            ['PHP版本', PHP_VERSION],
            ['Laravel版本', app()->version()],
            ['应用环境', config('app.env')],
            ['调试模式', config('app.debug') ? '开启' : '关闭'],
            ['当前用户', get_current_user()],
            ['工作目录', getcwd()],
            ['artisan路径', base_path('artisan')],
            ['存储路径可写', is_writable(storage_path()) ? '是' : '否'],
        ]);
    }

    private function checkScheduleConfig(): void
    {
        $this->info("\n📅 调度配置检查:");
        
        // 检查调度任务列表
        $this->call('schedule:list');
        
        // 检查cron配置提示
        $this->info("\n🕒 Cron配置提示:");
        $this->info("确保服务器crontab中有以下配置:");
        $this->warn("* * * * * docker exec erp_php php /var/www/html/artisan schedule:run >> /var/www/html/storage/logs/cron.log 2>&1");
        
        $this->info("\n💡 注意事项:");
        $this->info("1. Docker环境中路径: /var/www/html/artisan");
        $this->info("2. 本地开发路径: " . base_path('artisan'));
        $this->info("3. 确保容器名称正确: erp_php");
        $this->info("4. 确保日志目录有写权限");
    }

    private function checkLastRun(): void
    {
        $this->info("\n⏰ 调度执行检查:");
        
        // 检查Laravel调度缓存
        $lastScheduleRun = Cache::get('illuminate:schedule:run');
        if ($lastScheduleRun) {
            $this->info("最后调度运行时间: " . date('Y-m-d H:i:s', $lastScheduleRun));
        } else {
            $this->warn("未找到调度运行记录");
        }
        
        // 检查调度互斥锁
        $cacheDir = storage_path('framework/cache');
        $this->info("缓存目录: " . $cacheDir);
        
        // 检查是否有正在运行的调度任务
        $runningSchedules = glob(storage_path('framework/schedule-*'));
        if (!empty($runningSchedules)) {
            $this->info("正在运行的调度任务: " . count($runningSchedules));
            if ($this->option('detail')) {
                foreach ($runningSchedules as $schedule) {
                    $this->line("  - " . basename($schedule));
                }
            }
        } else {
            $this->info("没有正在运行的调度任务");
        }
    }

    private function checkDatabaseConnections(): void
    {
        $this->info("\n💾 数据库连接检查:");
        
        // 检查MySQL连接
        try {
            DB::connection('mysql')->getPdo();
            $this->info("✅ MySQL连接正常");
        } catch (\Exception $e) {
            $this->error("❌ MySQL连接失败: " . $e->getMessage());
        }
        
        // 检查Oracle连接
        try {
            DB::connection('oracle')->getPdo();
            $this->info("✅ Oracle连接正常");
        } catch (\Exception $e) {
            $this->error("❌ Oracle连接失败: " . $e->getMessage());
        }
    }

    private function checkSyncLogs(): void
    {
        $this->info("\n📊 同步记录统计:");
        
        try {
            // 最近24小时的同步记录
            $recent24h = SyncLog::where('created_at', '>=', now()->subDay())->count();
            $this->info("最近24小时同步记录: {$recent24h} 条");
            
            // 最近1小时的同步记录
            $recent1h = SyncLog::where('created_at', '>=', now()->subHour())->count();
            $this->info("最近1小时同步记录: {$recent1h} 条");
            
            // 最新的几条记录
            $latestLogs = SyncLog::latest()
                ->limit(5)
                ->get(['table_name', 'sync_type', 'status', 'start_time', 'records_processed']);
                
            if ($latestLogs->isNotEmpty()) {
                $this->info("\n最近5条同步记录:");
                $this->table(
                    ['表名', '类型', '状态', '开始时间', '处理记录数'],
                    $latestLogs->map(function ($log) {
                        return [
                            $log->table_name,
                            $log->sync_type,
                            $log->status,
                            $log->start_time?->format('m-d H:i:s'),
                            $log->records_processed ?? 0
                        ];
                    })->toArray()
                );
            } else {
                $this->warn("没有找到同步记录");
            }
            
            // 统计各状态的记录数
            $statusStats = SyncLog::selectRaw('status, count(*) as count')
                ->where('created_at', '>=', now()->subWeek())
                ->groupBy('status')
                ->get();
                
            if ($statusStats->isNotEmpty()) {
                $this->info("\n最近一周同步状态统计:");
                $this->table(['状态', '数量'], $statusStats->toArray());
            }
            
        } catch (\Exception $e) {
            $this->error("查询同步记录失败: " . $e->getMessage());
        }
    }

    private function testSyncFunction(): void
    {
        $this->info("\n🧪 测试同步功能:");
        
        try {
            // 测试简单的同步命令
            $this->info("执行测试同步命令...");
            $exitCode = $this->call('oracle:sync', [
                '--table' => 'RTAXL_T',
                '--type' => 'incremental'
            ]);
            
            if ($exitCode === 0) {
                $this->info("✅ 同步命令执行成功");
            } else {
                $this->warn("⚠️ 同步命令返回非零退出码: {$exitCode}");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ 同步功能测试失败: " . $e->getMessage());
        }
    }

    private function checkFilePermissions(): void
    {
        $this->info("\n📁 文件权限检查:");
        
        $paths = [
            'storage/logs' => storage_path('logs'),
            'storage/framework' => storage_path('framework'),
            'bootstrap/cache' => base_path('bootstrap/cache'),
        ];
        
        foreach ($paths as $name => $path) {
            $writable = is_writable($path);
            $perms = fileperms($path);
            $this->info("{$name}: " . ($writable ? '✅ 可写' : '❌ 不可写') . " (权限: " . substr(sprintf('%o', $perms), -4) . ")");
        }
        
        // 检查日志文件
        $logFiles = [
            'laravel.log' => storage_path('logs/laravel.log'),
            'sync.log' => storage_path('logs/sync.log'),
            'cron.log' => storage_path('logs/cron.log'),
        ];
        
        $this->info("\n日志文件状态:");
        foreach ($logFiles as $name => $path) {
            if (file_exists($path)) {
                $size = filesize($path);
                $modified = filemtime($path);
                $this->info("{$name}: 存在 (大小: {$size} bytes, 修改时间: " . date('Y-m-d H:i:s', $modified) . ")");
            } else {
                $this->warn("{$name}: 不存在");
            }
        }
    }
} 