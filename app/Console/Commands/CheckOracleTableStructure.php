<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\DatabaseService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CheckOracleTableStructure extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:check-tables 
                            {--table= : 指定要检查的表名，不指定则检查所有配置的表}';

    /**
     * The console command description.
     */
    protected $description = '检查Oracle表的结构信息';

    private DatabaseService $databaseService;

    public function __construct(DatabaseService $databaseService)
    {
        parent::__construct();
        $this->databaseService = $databaseService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tableName = $this->option('table');
        
        $this->info("🔍 检查Oracle表结构...");
        $this->newLine();

        // 检查Oracle连接
        $oracleTest = $this->databaseService->testConnection('oracle');
        if (!$oracleTest['success']) {
            $this->error("❌ Oracle数据库连接失败: " . $oracleTest['error']);
            return Command::FAILURE;
        }

        $this->info("✅ Oracle数据库连接成功");
        $this->newLine();

        try {
            if ($tableName) {
                $this->checkSingleTable($tableName);
            } else {
                $this->checkAllConfiguredTables();
            }

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error("❌ 检查表结构时发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 检查单个表
     */
    private function checkSingleTable(string $tableName): void
    {
        $this->info("📋 检查表: {$tableName}");
        
        if (!$this->tableExists($tableName)) {
            $this->error("❌ 表 {$tableName} 不存在");
            return;
        }

        $this->displayTableStructure($tableName);
        $this->displayTableSample($tableName);
        $this->suggestConfiguration($tableName);
    }

    /**
     * 检查所有配置的表
     */
    private function checkAllConfiguredTables(): void
    {
        $configuredTables = array_keys(config('sync.tables', []));
        
        if (empty($configuredTables)) {
            $this->warn("⚠️  没有配置需要检查的表");
            return;
        }

        $this->info("📋 检查配置的表: " . implode(', ', $configuredTables));
        $this->newLine();

        foreach ($configuredTables as $tableName) {
            $this->info("=" . str_repeat("=", 50));
            $this->checkSingleTable($tableName);
            $this->newLine();
        }

        // 生成建议的配置
        $this->generateSuggestedConfig($configuredTables);
    }

    /**
     * 检查表是否存在
     */
    private function tableExists(string $tableName): bool
    {
        try {
            $result = DB::connection('oracle')->select("
                SELECT COUNT(*) as count 
                FROM USER_TABLES 
                WHERE TABLE_NAME = ?
            ", [strtoupper($tableName)]);

            return ($result[0]->count ?? 0) > 0;

        } catch (Exception $e) {
            $this->error("检查表存在性时出错: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 显示表结构
     */
    private function displayTableStructure(string $tableName): void
    {
        try {
            $columns = DB::connection('oracle')->select("
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    DATA_LENGTH,
                    DATA_PRECISION,
                    DATA_SCALE,
                    NULLABLE,
                    COLUMN_ID
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = ? 
                ORDER BY COLUMN_ID
            ", [strtoupper($tableName)]);

            if (empty($columns)) {
                $this->error("❌ 无法获取表 {$tableName} 的结构信息");
                return;
            }

            $this->info("📊 表结构:");
            $tableData = [];
            
            foreach ($columns as $column) {
                $dataType = $column->data_type;
                if ($column->data_precision && $column->data_scale) {
                    $dataType .= "({$column->data_precision},{$column->data_scale})";
                } elseif ($column->data_length && in_array($column->data_type, ['VARCHAR2', 'CHAR'])) {
                    $dataType .= "({$column->data_length})";
                }

                $tableData[] = [
                    $column->column_id,
                    $column->column_name,
                    $dataType,
                    $column->nullable === 'Y' ? '是' : '否'
                ];
            }

            $this->table(['序号', '字段名', '数据类型', '可空'], $tableData);

        } catch (Exception $e) {
            $this->error("❌ 获取表结构失败: " . $e->getMessage());
        }
    }

    /**
     * 显示表数据样例
     */
    private function displayTableSample(string $tableName): void
    {
        try {
            $this->info("📄 数据样例 (前3条记录):");
            
            $sampleData = DB::connection('oracle')->select("
                SELECT * FROM {$tableName} WHERE ROWNUM <= 3
            ");

            if (empty($sampleData)) {
                $this->warn("⚠️  表 {$tableName} 中没有数据");
                return;
            }

            // 获取字段名
            $firstRow = (array) $sampleData[0];
            $headers = array_keys($firstRow);

            // 准备表格数据
            $tableData = [];
            foreach ($sampleData as $row) {
                $rowArray = (array) $row;
                $tableData[] = array_map(function($value) {
                    if (is_null($value)) return 'NULL';
                    if (is_string($value) && strlen($value) > 20) {
                        return substr($value, 0, 17) . '...';
                    }
                    return $value;
                }, array_values($rowArray));
            }

            $this->table($headers, $tableData);

        } catch (Exception $e) {
            $this->error("❌ 获取数据样例失败: " . $e->getMessage());
        }
    }

    /**
     * 建议配置
     */
    private function suggestConfiguration(string $tableName): void
    {
        try {
            $this->info("💡 配置建议:");

            // 查找可能的主键字段
            $primaryKeyFields = $this->findPossiblePrimaryKeys($tableName);
            
            // 查找可能的时间戳字段
            $timestampFields = $this->findPossibleTimestampFields($tableName);

            if (!empty($primaryKeyFields)) {
                $this->info("  🔑 建议的主键字段: " . implode(', ', $primaryKeyFields));
            } else {
                $this->warn("  ⚠️  未找到明显的主键字段，可能需要使用ROWID");
            }

            if (!empty($timestampFields)) {
                $this->info("  🕐 建议的时间戳字段: " . implode(', ', $timestampFields));
            } else {
                $this->warn("  ⚠️  未找到时间戳字段，建议使用全量同步");
            }

        } catch (Exception $e) {
            $this->error("❌ 生成配置建议失败: " . $e->getMessage());
        }
    }

    /**
     * 查找可能的主键字段
     */
    private function findPossiblePrimaryKeys(string $tableName): array
    {
        try {
            // 首先查找实际的主键约束
            $primaryKeys = DB::connection('oracle')->select("
                SELECT cc.COLUMN_NAME
                FROM USER_CONSTRAINTS c
                JOIN USER_CONS_COLUMNS cc ON c.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                WHERE c.TABLE_NAME = ? AND c.CONSTRAINT_TYPE = 'P'
                ORDER BY cc.POSITION
            ", [strtoupper($tableName)]);

            if (!empty($primaryKeys)) {
                return array_map(fn($pk) => $pk->column_name, $primaryKeys);
            }

            // 如果没有主键约束，查找可能的主键字段
            $possibleKeys = DB::connection('oracle')->select("
                SELECT COLUMN_NAME
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = ? 
                AND (
                    COLUMN_NAME LIKE '%ID' 
                    OR COLUMN_NAME LIKE '%KEY' 
                    OR COLUMN_NAME LIKE '%NO'
                    OR COLUMN_NAME LIKE '%CODE'
                )
                AND NULLABLE = 'N'
                ORDER BY COLUMN_ID
            ", [strtoupper($tableName)]);

            return array_map(fn($col) => $col->column_name, $possibleKeys);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 查找可能的时间戳字段
     */
    private function findPossibleTimestampFields(string $tableName): array
    {
        try {
            $timestampFields = DB::connection('oracle')->select("
                SELECT COLUMN_NAME
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = ? 
                AND (
                    DATA_TYPE IN ('DATE', 'TIMESTAMP')
                    OR (
                        DATA_TYPE = 'VARCHAR2' 
                        AND (
                            COLUMN_NAME LIKE '%TIME%' 
                            OR COLUMN_NAME LIKE '%DATE%'
                            OR COLUMN_NAME LIKE '%UPDATED%'
                            OR COLUMN_NAME LIKE '%MODIFIED%'
                            OR COLUMN_NAME LIKE '%CREATED%'
                        )
                    )
                )
                ORDER BY COLUMN_ID
            ", [strtoupper($tableName)]);

            return array_map(fn($col) => $col->column_name, $timestampFields);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 生成建议的配置
     */
    private function generateSuggestedConfig(array $tableNames): void
    {
        $this->info("🔧 生成建议的配置文件内容:");
        $this->newLine();

        $config = [];
        
        foreach ($tableNames as $tableName) {
            if (!$this->tableExists($tableName)) {
                continue;
            }

            $primaryKeys = $this->findPossiblePrimaryKeys($tableName);
            $timestampFields = $this->findPossibleTimestampFields($tableName);

            $primaryKey = !empty($primaryKeys) ? $primaryKeys[0] : 'ROWID';
            $timestampField = !empty($timestampFields) ? $timestampFields[0] : null;

            $config[$tableName] = [
                'primary_key' => $primaryKey,
                'timestamp_field' => $timestampField,
                'batch_size' => 1000,
                'enabled' => true,
            ];
        }

        // 输出PHP配置格式
        $this->line("// 建议的 config/sync.php 表配置:");
        $this->line("'tables' => [");
        
        foreach ($config as $tableName => $tableConfig) {
            $this->line("    '{$tableName}' => [");
            $this->line("        'primary_key' => '{$tableConfig['primary_key']}',");
            
            if ($tableConfig['timestamp_field']) {
                $this->line("        'timestamp_field' => '{$tableConfig['timestamp_field']}',");
            } else {
                $this->line("        'timestamp_field' => null, // 无时间戳字段，建议使用全量同步");
            }
            
            $this->line("        'batch_size' => {$tableConfig['batch_size']},");
            $this->line("        'enabled' => " . ($tableConfig['enabled'] ? 'true' : 'false') . ",");
            $this->line("    ],");
        }
        
        $this->line("],");
    }
} 