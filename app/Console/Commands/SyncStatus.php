<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\DataSyncService;
use App\Models\SyncLog;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SyncStatus extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:sync-status 
                            {--table= : 指定要查看的表名，不指定则查看所有表}
                            {--limit=10 : 显示的记录数量限制}
                            {--status= : 过滤特定状态的记录}';

    /**
     * The console command description.
     */
    protected $description = '查看Oracle数据同步状态和历史记录';

    private DataSyncService $dataSyncService;

    public function __construct(DataSyncService $dataSyncService)
    {
        parent::__construct();
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tableName = $this->option('table');
        $limit = (int) $this->option('limit');
        $status = $this->option('status');

        if ($tableName) {
            $this->showTableStatus($tableName, $limit, $status);
        } else {
            $this->showOverallStatus($limit, $status);
        }

        return Command::SUCCESS;
    }

    /**
     * 显示指定表的同步状态
     */
    private function showTableStatus(string $tableName, int $limit, ?string $status): void
    {
        $this->info("📊 表 {$tableName} 的同步状态");
        $this->newLine();

        // 获取最新同步记录
        $latestSync = SyncLog::getLatestSync($tableName);
        if ($latestSync) {
            $this->info("🕐 最新同步信息:");
            $this->displaySyncInfo($latestSync);
            $this->newLine();
        } else {
            $this->warn("⚠️  该表尚未进行过同步");
            return;
        }

        // 获取历史记录
        $query = SyncLog::where('table_name', $tableName);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        $syncLogs = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        if ($syncLogs->isNotEmpty()) {
            $this->info("📋 最近 {$limit} 条同步记录:");
            $this->displaySyncHistory($syncLogs);
        }

        // 显示统计信息
        $this->displayTableStats($tableName);
    }

    /**
     * 显示整体同步状态
     */
    private function showOverallStatus(int $limit, ?string $status): void
    {
        $this->info("📊 Oracle数据同步整体状态");
        $this->newLine();

        // 获取同步统计信息
        $stats = $this->dataSyncService->getSyncStats();
        
        if (empty($stats)) {
            $this->warn("⚠️  尚未进行过任何同步操作");
            return;
        }

        // 显示各表的最新状态
        $this->info("📋 各表最新同步状态:");
        $tableData = [];
        
        foreach ($stats as $table => $stat) {
            $latestSync = $stat['latest_sync'];
            if ($latestSync) {
                $tableData[] = [
                    $table,
                    $this->getStatusIcon($latestSync->status) . ' ' . $latestSync->status_label,
                    $latestSync->sync_type_label,
                    $latestSync->records_processed ?? 0,
                    $latestSync->created_at->format('Y-m-d H:i:s'),
                    $latestSync->duration ? $latestSync->duration . 's' : 'N/A'
                ];
            } else {
                $tableData[] = [
                    $table,
                    '⚪ 未同步',
                    'N/A',
                    0,
                    'N/A',
                    'N/A'
                ];
            }
        }

        $this->table([
            '表名',
            '状态',
            '同步类型',
            '处理记录数',
            '最后同步时间',
            '耗时'
        ], $tableData);

        $this->newLine();

        // 显示汇总统计
        $this->displayOverallStats($stats);

        // 显示最近的同步记录
        if (!$status || in_array($status, [SyncLog::STATUS_SUCCESS, SyncLog::STATUS_FAILED, SyncLog::STATUS_RUNNING])) {
            $this->newLine();
            $this->info("📋 最近 {$limit} 条同步记录:");
            
            $query = SyncLog::query();
            if ($status) {
                $query->where('status', $status);
            }
            
            $recentLogs = $query->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
                
            $this->displaySyncHistory($recentLogs);
        }
    }

    /**
     * 显示同步信息
     */
    private function displaySyncInfo(SyncLog $syncLog): void
    {
        $this->table(['属性', '值'], [
            ['状态', $this->getStatusIcon($syncLog->status) . ' ' . $syncLog->status_label],
            ['同步类型', $syncLog->sync_type_label],
            ['开始时间', $syncLog->start_time?->format('Y-m-d H:i:s') ?? 'N/A'],
            ['结束时间', $syncLog->end_time?->format('Y-m-d H:i:s') ?? 'N/A'],
            ['耗时', $syncLog->duration ? $syncLog->duration . ' 秒' : 'N/A'],
            ['处理记录数', $syncLog->records_processed ?? 0],
            ['新增记录数', $syncLog->records_inserted ?? 0],
            ['更新记录数', $syncLog->records_updated ?? 0],
            ['删除记录数', $syncLog->records_deleted ?? 0],
            ['错误信息', $syncLog->error_message ?? '无']
        ]);
    }

    /**
     * 显示同步历史记录
     */
    private function displaySyncHistory($syncLogs): void
    {
        $tableData = [];
        
        foreach ($syncLogs as $log) {
            $tableData[] = [
                $log->table_name,
                $this->getStatusIcon($log->status) . ' ' . $log->status_label,
                $log->sync_type_label,
                $log->records_processed ?? 0,
                $log->created_at->format('Y-m-d H:i:s'),
                $log->duration ? $log->duration . 's' : 'N/A',
                $log->error_message ? substr($log->error_message, 0, 30) . '...' : ''
            ];
        }

        $this->table([
            '表名',
            '状态',
            '同步类型',
            '处理记录数',
            '同步时间',
            '耗时',
            '错误信息'
        ], $tableData);
    }

    /**
     * 显示表统计信息
     */
    private function displayTableStats(string $tableName): void
    {
        $this->newLine();
        $this->info("📈 统计信息:");

        $totalSyncs = SyncLog::where('table_name', $tableName)->count();
        $successfulSyncs = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_SUCCESS)
            ->count();
        $failedSyncs = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_FAILED)
            ->count();
        $successRate = $totalSyncs > 0 ? round(($successfulSyncs / $totalSyncs) * 100, 2) : 0;

        $avgProcessed = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_SUCCESS)
            ->avg('records_processed') ?? 0;

        $totalProcessed = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_SUCCESS)
            ->sum('records_processed') ?? 0;

        $this->table(['指标', '值'], [
            ['总同步次数', $totalSyncs],
            ['成功次数', $successfulSyncs],
            ['失败次数', $failedSyncs],
            ['成功率', $successRate . '%'],
            ['平均处理记录数', round((float) $avgProcessed)],
            ['累计处理记录数', $totalProcessed]
        ]);
    }

    /**
     * 显示整体统计信息
     */
    private function displayOverallStats(array $stats): void
    {
        $this->info("📈 整体统计信息:");

        $totalTables = count($stats);
        $syncedTables = 0;
        $totalSyncs = 0;
        $totalSuccessRate = 0;

        foreach ($stats as $stat) {
            if ($stat['latest_sync']) {
                $syncedTables++;
            }
            $totalSyncs += $stat['total_syncs'];
            $totalSuccessRate += $stat['success_rate'];
        }

        $avgSuccessRate = $totalTables > 0 ? round($totalSuccessRate / $totalTables, 2) : 0;

        $this->table(['指标', '值'], [
            ['配置表总数', $totalTables],
            ['已同步表数', $syncedTables],
            ['未同步表数', $totalTables - $syncedTables],
            ['总同步次数', $totalSyncs],
            ['平均成功率', $avgSuccessRate . '%']
        ]);
    }

    /**
     * 获取状态图标
     */
    private function getStatusIcon(string $status): string
    {
        return match($status) {
            SyncLog::STATUS_SUCCESS => '✅',
            SyncLog::STATUS_FAILED => '❌',
            SyncLog::STATUS_RUNNING => '🔄',
            SyncLog::STATUS_PENDING => '⏳',
            SyncLog::STATUS_PARTIAL => '⚠️',
            default => '⚪'
        };
    }
} 