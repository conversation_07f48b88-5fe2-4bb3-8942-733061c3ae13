<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\DataSyncService;
use App\Models\SyncLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class TestChangeLogSync extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'oracle:test-changelog-sync 
                            {--table= : 指定要测试的表名，不指定则测试所有配置的表}
                            {--dry-run : 干跑模式，只显示变更记录不实际同步}
                            {--limit=10 : 限制处理的变更记录数量}';

    /**
     * The console command description.
     */
    protected $description = '测试基于变更日志的增量同步功能';

    private DataSyncService $dataSyncService;

    public function __construct(DataSyncService $dataSyncService)
    {
        parent::__construct();
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tableName = $this->option('table');
        $dryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');

        $this->info("🧪 测试基于变更日志的增量同步");
        $this->info("模式: " . ($dryRun ? "干跑模式（不实际同步）" : "实际同步模式"));
        $this->newLine();

        try {
            // 检查sync_change_log表是否存在
            if (!$this->checkChangeLogTable()) {
                return Command::FAILURE;
            }

            if ($tableName) {
                // 测试指定表
                return $this->testSingleTable($tableName, $dryRun, $limit);
            } else {
                // 测试所有配置的表
                return $this->testAllTables($dryRun, $limit);
            }

        } catch (Exception $e) {
            $this->error("❌ 测试过程中发生错误: " . $e->getMessage());
            $this->error("📍 错误位置: " . $e->getFile() . ':' . $e->getLine());
            return Command::FAILURE;
        }
    }

    /**
     * 检查变更日志表是否存在
     */
    private function checkChangeLogTable(): bool
    {
        $this->info("📋 检查变更日志表...");

        try {
            $tables = DB::connection('oracle')->select("
                SELECT table_name FROM user_tables 
                WHERE table_name = 'SYNC_CHANGE_LOG'
            ");

            if (empty($tables)) {
                $this->error("❌ sync_change_log表不存在，请先部署变更监听器");
                return false;
            }

            $this->info("✅ sync_change_log表存在");

            // 检查表结构
            $columns = DB::connection('oracle')->select("
                SELECT column_name, data_type 
                FROM user_tab_columns 
                WHERE table_name = 'SYNC_CHANGE_LOG'
                ORDER BY column_id
            ");

            $requiredColumns = ['ID', 'TABLE_NAME', 'OPERATION', 'PK_JSON', 'PK_OLD_JSON', 'CHANGE_TIME', 'SYNC_STATUS'];
            $existingColumns = array_column($columns, 'COLUMN_NAME');

            foreach ($requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $existingColumns)) {
                    $this->error("❌ 缺少必要字段: {$requiredColumn}");
                    return false;
                }
            }

            $this->info("✅ 表结构验证通过");
            return true;

        } catch (Exception $e) {
            $this->error("❌ 检查变更日志表失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 测试单个表
     */
    private function testSingleTable(string $tableName, bool $dryRun, int $limit): int
    {
        $this->info("🔍 测试表: {$tableName}");

        // 检查表配置
        $config = config("sync.tables.{$tableName}");
        if (!$config) {
            $this->error("❌ 表 {$tableName} 没有同步配置");
            return Command::FAILURE;
        }

        $this->info("📋 表配置信息:");
        $this->info("  🔑 主键: " . (is_array($config['primary_key']) ? implode(', ', $config['primary_key']) : $config['primary_key']));
        $this->info("  🎯 过滤条件: " . json_encode($config['filter_conditions'] ?? []));
        $this->newLine();

        // 查询变更记录
        $changes = $this->getChangeRecords($tableName, $limit);

        if (empty($changes)) {
            $this->info("📭 没有发现未同步的变更记录");
            return Command::SUCCESS;
        }

        $this->info("📊 发现 " . count($changes) . " 条变更记录:");
        $this->displayChangeRecords($changes);
        $this->newLine();

        if ($dryRun) {
            $this->info("🏃‍♂️ 干跑模式，跳过实际同步");
            return Command::SUCCESS;
        }

        // 执行实际同步
        if ($this->confirm("是否执行实际同步？")) {
            return $this->executeSyncForTable($tableName);
        }

        return Command::SUCCESS;
    }

    /**
     * 测试所有表
     */
    private function testAllTables(bool $dryRun, int $limit): int
    {
        $this->info("🔍 测试所有配置的表");

        $syncTables = config('sync.tables', []);
        $enabledTables = array_filter($syncTables, function ($config) {
            return $config['enabled'] ?? true;
        });

        if (empty($enabledTables)) {
            $this->error("❌ 没有启用的同步表配置");
            return Command::FAILURE;
        }

        $this->info("📊 共有 " . count($enabledTables) . " 个启用的表");
        $this->newLine();

        $allSuccess = true;
        $totalChanges = 0;

        foreach (array_keys($enabledTables) as $tableName) {
            $this->info("🔍 检查表: {$tableName}");

            $changes = $this->getChangeRecords($tableName, $limit);
            $changeCount = count($changes);
            $totalChanges += $changeCount;

            if ($changeCount > 0) {
                $this->info("  📊 发现 {$changeCount} 条变更记录");
                
                if (!$dryRun && $changeCount > 0) {
                    $this->info("  🔄 执行增量同步...");
                    $result = $this->executeSyncForTable($tableName);
                    if ($result !== Command::SUCCESS) {
                        $allSuccess = false;
                    }
                }
            } else {
                $this->info("  📭 没有变更记录");
            }
        }

        $this->newLine();
        $this->info("📈 总计发现 {$totalChanges} 条变更记录");

        if ($dryRun) {
            $this->info("🏃‍♂️ 干跑模式完成");
        } else {
            $this->info($allSuccess ? "✅ 所有表同步完成" : "⚠️ 部分表同步失败");
        }

        return $allSuccess ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * 获取变更记录
     */
    private function getChangeRecords(string $tableName, int $limit): array
    {
        try {
            $sql = "
                SELECT operation, pk_json, pk_old_json, change_time, sync_status, id
                FROM sync_change_log 
                WHERE table_name = ?
                AND sync_status = 0
                ORDER BY change_time ASC
            ";

            if ($limit > 0) {
                $sql .= " FETCH FIRST {$limit} ROWS ONLY";
            }

            return DB::connection('oracle')->select($sql, [$tableName]);

        } catch (Exception $e) {
            $this->error("❌ 查询变更记录失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 显示变更记录
     */
    private function displayChangeRecords(array $changes): void
    {
        foreach ($changes as $index => $change) {
            $pkData = json_decode($change->pk_json, true);
            $pkStr = $pkData ? implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pkData), $pkData)) : '解析失败';
            
            $operationIcon = match(strtoupper($change->operation)) {
                'INSERT' => '➕',
                'UPDATE' => '✏️',
                'DELETE' => '🗑️',
                default => '❓'
            };
            
            // 如果是UPDATE操作且有pk_old_json，显示主键变更信息
            $primaryKeyChangeInfo = '';
            if (strtoupper($change->operation) === 'UPDATE' && !empty($change->pk_old_json)) {
                $pkOldData = json_decode($change->pk_old_json, true);
                if ($pkOldData && $pkData) {
                    $hasKeyChange = false;
                    foreach ($pkData as $key => $newValue) {
                        $oldValue = $pkOldData[$key] ?? null;
                        if ($newValue !== $oldValue) {
                            $hasKeyChange = true;
                            break;
                        }
                    }
                    
                    if ($hasKeyChange) {
                        $pkOldStr = implode(', ', array_map(fn($k, $v) => "{$k}={$v}", array_keys($pkOldData), $pkOldData));
                        $primaryKeyChangeInfo = " (主键变更: {$pkOldStr} → {$pkStr})";
                    }
                }
            }
            
            $this->info("  " . ($index + 1) . ". {$operationIcon} {$change->operation}: {$pkStr}{$primaryKeyChangeInfo} [{$change->change_time}]");
        }
    }

    /**
     * 执行指定表的同步
     */
    private function executeSyncForTable(string $tableName): int
    {
        try {
            $this->info("🔄 开始增量同步表: {$tableName}");

            $result = $this->dataSyncService->syncTable($tableName, SyncLog::TYPE_INCREMENTAL);

            if ($result['success']) {
                $this->info("✅ 同步成功:");
                $this->info("  📊 处理记录: {$result['records_processed']}");
                $this->info("  ➕ 新增记录: {$result['records_inserted']}");
                $this->info("  ✏️ 更新记录: {$result['records_updated']}");
                $this->info("  🗑️ 删除记录: {$result['records_deleted']}");
                
                if (isset($result['details']['total_changes'])) {
                    $this->info("  📝 处理变更: {$result['details']['total_changes']}");
                }
                
                return Command::SUCCESS;
            } else {
                $this->error("❌ 同步失败: " . ($result['error'] ?? '未知错误'));
                return Command::FAILURE;
            }

        } catch (Exception $e) {
            $this->error("❌ 同步执行失败: " . $e->getMessage());
            Log::error("变更日志同步失败", [
                'table' => $tableName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 显示变更记录统计
     */
    private function showChangeLogStats(): void
    {
        $this->info("📊 变更记录统计:");

        try {
            $stats = DB::connection('oracle')->select("
                SELECT table_name, operation, COUNT(*) as count
                FROM sync_change_log 
                WHERE sync_status = 0
                GROUP BY table_name, operation
                ORDER BY table_name, operation
            ");

            if (empty($stats)) {
                $this->info("  📭 当前没有未同步的变更记录");
                return;
            }

            $tableStats = [];
            foreach ($stats as $stat) {
                if (!isset($tableStats[$stat->table_name])) {
                    $tableStats[$stat->table_name] = [];
                }
                $tableStats[$stat->table_name][$stat->operation] = $stat->count;
            }

            foreach ($tableStats as $table => $operations) {
                $total = array_sum($operations);
                $this->info("  📋 {$table}: {$total} 条");
                foreach ($operations as $operation => $count) {
                    $icon = match(strtoupper($operation)) {
                        'INSERT' => '➕',
                        'UPDATE' => '✏️',
                        'DELETE' => '🗑️',
                        default => '❓'
                    };
                    $this->info("    {$icon} {$operation}: {$count}");
                }
            }

        } catch (Exception $e) {
            $this->error("❌ 获取统计信息失败: " . $e->getMessage());
        }
    }
} 