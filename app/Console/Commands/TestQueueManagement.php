<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\QueueManagementService;

class TestQueueManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:queue-management {action=status}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试队列管理服务功能';

    /**
     * Execute the console command.
     */
    public function handle(QueueManagementService $queueService)
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->showStatus($queueService);
                break;
            case 'start':
                $this->startWorker($queueService);
                break;
            case 'ensure':
                $this->ensureWorkers($queueService);
                break;
            case 'check':
                $this->checkAndFix($queueService);
                break;
            default:
                $this->error("未知操作: {$action}");
                $this->info("可用操作: status, start, ensure, check");
        }
    }

    private function showStatus(QueueManagementService $queueService)
    {
        $this->info('=== 队列状态 ===');
        
        $stats = $queueService->getQueueStats();
        
        $this->info("工作进程数量: {$stats['worker_count']}");
        $this->info("待处理任务: {$stats['pending_jobs']}");
        $this->info("失败任务: {$stats['failed_jobs']}");
        $this->info("有活跃进程: " . ($stats['has_active_workers'] ? '是' : '否'));
        $this->info("检查时间: {$stats['timestamp']}");
    }

    private function startWorker(QueueManagementService $queueService)
    {
        $this->info('=== 启动队列工作进程 ===');
        
        $result = $queueService->startQueueWorker();
        
        if ($result['success']) {
            $this->info("✅ " . $result['message']);
            if (isset($result['warning'])) {
                $this->warn("⚠️  " . $result['warning']);
            }
        } else {
            $this->error("❌ " . $result['message']);
        }
    }

    private function ensureWorkers(QueueManagementService $queueService)
    {
        $this->info('=== 确保队列工作进程 ===');
        
        $results = $queueService->ensureQueueWorkers(2);
        
        foreach ($results as $result) {
            if ($result['success']) {
                $this->info("✅ " . $result['message']);
            } else {
                $this->error("❌ " . $result['message']);
            }
        }
    }

    private function checkAndFix(QueueManagementService $queueService)
    {
        $this->info('=== 队列健康检查和自动修复 ===');
        
        $result = $queueService->checkAndAutoFix();
        
        if (!empty($result['issues'])) {
            $this->warn('发现问题:');
            foreach ($result['issues'] as $issue) {
                $this->warn("  - {$issue}");
            }
        }

        if (!empty($result['fixes'])) {
            $this->info('应用修复:');
            foreach ($result['fixes'] as $fix) {
                $this->info("  ✅ {$fix}");
            }
        }

        $this->info("最终状态: 工作进程={$result['worker_count']}, 待处理任务={$result['pending_jobs']}");
        $this->info("健康状态: " . ($result['is_healthy'] ? '✅ 健康' : '❌ 不健康'));
    }
} 