<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DatabaseService;

class TestDatabaseConnections extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:test {connection? : 指定要测试的数据库连接名称} {--all : 测试所有数据库连接}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试数据库连接';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $databaseService = new DatabaseService();
        
        if ($this->option('all')) {
            return $this->testAllConnections($databaseService);
        }
        
        $connection = $this->argument('connection') ?? 'default';
        return $this->testSingleConnection($databaseService, $connection);
    }
    
    /**
     * 测试单个数据库连接
     */
    private function testSingleConnection(DatabaseService $databaseService, string $connection): int
    {
        $this->info("正在测试数据库连接: {$connection}");
        $this->newLine();
        
        $result = $databaseService->testConnection($connection);
        
        if ($result['success']) {
            $this->info('✅ 数据库连接测试成功！');
            $this->newLine();
            
            $this->displayConnectionResult($result);
            
            return Command::SUCCESS;
        } else {
            $this->error('❌ 数据库连接测试失败！');
            $this->error('错误信息: ' . $result['error']);
            
            return Command::FAILURE;
        }
    }
    
    /**
     * 测试所有数据库连接
     */
    private function testAllConnections(DatabaseService $databaseService): int
    {
        $this->info('正在测试所有数据库连接...');
        $this->newLine();
        
        $results = $databaseService->testAllConnections();
        $hasFailure = false;
        
        foreach ($results as $result) {
            if ($result['success']) {
                $this->info("✅ {$result['connection']} ({$result['driver']}) - 连接成功");
            } else {
                $this->error("❌ {$result['connection']} - 连接失败: {$result['error']}");
                $hasFailure = true;
            }
        }
        
        $this->newLine();
        
        if (!$hasFailure) {
            $this->info('🎉 所有数据库连接测试通过！');
            
            // 显示详细信息
            $this->newLine();
            $this->info('详细信息:');
            foreach ($results as $result) {
                if ($result['success']) {
                    $this->displayConnectionResult($result, false);
                }
            }
            
            return Command::SUCCESS;
        } else {
            $this->error('⚠️  部分数据库连接测试失败！');
            return Command::FAILURE;
        }
    }
    
    /**
     * 显示连接测试结果
     */
    private function displayConnectionResult(array $result, bool $showHeader = true): void
    {
        if ($showHeader) {
            $this->info('连接信息:');
        } else {
            $this->newLine();
            $this->info("--- {$result['connection']} ---");
        }
        
        $this->info('  连接名称: ' . $result['connection']);
        $this->info('  数据库类型: ' . $result['driver']);
        $this->info('  主机地址: ' . $result['host']);
        $this->info('  数据库名: ' . $result['database']);
        
        if (!empty($result['info'])) {
            $this->displayDatabaseInfo($result['info']);
        }
        
        if (!$showHeader) {
            $this->newLine();
        }
    }
    
    /**
     * 显示数据库详细信息
     */
    private function displayDatabaseInfo(array $info): void
    {
        if (empty($info)) {
            return;
        }
        
        $this->info('  数据库详细信息:');
        
        if (isset($info['version'])) {
            $this->info('    版本: ' . $info['version']);
        }
        
        if (isset($info['current_user'])) {
            $this->info('    当前用户: ' . $info['current_user']);
        }
        
        if (isset($info['server_time'])) {
            $this->info('    服务器时间: ' . $info['server_time']);
        }
        
        if (isset($info['query_test'])) {
            $this->info('    查询测试: ' . $info['query_test']);
        }
        
        if (isset($info['error'])) {
            $this->warn('    错误信息: ' . $info['error']);
        }
    }
} 