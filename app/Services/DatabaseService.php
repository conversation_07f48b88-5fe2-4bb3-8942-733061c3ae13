<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Exception;

class DatabaseService
{
    /**
     * 测试数据库连接
     */
    public function testConnection(string $connection = 'default'): array
    {
        try {
            $connectionName = $connection === 'default' ? Config::get('database.default') : $connection;
            
            // 获取连接配置
            $config = Config::get("database.connections.{$connectionName}");
            
            if (!$config) {
                throw new Exception("数据库连接配置 '{$connectionName}' 不存在");
            }
            
            // 对于Oracle连接，先检查是否可以安全测试
            if ($config['driver'] === 'oracle') {
                return $this->testOracleConnectionSafely($connectionName, $config);
            }
            
            // 测试连接
            $pdo = DB::connection($connectionName)->getPdo();
            
            if (!$pdo) {
                throw new Exception('无法建立数据库连接');
            }
            
            // 获取数据库信息
            $info = $this->getDatabaseInfo($connectionName, $config['driver']);
            
            return [
                'success' => true,
                'connection' => $connectionName,
                'driver' => $config['driver'],
                'host' => $config['host'] ?? 'N/A',
                'database' => $config['database'] ?? 'N/A',
                'info' => $info,
                'message' => '数据库连接测试成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'connection' => $connection,
                'error' => $e->getMessage(),
                'message' => '数据库连接测试失败'
            ];
        }
    }
    
    /**
     * 安全地测试Oracle连接
     */
    private function testOracleConnectionSafely(string $connectionName, array $config): array
    {
        try {
            // 检查Oracle客户端库是否可用
            if (!function_exists('oci_connect')) {
                throw new Exception('OCI8扩展未安装或未启用');
            }
            
            // 尝试获取Oracle客户端版本（这可能会导致段错误）
            $clientVersion = null;
            try {
                $clientVersion = oci_client_version();
            } catch (Exception $e) {
                throw new Exception('Oracle客户端库未正确安装或配置: ' . $e->getMessage());
            }
            
            // 如果能到这里，说明Oracle客户端库基本可用，尝试连接
            $pdo = DB::connection($connectionName)->getPdo();
            
            if (!$pdo) {
                throw new Exception('无法建立Oracle数据库连接');
            }
            
            // 获取数据库信息
            $info = $this->getDatabaseInfo($connectionName, $config['driver']);
            
            return [
                'success' => true,
                'connection' => $connectionName,
                'driver' => $config['driver'],
                'host' => $config['host'] ?? 'N/A',
                'database' => $config['database'] ?? 'N/A',
                'info' => $info,
                'message' => '数据库连接测试成功',
                'oracle_client_version' => $clientVersion
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'connection' => $connectionName,
                'driver' => 'oracle',
                'host' => $config['host'] ?? 'N/A',
                'database' => $config['database'] ?? 'N/A',
                'error' => $e->getMessage(),
                'message' => 'Oracle数据库连接测试失败',
                'suggestion' => '请检查Oracle客户端库是否正确安装和配置'
            ];
        }
    }
    
    /**
     * 获取数据库信息
     */
    private function getDatabaseInfo(string $connection, string $driver): array
    {
        $info = [];
        
        try {
            switch ($driver) {
                case 'oracle':
                    $info = $this->getOracleInfo($connection);
                    break;
                case 'mysql':
                    $info = $this->getMysqlInfo($connection);
                    break;
                default:
                    $info['driver'] = $driver;
                    break;
            }
        } catch (Exception $e) {
            $info['error'] = '获取数据库信息失败: ' . $e->getMessage();
        }
        
        return $info;
    }
    
    /**
     * 获取Oracle数据库信息
     */
    private function getOracleInfo(string $connection): array
    {
        $info = [];
        
        try {
            // 获取版本信息
            $version = DB::connection($connection)->select('SELECT * FROM V$VERSION WHERE ROWNUM = 1');
            if (!empty($version)) {
                $info['version'] = $version[0]->banner;
            }
            
            // 获取当前用户
            $user = DB::connection($connection)->select('SELECT USER FROM DUAL');
            if (!empty($user)) {
                $info['current_user'] = $user[0]->user;
            }
            
            // 获取当前时间
            $time = DB::connection($connection)->select('SELECT SYSDATE FROM DUAL');
            if (!empty($time)) {
                $info['server_time'] = $time[0]->sysdate;
            }
            
            // 测试查询
            $test = DB::connection($connection)->select('SELECT 1 as test_value FROM DUAL');
            $info['query_test'] = !empty($test) && $test[0]->test_value == 1 ? '通过' : '失败';
            
        } catch (Exception $e) {
            $info['error'] = $e->getMessage();
        }
        
        return $info;
    }
    
    /**
     * 获取MySQL数据库信息
     */
    private function getMysqlInfo(string $connection): array
    {
        $info = [];
        
        try {
            // 获取版本信息
            $version = DB::connection($connection)->select('SELECT VERSION() as version');
            if (!empty($version)) {
                $info['version'] = $version[0]->version;
            }
            
            // 获取当前用户
            $user = DB::connection($connection)->select('SELECT USER() as user');
            if (!empty($user)) {
                $info['current_user'] = $user[0]->user;
            }
            
            // 获取当前时间
            $time = DB::connection($connection)->select('SELECT NOW() as server_time');
            if (!empty($time)) {
                $info['server_time'] = $time[0]->server_time;
            }
            
            // 测试查询
            $test = DB::connection($connection)->select('SELECT 1 as test_value');
            $info['query_test'] = !empty($test) && $test[0]->test_value == 1 ? '通过' : '失败';
            
        } catch (Exception $e) {
            $info['error'] = $e->getMessage();
        }
        
        return $info;
    }
    
    /**
     * 获取所有配置的数据库连接
     */
    public function getAllConnections(): array
    {
        $connections = Config::get('database.connections', []);
        $result = [];
        
        foreach ($connections as $name => $config) {
            $result[] = [
                'name' => $name,
                'driver' => $config['driver'],
                'host' => $config['host'] ?? 'N/A',
                'database' => $config['database'] ?? 'N/A',
                'is_default' => $name === Config::get('database.default')
            ];
        }
        
        return $result;
    }
    
    /**
     * 测试所有数据库连接
     */
    public function testAllConnections(): array
    {
        $connections = $this->getAllConnections();
        $results = [];
        
        foreach ($connections as $connection) {
            $results[] = $this->testConnection($connection['name']);
        }
        
        return $results;
    }
} 