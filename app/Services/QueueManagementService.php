<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Log;

class QueueManagementService
{
    /**
     * 检查是否有队列工作进程运行
     */
    public function hasActiveWorkers(): bool
    {
        try {
            $result = Process::run('ps aux | grep "queue:work" | grep -v grep');
            return $result->successful() && !empty(trim($result->output()));
        } catch (\Exception $e) {
            Log::error('检查队列工作进程失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取队列工作进程数量
     */
    public function getWorkerCount(): int
    {
        try {
            $result = Process::run('ps aux | grep "queue:work" | grep -v grep | wc -l');
            if ($result->successful()) {
                return (int) trim($result->output());
            }
        } catch (\Exception $e) {
            Log::error('获取队列工作进程数量失败: ' . $e->getMessage());
        }
        
        return 0;
    }

    /**
     * 获取待处理任务数量
     */
    public function getPendingJobsCount(): int
    {
        try {
            return DB::table('jobs')->count();
        } catch (\Exception $e) {
            Log::error('获取待处理任务数量失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 启动队列工作进程
     */
    public function startQueueWorker(): array
    {
        try {
            // 启动前检查当前工作进程数量，避免启动过多
            $currentWorkers = $this->getWorkerCount();
            if ($currentWorkers >= 4) {
                $message = "已有 {$currentWorkers} 个工作进程运行，跳过启动新进程";
                Log::info($message);
                return [
                    'success' => true,
                    'message' => $message,
                    'skipped' => true,
                    'current_workers' => $currentWorkers
                ];
            }
            
            // 🔧 关键修复：使用更可靠的进程启动方法
            $basePath = base_path();
            // 🔧 重要修复：确保使用命令行PHP而不是php-fpm
            $phpBinary = 'php'; // 使用PATH中的php而不是PHP_BINARY
            
            // 检测操作系统并构建命令
            $os = PHP_OS_FAMILY;
            
            if ($os === 'Darwin') {
                // macOS: 设置完整环境，使用exec替代Process::run
                $command = "cd {$basePath} && {$phpBinary} artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 & echo \$!";
            } else {
                // Linux: 同样设置完整环境
                $command = "cd {$basePath} && nohup {$phpBinary} artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 & echo \$!";
            }
            
            Log::info("🔧 HTTP环境优化启动命令: {$command}");
            
            // 🔧 关键修复：使用exec替代Process::run，设置正确的工作目录
            $descriptorspec = [
                0 => ['pipe', 'r'],  // stdin
                1 => ['pipe', 'w'],  // stdout  
                2 => ['pipe', 'w']   // stderr
            ];
            
            $process = proc_open($command, $descriptorspec, $pipes, $basePath);
            
            if (is_resource($process)) {
                // 读取输出获取PID
                $pid = trim(fgets($pipes[1]));
                
                // 关闭管道
                fclose($pipes[0]);
                fclose($pipes[1]);
                fclose($pipes[2]);
                
                // 关闭进程句柄
                proc_close($process);
                
                if ($pid && is_numeric($pid)) {
                    Log::info("🔧 队列工作进程PID: {$pid}");
                    
                    // 等待进程启动
                    sleep(3);
                    
                    // 🔧 关键修复：不依赖shell PID验证，直接检查队列工作进程
                    Log::info("🔧 跳过shell PID验证，直接检查队列工作进程");
                    
                    // 检查是否有队列工作进程运行
                    if ($this->hasActiveWorkers()) {
                        $actualWorkerCount = $this->getWorkerCount();
                        $message = "🔧 HTTP环境队列工作进程启动成功 (Shell PID: {$pid}, 工作进程数: {$actualWorkerCount})";
                        Log::info($message);
                        
                        return [
                            'success' => true,
                            'message' => $message,
                            'shell_pid' => $pid,
                            'worker_count' => $actualWorkerCount,
                            'method' => 'proc_open_worker_verified'
                        ];
                    } else {
                        // 再等待2秒，有时启动需要更多时间
                        Log::info("🔧 首次检查未发现工作进程，等待2秒后重试");
                        sleep(2);
                        
                        if ($this->hasActiveWorkers()) {
                            $actualWorkerCount = $this->getWorkerCount();
                            $message = "🔧 HTTP环境队列工作进程启动成功 (延迟启动, 工作进程数: {$actualWorkerCount})";
                            Log::info($message);
                            
                            return [
                                'success' => true,
                                'message' => $message,
                                'shell_pid' => $pid,
                                'worker_count' => $actualWorkerCount,
                                'method' => 'proc_open_delayed_verified'
                            ];
                        }
                        
                        throw new \Exception("🔧 HTTP环境队列工作进程启动失败：等待5秒后仍未检测到活动进程 (Shell PID: {$pid})");
                    }
                } else {
                    throw new \Exception("🔧 无法获取有效PID: " . $pid);
                }
            } else {
                throw new \Exception("🔧 proc_open启动失败");
            }
            
        } catch (\Exception $e) {
            $message = '🔧 HTTP环境启动队列工作进程失败: ' . $e->getMessage();
            Log::error($message);
            Log::error('🔧 错误详情: ' . $e->getTraceAsString());
            
            // 🔧 降级策略：提供手动启动指导
            $manualCommand = "php artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3";
            
            return [
                'success' => false,
                'message' => $message,
                'error' => $e->getMessage(),
                'manual_fix' => [
                    'message' => '自动启动失败，请手动启动队列工作进程',
                    'command' => $manualCommand,
                    'description' => '在项目根目录下运行上述命令启动队列工作进程'
                ]
            ];
        }
    }

    /**
     * 确保有足够的队列工作进程
     */
    public function ensureQueueWorkers(int $minWorkers = 1): array
    {
        $currentWorkers = $this->getWorkerCount();
        $results = [];
        
        Log::info("当前队列工作进程数量: {$currentWorkers}, 最小需要: {$minWorkers}");
        
        if ($currentWorkers < $minWorkers) {
            $needed = $minWorkers - $currentWorkers;
            Log::info("需要启动 {$needed} 个队列工作进程");
            
            for ($i = 0; $i < $needed; $i++) {
                $result = $this->startQueueWorker();
                $results[] = $result;
                
                if (!$result['success']) {
                    Log::error("启动第 " . ($i + 1) . " 个队列工作进程失败");
                    break;
                }
                
                // 短暂延迟，避免同时启动多个进程的竞争
                if ($i < $needed - 1) {
                    usleep(500000); // 0.5秒
                }
            }
        } else {
            $results[] = [
                'success' => true,
                'message' => "队列工作进程数量充足 ({$currentWorkers} 个)",
                'skipped' => true
            ];
        }
        
        return $results;
    }

    /**
     * 检查队列健康状态并自动修复
     */
    public function checkAndAutoFix(): array
    {
        $issues = [];
        $fixes = [];
        
        // 检查工作进程
        $workerCount = $this->getWorkerCount();
        $pendingJobs = $this->getPendingJobsCount();
        
        Log::info("队列健康检查: 工作进程={$workerCount}, 待处理任务={$pendingJobs}");
        
        if ($workerCount === 0) {
            $issues[] = '没有队列工作进程在运行';
            
            // 自动启动工作进程
            $result = $this->startQueueWorker();
            $fixes[] = $result['message'];
            
            if (!$result['success']) {
                $issues[] = '自动启动队列工作进程失败: ' . $result['error'];
            }
        } elseif ($workerCount === 1 && $pendingJobs > 5) {
            $issues[] = "队列任务较多 ({$pendingJobs}) 但只有1个工作进程";
            
            // 启动额外的工作进程
            $result = $this->startQueueWorker();
            if ($result['success']) {
                $fixes[] = $result['message'];
            }
        }
        
        if ($pendingJobs > 20) {
            $issues[] = "待处理任务过多 ({$pendingJobs})";
        }
        
        return [
            'issues' => $issues,
            'fixes' => $fixes,
            'worker_count' => $this->getWorkerCount(), // 重新获取最新数量
            'pending_jobs' => $pendingJobs,
            'is_healthy' => empty($issues) || !empty($fixes)
        ];
    }

    /**
     * 启动单任务工作进程（处理完一个任务后自动退出）
     */
    public function startSingleTaskWorker(): array
    {
        try {
            // 🔧 关键修复：使用更可靠的进程启动方法
            $basePath = base_path();
            // 🔧 重要修复：确保使用命令行PHP而不是php-fpm
            $phpBinary = 'php'; // 使用PATH中的php而不是PHP_BINARY
            
            // 检测操作系统并构建命令
            $os = PHP_OS_FAMILY;
            
            if ($os === 'Darwin') {
                // macOS: 单任务工作进程保留max-jobs=1
                $command = "cd {$basePath} && {$phpBinary} artisan queue:work --max-jobs=1 --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 & echo \$!";
            } else {
                // Linux: 单任务工作进程保留max-jobs=1
                $command = "cd {$basePath} && nohup {$phpBinary} artisan queue:work --max-jobs=1 --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 & echo \$!";
            }
            
            Log::info("🔧 HTTP环境启动单任务工作进程命令: {$command}");
            
            // 🔧 关键修复：使用proc_open替代Process::run
            $descriptorspec = [
                0 => ['pipe', 'r'],  // stdin
                1 => ['pipe', 'w'],  // stdout  
                2 => ['pipe', 'w']   // stderr
            ];
            
            $process = proc_open($command, $descriptorspec, $pipes, $basePath);
            
            if (is_resource($process)) {
                // 读取输出获取PID
                $pid = trim(fgets($pipes[1]));
                
                // 关闭管道
                fclose($pipes[0]);
                fclose($pipes[1]);
                fclose($pipes[2]);
                
                // 关闭进程句柄
                proc_close($process);
                
                if ($pid && is_numeric($pid)) {
                    Log::info("🔧 单任务工作进程PID: {$pid}");
                    
                    // 对于单任务工作进程，不需要验证是否持续运行
                    // 因为它们会在没有任务时立即退出，这是正常行为
                    $message = "🔧 HTTP环境单任务队列工作进程启动成功 (PID: {$pid})";
                    Log::info($message);
                    
                    return [
                        'success' => true,
                        'message' => $message,
                        'pid' => $pid,
                        'type' => 'single_task',
                        'method' => 'proc_open_http_optimized'
                    ];
                } else {
                    throw new \Exception("🔧 无法获取有效PID: " . $pid);
                }
            } else {
                throw new \Exception("🔧 proc_open启动失败");
            }
            
        } catch (\Exception $e) {
            $message = '🔧 HTTP环境启动单任务队列工作进程失败: ' . $e->getMessage();
            Log::error($message);
            Log::error('🔧 错误详情: ' . $e->getTraceAsString());
            
            // 🔧 降级策略：提供手动启动指导
            $manualCommand = "php artisan queue:work --max-jobs=1 --timeout=3600 --memory=512 --tries=3 --sleep=3";
            
            return [
                'success' => false,
                'message' => $message,
                'error' => $e->getMessage(),
                'manual_fix' => [
                    'message' => '自动启动失败，请手动启动单任务队列工作进程',
                    'command' => $manualCommand,
                    'description' => '在项目根目录下运行上述命令启动单任务队列工作进程'
                ]
            ];
        }
    }

    /**
     * 批量启动多个工作进程
     */
    public function startMultipleWorkers(int $count, bool $singleTask = true): array
    {
        $results = [];
        $successCount = 0;
        
        Log::info("开始批量启动 {$count} 个工作进程，单任务模式: " . ($singleTask ? '是' : '否'));
        
        for ($i = 0; $i < $count; $i++) {
            if ($singleTask) {
                $result = $this->startSingleTaskWorker();
            } else {
                $result = $this->startQueueWorker();
            }
            
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                Log::error("启动第 " . ($i + 1) . " 个工作进程失败: " . $result['message']);
            }
            
            // 短暂延迟，避免同时启动多个进程的竞争
            if ($i < $count - 1) {
                usleep(200000); // 0.2秒
            }
        }
        
        Log::info("批量启动完成: {$successCount}/{$count} 个工作进程启动成功");
        
        return [
            'total_requested' => $count,
            'successful_starts' => $successCount,
            'failed_starts' => $count - $successCount,
            'results' => $results,
            'success' => $successCount > 0
        ];
    }

    /**
     * 根据任务数量确保有足够的工作进程
     */
    public function ensureWorkersForTasks(int $taskCount): array
    {
        // 为多表同步使用常驻工作进程，最多启动4个工作进程
        $maxWorkers = min($taskCount, 4);
        $currentWorkers = $this->getWorkerCount();
        
        Log::info("任务数量: {$taskCount}, 当前工作进程: {$currentWorkers}, 计划最大工作进程: {$maxWorkers}");
        
        // 重要修复：如果当前工作进程数量已经足够，直接返回成功
        if ($currentWorkers >= $maxWorkers) {
            $message = "工作进程数量充足 ({$currentWorkers} 个)，无需启动更多";
            Log::info($message);
            return [
                'success' => true,
                'message' => $message,
                'skipped' => true,
                'current_workers' => $currentWorkers,
                'required_workers' => $maxWorkers
            ];
        }
        
        // 如果有工作进程但数量不足，也考虑是否真的需要更多
        if ($currentWorkers > 0 && $currentWorkers >= min($taskCount, 2)) {
            $message = "当前有 {$currentWorkers} 个工作进程运行，对于 {$taskCount} 个任务已经足够";
            Log::info($message);
            return [
                'success' => true,
                'message' => $message,
                'skipped' => true,
                'current_workers' => $currentWorkers,
                'sufficient_for_tasks' => true
            ];
        }
        
        // 计算需要启动的工作进程数量
        $needToStart = $maxWorkers - $currentWorkers;
        
        Log::info("需要额外启动 {$needToStart} 个常驻工作进程");
        
        // 为多表同步启动常驻工作进程（不是单任务工作进程）
        $result = $this->startMultipleWorkers($needToStart, false); // false = 常驻工作进程
        
        return array_merge($result, [
            'previous_workers' => $currentWorkers,
            'target_workers' => $maxWorkers,
            'message' => "为 {$taskCount} 个任务启动了 {$result['successful_starts']} 个常驻工作进程"
        ]);
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats(): array
    {
        return [
            'worker_count' => $this->getWorkerCount(),
            'pending_jobs' => $this->getPendingJobsCount(),
            'failed_jobs' => DB::table('failed_jobs')->count(),
            'has_active_workers' => $this->hasActiveWorkers(),
            'timestamp' => now()->toISOString()
        ];
    }
} 