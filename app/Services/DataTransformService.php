<?php

namespace App\Services;

use App\Models\AccountReceivablePayableType;
use App\Models\BOM;
use App\Models\Category;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\ExchangeRateBasis;
use App\Models\InvoiceType;
use App\Models\Material;
use App\Models\MaterialTranslation;
use App\Models\ReceiptPaymentTerm;
use App\Models\SalesPricingMethod;
use App\Models\SalesType;
use App\Models\TaxType;
use App\Models\TradeTerm;
use Carbon\Carbon;
use DB;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * 数据转化服务
 * 负责将本地同步的Oracle数据转化为业务模型数据
 * 注意：此服务从本地数据库的同步表中读取数据，不直接访问Oracle数据库
 */
class DataTransformService
{
    private DatabaseService $databaseService;

    public function __construct(DatabaseService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * 执行指定类型的数据转化
     * 
     * @param string $type 转化类型 (category|customer|bom|material)
     * @param string $companyCode 公司代码 
     * @param array $options 转化选项
     * @return array 转化结果
     */
    public function transform(string $type, string $companyCode = 'TB', array $options = []): array
    {
        try {
            $startTime = microtime(true);
            
            Log::info("开始执行数据转化", [
                'type' => $type,
                'company_code' => $companyCode,
                'options' => $options,
                'source' => 'local_synced_tables'
            ]);

            $result = match ($type) {
                'category' => $this->transformCategories($companyCode, $options),
                'customer' => $this->transformCustomers($companyCode, $options),
                'bom' => $this->transformBOMs($companyCode, $options),
                'material' => $this->transformMaterials($companyCode, $options),
                default => throw new Exception("不支持的转化类型: {$type}")
            };

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            Log::info("数据转化完成", [
                'type' => $type,
                'company_code' => $companyCode,
                'duration' => $duration,
                'result' => $result
            ]);

            return array_merge($result, [
                'duration' => $duration,
                'company_code' => $companyCode
            ]);

        } catch (Exception $e) {
            Log::error("数据转化失败", [
                'type' => $type,
                'company_code' => $companyCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => $type,
                'company_code' => $companyCode
            ];
        }
    }

    /**
     * 转化分类数据
     * 从本地同步的 RTAXL_T 表转化分类信息
     */
    private function transformCategories(string $companyCode, array $options = []): array
    {
        $processed = 0;
        $errors = 0;
        $locale = $options['locale'] ?? 'zh_CN';

        try {
            // 从本地数据库的同步表读取数据，不直接访问Oracle
            // 注意：本地同步表的字段名为大写
            $query = DB::table('RTAXL_T')
                ->where('RTAXLENT', '=', '40')
                ->where('RTAXL002', '=', $locale)
                ->whereNotNull('RTAXL003')
                ->orderBy('RTAXL001');

            $query->chunk(1000, function ($chunk) use ($companyCode, $locale, &$processed, &$errors) {
                foreach ($chunk as $row) {
                    try {
                        Category::updateOrCreate(
                            [
                                'company_code' => $companyCode,
                                'category_code' => $row->RTAXL001,
                                'locale' => $locale,
                            ],
                            [
                                'category_name' => $row->RTAXL003
                            ]
                        );
                        $processed++;
                    } catch (Exception $e) {
                        $errors++;
                        Log::warning("分类转化失败", [
                            'category_code' => $row->RTAXL001 ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });

            return [
                'success' => true,
                'type' => 'category',
                'processed' => $processed,
                'errors' => $errors,
                'message' => "成功转化 {$processed} 条分类记录，{$errors} 条失败",
                'source_table' => 'RTAXL_T (local)'
            ];

        } catch (Exception $e) {
            throw new Exception("分类转化失败: " . $e->getMessage());
        }
    }

    /**
     * 转化客户数据  
     * 从本地同步的 PMAB_T 和 PMAAL_T 表转化客户信息
     */
    private function transformCustomers(string $companyCode, array $options = []): array
    {
        $processed = 0;
        $errors = 0;
        $locale = $options['locale'] ?? 'zh_CN';

        try {
            // 检查相关表的数据完整性
            $integrityCheck = $this->checkCustomerTablesIntegrity($locale);
            if (!$integrityCheck['ready']) {
                Log::warning("客户数据表完整性检查未通过，继续转化但数据可能不完整", [
                    'integrity_check' => $integrityCheck,
                    'locale' => $locale
                ]);
            }
            // 客户取价方式映射
            $pricingMethodMap = [
                '00006' => 'C02',
                '00008' => 'C02', 
                '0005' => 'C03',
                '0007' => 'C01',
            ];

            // 汇率计算基准映射
            $exchangeRateBasisMap = [
                '1' => '01',
                '2' => '02',
                '3' => '03',
                '4' => '04',
                '5' => '05',
            ];

            // 从本地数据库的同步表读取数据
            $query = DB::table('PMAB_T')
                ->where('PMABENT', '=', '40')
                ->where('PMABSITE', '=', 'ALL')
                ->whereNotNull('PMAB087')
                ->join('PMAAL_T', function ($join) use ($locale) {
                    $join->on('PMAAL_T.PMAAL001', '=', 'PMAB_T.PMAB001')
                        ->where('PMAAL_T.PMAALENT', '=', '40')
                        ->where('PMAAL_T.PMAAL002', '=', $locale);
                })
                ->orderBy('PMAB_T.PMAB001');

            $query->chunk(1000, function ($chunk) use (
                $companyCode, 
                $pricingMethodMap, 
                $exchangeRateBasisMap, 
                &$processed, 
                &$errors
            ) {
                foreach ($chunk as $data) {
                    try {
                        // 查找关联的基础数据ID
                        $currencyId = Currency::where('code', $data->PMAB083)->value('id');
                        $taxTypeId = TaxType::where('code', $data->PMAB084)->value('id');
                        $tradeTermId = TradeTerm::where('code', $data->PMAB103)->value('id');
                        $pricingMethodId = isset($pricingMethodMap[$data->PMAB104]) 
                            ? SalesPricingMethod::where('code', $pricingMethodMap[$data->PMAB104])->value('id')
                            : null;
                        $invoiceTypeId = InvoiceType::where('code', $data->PMAB106)->value('id');
                        $paymentTermId = ReceiptPaymentTerm::where('code', $data->PMAB087)->value('id');
                        $accountReceivableTypeId = AccountReceivablePayableType::where('code', $data->PMAB105)->value('id');
                        $salesTypeId = SalesType::where('code', $data->PMAB089)->value('id');
                        $exchangeRateBasisId = isset($exchangeRateBasisMap[$data->PMAB108])
                            ? ExchangeRateBasis::where('code', $exchangeRateBasisMap[$data->PMAB108])->value('id')
                            : null;

                        Customer::updateOrCreate(
                            [
                                'company_code' => $companyCode,
                                'code' => $data->PMAB001,
                            ],
                            [
                                'short_name' => $data->PMAAL004,
                                'full_name' => $data->PMAAL003,
                                'currency_id' => $currencyId,
                                'tax_type_id' => $taxTypeId,
                                'trade_term_id' => $tradeTermId,
                                'pricing_method_id' => $pricingMethodId,
                                'invoice_type_id' => $invoiceTypeId,
                                'payment_term_id' => $paymentTermId,
                                'account_receivable_type_id' => $accountReceivableTypeId,
                                'sales_type_id' => $salesTypeId,
                                'exchange_rate_basis_id' => $exchangeRateBasisId,
                                'tax_number' => '',
                                'status' => $data->PMABSTUS == 'Y' ? 'Y' : 'N',
                                'activity_level' => 0,
                            ]
                        );
                        $processed++;
                    } catch (Exception $e) {
                        $errors++;
                        Log::warning("客户转化失败", [
                            'customer_code' => $data->PMAB001 ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });

            return [
                'success' => true,
                'type' => 'customer',
                'processed' => $processed,
                'errors' => $errors,
                'message' => "成功转化 {$processed} 条客户记录，{$errors} 条失败",
                'source_tables' => 'PMAB_T + PMAAL_T (local)'
            ];

        } catch (Exception $e) {
            throw new Exception("客户转化失败: " . $e->getMessage());
        }
    }

    /**
     * 转化BOM数据
     * 从本地同步的 bmba_t 表转化BOM信息
     */
    private function transformBOMs(string $companyCode, array $options = []): array
    {
        $processed = 0;
        $errors = 0;
        $skipped = 0;
        $updated = 0;
        $created = 0;
        $version = $options['version'] ?? Carbon::now()->format('Ymd');

        try {
            // 从本地数据库的同步表读取数据
            $query = DB::table('BMBA_T')
                ->where('BMBAENT', '=', '40')
                ->where('BMBASITE', '=', 'ALL')
                ->orderBy('BMBA001')
                ->orderBy('BMBA003');

            $query->chunk(1000, function ($chunk) use ($companyCode, $version, &$processed, &$errors, &$skipped, &$updated, &$created) {
                foreach ($chunk as $bom) {
                    try {
                        // 🔧 变更检测机制
                        $primaryKey = [
                            'company_code' => $companyCode,
                            'parent_material_code' => $bom->BMBA001,
                            'child_material_code' => $bom->BMBA003,
                        ];
                        
                        // 🔧 修复：业务数据部分（不包含version）
                        $businessData = [
                            'base_quantity' => $bom->BMBA012 ? $bom->BMBA012 : 1,
                            'child_quantity' => $bom->BMBA011 ? $bom->BMBA011 : 1,
                            'unit' => $bom->BMBA010,
                            'is_order_expand' => $bom->BMBA021,
                            'is_optional' => $bom->BMBA020,
                            'is_customer_material' => $bom->BMBA031,
                            'is_agent_purchase' => $bom->BMBA022,
                            'status' => 'Y',
                            'customer_code' => $bom->BMBA004,
                            'effective_time' => $bom->BMBA005,
                            'failure_time' => $bom->BMBA006,
                        ];
                        
                        // 检查记录是否存在
                        $existingBom = BOM::where($primaryKey)->first();
                        
                        if (!$existingBom) {
                            // 🔧 修复：新建记录时包含version
                            BOM::create(array_merge($primaryKey, $businessData, ['version' => $version]));
                            $created++;
                        } else {
                            // 🔧 修复：检查业务数据是否有实际变更
                            $hasChanges = false;
                            
                            foreach ($businessData as $key => $newValue) {
                                $oldValue = $existingBom->$key;
                                
                                // 使用严格比较，正确处理数值类型
                                if ($oldValue !== $newValue) {
                                    // 特殊处理数值字段的类型转换
                                    if (in_array($key, ['base_quantity', 'child_quantity']) && 
                                        is_numeric($oldValue) && is_numeric($newValue) && 
                                        (float)$oldValue === (float)$newValue) {
                                        continue; // 数值相等，跳过
                                    }
                                    
                                    $hasChanges = true;
                                    Log::info("BOM业务数据变更检测", [
                                        'field' => $key,
                                        'old_value' => $oldValue,
                                        'new_value' => $newValue,
                                        'parent_material' => $bom->BMBA001,
                                        'child_material' => $bom->BMBA003
                                    ]);
                                    break;
                                }
                            }
                            
                            if ($hasChanges) {
                                // 🔧 修复：只有在有业务数据变更时才更新version和updated_at
                                $existingBom->update(array_merge($businessData, ['version' => $version]));
                                $updated++;
                            } else {
                                // 🔧 修复：无业务数据变更，完全跳过更新（不更新version、updated_at）
                                $skipped++;
                            }
                        }
                        
                        $processed++;
                    } catch (Exception $e) {
                        $errors++;
                        Log::warning("BOM转化失败", [
                            'parent_material' => $bom->BMBA001 ?? 'unknown',
                            'child_material' => $bom->BMBA003 ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });

            return [
                'success' => true,
                'type' => 'bom',
                'processed' => $processed,
                'created' => $created,
                'updated' => $updated,
                'skipped' => $skipped,
                'errors' => $errors,
                'message' => "成功转化 {$processed} 条BOM记录：新建 {$created} 条，更新 {$updated} 条，跳过 {$skipped} 条，{$errors} 条失败",
                'source_table' => 'BMBA_T (local)'
            ];

        } catch (Exception $e) {
            throw new Exception("BOM转化失败: " . $e->getMessage());
        }
    }

    /**
     * 检查客户数据表的完整性
     * 
     * @param string $locale 语言代码
     * @return array 检查结果
     */
    private function checkCustomerTablesIntegrity(string $locale): array
    {
        try {
            $result = [
                'ready' => true,
                'tables' => [],
                'warnings' => []
            ];
            
            // 检查PMAB_T表
            $pmabCount = DB::table('PMAB_T')
                ->where('PMABENT', '=', '40')
                ->where('PMABSITE', '=', 'ALL')
                ->whereNotNull('PMAB087')
                ->count();
                
            $result['tables']['PMAB_T'] = [
                'count' => $pmabCount,
                'status' => $pmabCount > 0 ? 'ok' : 'empty'
            ];
            
            // 检查PMAAL_T表
            $pmaalCount = DB::table('PMAAL_T')
                ->where('PMAALENT', '=', '40')
                ->where('PMAAL002', '=', $locale)
                ->count();
                
            $result['tables']['PMAAL_T'] = [
                'count' => $pmaalCount,
                'status' => $pmaalCount > 0 ? 'ok' : 'empty'
            ];
            
            // 检查关联完整性
            if ($pmabCount > 0 && $pmaalCount > 0) {
                $joinCount = DB::table('PMAB_T')
                    ->where('PMAB_T.PMABENT', '=', '40')
                    ->where('PMAB_T.PMABSITE', '=', 'ALL')
                    ->whereNotNull('PMAB_T.PMAB087')
                    ->join('PMAAL_T', function ($join) use ($locale) {
                        $join->on('PMAAL_T.PMAAL001', '=', 'PMAB_T.PMAB001')
                            ->where('PMAAL_T.PMAALENT', '=', '40')
                            ->where('PMAAL_T.PMAAL002', '=', $locale);
                    })
                    ->count();
                    
                $result['tables']['joined'] = [
                    'count' => $joinCount,
                    'status' => $joinCount > 0 ? 'ok' : 'no_match'
                ];
                
                if ($joinCount == 0) {
                    $result['ready'] = false;
                    $result['warnings'][] = 'PMAB_T和PMAAL_T表无法关联，可能是语言代码不匹配';
                }
            } else {
                $result['ready'] = false;
                if ($pmabCount == 0) {
                    $result['warnings'][] = 'PMAB_T表无数据';
                }
                if ($pmaalCount == 0) {
                    $result['warnings'][] = "PMAAL_T表无{$locale}语言数据";
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'ready' => false,
                'error' => $e->getMessage(),
                'tables' => [],
                'warnings' => ['数据完整性检查失败']
            ];
        }
    }

    /**
     * 检查物料数据表的完整性
     * 
     * @param string $locale 语言代码
     * @return array 检查结果
     */
    private function checkMaterialTablesIntegrity(string $locale): array
    {
        try {
            $result = [
                'ready' => true,
                'tables' => [],
                'warnings' => []
            ];
            
            $requiredTables = ['IMAA_T', 'IMAAL_T', 'IMAF_T', 'BMAA_T'];
            
            foreach ($requiredTables as $table) {
                $query = DB::table($table);
                
                // 添加表特定的过滤条件
                switch ($table) {
                    case 'IMAA_T':
                        $query->where('IMAAENT', '=', '40')
                            ->where('IMAASTUS', '=', 'Y');
                        break;
                    case 'IMAAL_T':
                        $query->where('IMAALENT', '=', '40')
                            ->where('IMAAL002', '=', $locale);
                        break;
                    case 'IMAF_T':
                        $query->where('IMAFENT', '=', '40')
                            ->where('IMAFSITE', '=', 'ALL');
                        break;
                    case 'BMAA_T':
                        $query->where('BMAAENT', '=', '40')
                            ->where('BMAASITE', '=', 'ALL');
                        break;
                }
                
                $count = $query->count();
                $result['tables'][$table] = [
                    'count' => $count,
                    'status' => $count > 0 ? 'ok' : 'empty'
                ];
                
                if ($count == 0) {
                    $result['warnings'][] = "{$table}表无数据";
                    if (in_array($table, ['IMAA_T', 'IMAAL_T', 'IMAF_T'])) {
                        // 核心表无数据时标记为未就绪
                        $result['ready'] = false;
                    }
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'ready' => false,
                'error' => $e->getMessage(),
                'tables' => [],
                'warnings' => ['数据完整性检查失败']
            ];
        }
    }

    /**
     * 转化物料数据（优化版本：支持增量转化和变更检测）
     * 从本地同步的 IMAA_T、IMAAL_T、IMAF_T、BMAA_T 表转化物料信息
     */
    private function transformMaterials(string $companyCode, array $options = []): array
    {
        $processed = 0;
        $errors = 0;
        $skipped = 0;
        $updated = 0;
        $created = 0;
        $locale = $options['locale'] ?? 'zh_CN';
        
        // 🔧 优化：增量转化选项，默认开启增量模式
        $incrementalMode = $options['incremental'] ?? true;
        $triggerTable = $options['trigger_table'] ?? null;
        
        try {
            // 检查相关表的数据完整性
            $integrityCheck = $this->checkMaterialTablesIntegrity($locale);
            if (!$integrityCheck['ready']) {
                Log::warning("物料数据表完整性检查未通过，继续转化但数据可能不完整", [
                    'integrity_check' => $integrityCheck,
                    'locale' => $locale
                ]);
            }
            
            // 🔧 构建基础查询
            $query = DB::table('IMAA_T')
                ->where('IMAAENT', '=', '40')
                ->where('IMAASTUS', '=', 'Y')
                ->join('IMAAL_T', function ($join) use ($locale) {
                    $join->on('IMAAL_T.IMAAL001', '=', 'IMAA_T.IMAA001')
                        ->where('IMAAL_T.IMAALENT', '=', '40')
                        ->where('IMAAL_T.IMAAL002', '=', $locale);
                })
                ->join('IMAF_T', function ($join) {
                    $join->on('IMAF_T.IMAF001', '=', 'IMAA_T.IMAA001')
                        ->where('IMAF_T.IMAFENT', '=', '40')
                        ->where('IMAF_T.IMAFSITE', '=', 'ALL');
                })
                ->leftJoin('BMAA_T', function ($join) {
                    $join->on('BMAA_T.BMAA001', '=', 'IMAA_T.IMAA001')
                        ->where('BMAA_T.BMAAENT', '=', '40')
                        ->where('BMAA_T.BMAASITE', '=', 'ALL');
                })
                ->orderBy('IMAA_T.IMAA001');

            // 🔧 修复：增量转化逻辑
            if ($incrementalMode && $triggerTable) {
                $cutoffTime = now()->subMinutes(10); // 只处理最近10分钟变更的物料
                
                Log::info("使用增量转化模式", [
                    'trigger_table' => $triggerTable,
                    'cutoff_time' => $cutoffTime->format('Y-m-d H:i:s'),
                    'locale' => $locale
                ]);
                
                // 🔧 修复：正确的Oracle字段名映射
                $tableFieldMapping = [
                    'IMAA_T' => 'IMAA001',
                    'IMAAL_T' => 'IMAAL001', 
                    'IMAF_T' => 'IMAF001',
                    'BMAA_T' => 'BMAA001'
                ];
                
                if (!isset($tableFieldMapping[$triggerTable])) {
                    Log::warning("未知的触发表，使用全量转化", ['trigger_table' => $triggerTable]);
                } else {
                    // 🔧 修复：从Oracle的sync_change_log表查询最近变更
                    try {
                        $recentChanges = DB::connection('oracle')
                            ->table('sync_change_log')
                            ->where('table_name', $triggerTable)
                            ->where('sync_status', 1)
                            ->where('change_time', '>=', $cutoffTime)
                            ->get(['pk_json']);
                            
                        $materialCodes = [];
                        foreach ($recentChanges as $change) {
                            $pk = json_decode($change->pk_json, true);
                            $fieldName = $tableFieldMapping[$triggerTable];
                            if (isset($pk[$fieldName])) {
                                $materialCodes[] = $pk[$fieldName];
                            }
                        }
                        
                        $materialCodes = array_unique($materialCodes);
                        
                        if (!empty($materialCodes)) {
                            $query->whereIn('IMAA_T.IMAA001', $materialCodes);
                            Log::info("增量模式：处理变更的物料", [
                                'trigger_table' => $triggerTable,
                                'material_count' => count($materialCodes),
                                'material_codes' => array_slice($materialCodes, 0, 10) // 只记录前10个
                            ]);
                        } else {
                            Log::info("增量模式：{$triggerTable}表没有最近变更的记录，跳过转化");
                            return [
                                'success' => true,
                                'type' => 'material',
                                'processed' => 0,
                                'skipped' => 0,
                                'message' => "增量模式：{$triggerTable}表没有最近变更，跳过转化",
                                'mode' => 'incremental',
                                'trigger_table' => $triggerTable
                            ];
                        }
                    } catch (Exception $e) {
                        Log::warning("增量模式查询失败，回退到全量转化", [
                            'trigger_table' => $triggerTable,
                            'error' => $e->getMessage()
                        ]);
                        // 继续使用全量查询
                    }
                }
            } else if (!$incrementalMode) {
                Log::info("使用全量转化模式", ['locale' => $locale]);
            }

            // 🔧 新增：分批处理逻辑，每批1000条记录
            $batchSize = $options['batch_size'] ?? 1000;
            $totalRecords = $query->count();
            
            Log::info("开始物料转化", [
                'mode' => $incrementalMode ? 'incremental' : 'full',
                'total_records' => $totalRecords,
                'batch_size' => $batchSize,
                'trigger_table' => $triggerTable
            ]);

            $query->chunk($batchSize, function ($chunk) use ($companyCode, $locale, &$processed, &$errors, &$skipped, &$updated, &$created) {
                foreach ($chunk as $material) {
                    try {
                        // 🔧 优化：变更检测机制
                        $existingMaterial = Material::where('company_code', $companyCode)
                            ->where('material_code', $material->IMAA001)
                            ->first();

                        // 准备新的材料数据
                        $materialData = [
                            'figure' => $material->IMAA041,
                            'unit' => $material->IMAA006,
                            'category_code' => $material->IMAA003,
                            'gross_weight' => $material->IMAA016 ? (float)$material->IMAA016 : 0,
                            'net_weight' => $material->IMAA017 ? (float)$material->IMAA017 : 0,
                            'paint_area' => $material->IMAAUD011 ? (float)$material->IMAAUD011 : 0,
                            'work_hours' => $material->IMAAUD011 ? (float)$material->IMAAUD011 : 0,
                            'length' => $material->IMAA019 ? (float)$material->IMAA019 : 0,
                            'width' => $material->IMAA020 ? (float)$material->IMAA020 : 0,
                            'height' => $material->IMAA021 ? (float)$material->IMAA021 : 0,
                            'supply_type' => $material->IMAF013,
                            'is_package' => ($material->BMAAUD011 == 1) ? 'Y' : 'N',
                            'status' => 'Y',
                        ];

                        // 🔧 修复：正确的变更检测逻辑
                        if (!$existingMaterial) {
                            // 新记录：直接创建
                            Material::create(array_merge([
                                'company_code' => $companyCode,
                                'material_code' => $material->IMAA001,
                            ], $materialData));
                            $created++;
                        } else {
                            // 现有记录：检查是否有变化
                            $hasChanges = false;
                            
                            foreach ($materialData as $key => $value) {
                                $oldValue = $existingMaterial->{$key};
                                
                                // 处理数值类型的比较
                                if (in_array($key, ['gross_weight', 'net_weight', 'paint_area', 'work_hours', 'length', 'width', 'height'])) {
                                    $oldValue = $oldValue ? (float)$oldValue : 0.0;
                                    $value = $value ? (float)$value : 0.0;
                                }
                                
                                // 处理字符串类型的比较
                                if (in_array($key, ['figure', 'unit', 'category_code', 'supply_type', 'is_package', 'status'])) {
                                    $oldValue = (string)($oldValue ?? '');
                                    $value = (string)($value ?? '');
                                }
                                
                                if ($oldValue !== $value) {
                                    $hasChanges = true;
                                    Log::debug("检测到字段变化", [
                                        'material_code' => $material->IMAA001,
                                        'field' => $key,
                                        'old_value' => $oldValue,
                                        'new_value' => $value
                                    ]);
                                    break;
                                }
                            }
                            
                            if ($hasChanges) {
                                // 有业务数据变更，更新记录
                                $existingMaterial->update($materialData);
                                $updated++;
                            } else {
                                // 无业务数据变更，跳过更新
                                $skipped++;
                            }
                        }

                        // 🔧 处理物料翻译信息（同样添加变更检测）
                        if ($material->IMAAL003 || $material->IMAAL004) {
                            $existingTranslation = MaterialTranslation::where('company_code', $companyCode)
                                ->where('material_code', $material->IMAA001)
                                ->where('locale', $locale)
                                ->first();

                            $translationData = [
                                'product_name' => $material->IMAAL003 ? $material->IMAAL003 : $material->IMAAL004,
                                'specification' => $material->IMAAL004 ? $material->IMAAL004 : $material->IMAAL003,
                            ];

                            $hasTranslationChanges = false;
                            if (!$existingTranslation) {
                                $hasTranslationChanges = true;
                            } else {
                                foreach ($translationData as $key => $value) {
                                    $oldValue = (string)($existingTranslation->{$key} ?? '');
                                    $newValue = (string)($value ?? '');
                                    
                                    if ($oldValue !== $newValue) {
                                        $hasTranslationChanges = true;
                                        break;
                                    }
                                }
                            }

                            if ($hasTranslationChanges) {
                                MaterialTranslation::updateOrCreate(
                                    [
                                        'company_code' => $companyCode,
                                        'material_code' => $material->IMAA001,
                                        'locale' => $locale,
                                    ],
                                    $translationData
                                );
                            }
                        }

                        $processed++;
                    } catch (Exception $e) {
                        $errors++;
                        Log::warning("物料转化失败", [
                            'material_code' => $material->IMAA001 ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });

            $resultData = [
                'success' => true,
                'type' => 'material',
                'processed' => $processed,
                'created' => $created,
                'updated' => $updated,
                'skipped' => $skipped,
                'errors' => $errors,
                'message' => "转化完成: 处理{$processed}条记录，新建{$created}条，更新{$updated}条，跳过{$skipped}条，失败{$errors}条",
                'source_tables' => 'IMAA_T + IMAAL_T + IMAF_T + BMAA_T (local)',
                'mode' => $incrementalMode ? 'incremental' : 'full',
                'total_records' => $totalRecords
            ];
            
            if (isset($triggerTable)) {
                $resultData['trigger_table'] = $triggerTable;
            }
            
            return $resultData;

        } catch (Exception $e) {
            throw new Exception("物料转化失败: " . $e->getMessage());
        }
    }

    /**
     * 批量转化多个类型的数据
     * 
     * @param array $types 转化类型列表
     * @param string $companyCode 公司代码
     * @param array $options 转化选项
     * @return array 批量转化结果
     */
    public function batchTransform(array $types, string $companyCode = 'TB', array $options = []): array
    {
        // 🔧 新增：检查是否启用并发转化
        $concurrentMode = $options['concurrent'] ?? false;
        
        if ($concurrentMode && count($types) > 1) {
            return $this->concurrentTransform($types, $companyCode, $options);
        }
        
        // 原有的串行转化逻辑
        $results = [];
        $totalDuration = 0;
        $totalProcessed = 0;
        $totalErrors = 0;

        foreach ($types as $type) {
            $result = $this->transform($type, $companyCode, $options);
            $results[$type] = $result;
            
            if (isset($result['duration'])) {
                $totalDuration += $result['duration'];
            }
            if (isset($result['processed'])) {
                $totalProcessed += $result['processed'];
            }
            if (isset($result['errors'])) {
                $totalErrors += $result['errors'];
            }
        }

        return [
            'success' => true,
            'results' => $results,
            'summary' => [
                'total_types' => count($types),
                'total_duration' => round($totalDuration, 2),
                'total_processed' => $totalProcessed,
                'total_errors' => $totalErrors,
                'company_code' => $companyCode,
                'data_source' => 'local_synced_tables',
                'mode' => 'sequential'
            ]
        ];
    }

    /**
     * 🔧 新增：并发转化多个类型的数据
     * 
     * @param array $types 转化类型列表
     * @param string $companyCode 公司代码
     * @param array $options 转化选项
     * @return array 并发转化结果
     */
    public function concurrentTransform(array $types, string $companyCode = 'TB', array $options = []): array
    {
        try {
            Log::info("开始并发转化", [
                'types' => $types,
                'company_code' => $companyCode,
                'total_types' => count($types)
            ]);

            $startTime = microtime(true);
            
            // 🔧 分析转化依赖关系，确定可以并发的组合
            $groups = $this->analyzeTransformDependencies($types);
            
            $allResults = [];
            $totalProcessed = 0;
            $totalErrors = 0;
            
            // 按依赖组串行执行，组内并发执行
            foreach ($groups as $groupIndex => $group) {
                Log::info("执行转化组 " . ($groupIndex + 1), [
                    'group_types' => $group,
                    'group_size' => count($group)
                ]);
                
                if (count($group) === 1) {
                    // 单个转化，直接执行
                    $type = $group[0];
                    $result = $this->transform($type, $companyCode, $options);
                    $allResults[$type] = $result;
                } else {
                    // 多个转化，使用队列并发执行
                    $groupResults = $this->executeTransformGroup($group, $companyCode, $options);
                    $allResults = array_merge($allResults, $groupResults);
                }
                
                // 统计当前组的结果
                foreach ($group as $type) {
                    if (isset($allResults[$type])) {
                        $result = $allResults[$type];
                        $totalProcessed += $result['processed'] ?? 0;
                        $totalErrors += $result['errors'] ?? 0;
                    }
                }
            }
            
            $endTime = microtime(true);
            $totalDuration = round($endTime - $startTime, 2);
            
            Log::info("并发转化完成", [
                'types' => $types,
                'total_duration' => $totalDuration,
                'total_processed' => $totalProcessed,
                'total_errors' => $totalErrors
            ]);
            
            return [
                'success' => true,
                'results' => $allResults,
                'summary' => [
                    'total_types' => count($types),
                    'total_duration' => $totalDuration,
                    'total_processed' => $totalProcessed,
                    'total_errors' => $totalErrors,
                    'company_code' => $companyCode,
                    'data_source' => 'local_synced_tables',
                    'mode' => 'concurrent',
                    'dependency_groups' => count($groups)
                ]
            ];
            
        } catch (Exception $e) {
            Log::error("并发转化失败", [
                'types' => $types,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'types' => $types,
                'company_code' => $companyCode
            ];
        }
    }

    /**
     * 🔧 新增：分析转化依赖关系
     * 
     * @param array $types 转化类型列表
     * @return array 依赖组列表
     */
    private function analyzeTransformDependencies(array $types): array
    {
        // 定义转化依赖关系
        $dependencies = [
            'category' => [], // 无依赖，可以并发
            'customer' => [], // 无依赖，可以并发
            'material' => [], // 无依赖，可以并发
            'bom' => []       // 无依赖，可以并发（BOM不依赖Material存在）
        ];
        
        // 🔧 关键发现：所有转化类型都可以并发执行！
        // - category: 只依赖RTAXL_T
        // - customer: 只依赖PMAB_T + PMAAL_T  
        // - material: 依赖IMAA_T + IMAAL_T + IMAF_T + BMAA_T
        // - bom: 只依赖BMBA_T
        // 它们之间没有业务逻辑依赖，都是从Oracle同步表转化到MySQL业务表
        
        Log::info("转化依赖分析结果：所有类型可以并发执行", [
            'types' => $types,
            'dependencies' => $dependencies
        ]);
        
        // 所有类型都可以在一个组内并发执行
        return [$types];
    }

    /**
     * 🔧 新增：执行转化组（组内并发）
     * 
     * @param array $types 转化类型列表
     * @param string $companyCode 公司代码
     * @param array $options 转化选项
     * @return array 转化结果
     */
    private function executeTransformGroup(array $types, string $companyCode, array $options): array
    {
        // 检查是否有队列工作进程运行
        $queueRunning = $this->checkTransformQueueStatus();
        
        if (!$queueRunning) {
            Log::warning("转化队列未运行，改为串行执行", [
                'types' => $types
            ]);
            
            // 回退到串行执行
            $results = [];
            foreach ($types as $type) {
                $results[$type] = $this->transform($type, $companyCode, $options);
            }
            return $results;
        }
        
        // 🔧 使用队列并发执行转化任务
        Log::info("使用队列并发执行转化组", [
            'types' => $types,
            'group_size' => count($types)
        ]);
        
        $jobs = [];
        $batchId = \Illuminate\Support\Str::uuid()->toString();
        
        // 为每个转化类型创建队列任务
        foreach ($types as $type) {
            $jobs[] = new \App\Jobs\TransformDataJob(
                [$type], 
                $companyCode, 
                array_merge($options, [
                    'batch_id' => $batchId,
                    'group_execution' => true
                ])
            );
        }
        
        try {
            // 创建批处理任务
            $batch = \Illuminate\Support\Facades\Bus::batch($jobs)
                ->name("并发转化批处理 - " . implode(',', $types))
                ->allowFailures()
                ->dispatch();
                
            Log::info("并发转化批处理已启动", [
                'batch_id' => $batchId,
                'laravel_batch_id' => $batch->id,
                'types' => $types,
                'job_count' => count($jobs)
            ]);
            
            // 🔧 等待批处理完成（最多等待10分钟）
            $maxWaitTime = 600; // 10分钟
            $checkInterval = 5;  // 5秒检查一次
            $waitedTime = 0;
            
            while (!$batch->finished() && $waitedTime < $maxWaitTime) {
                sleep($checkInterval);
                $waitedTime += $checkInterval;
                $batch = $batch->fresh(); // 刷新批处理状态
                
                Log::debug("等待并发转化完成", [
                    'batch_id' => $batch->id,
                    'progress' => $batch->progress(),
                    'processed_jobs' => $batch->processedJobs(),
                    'total_jobs' => $batch->totalJobs,
                    'waited_time' => $waitedTime
                ]);
            }
            
            if ($batch->finished()) {
                Log::info("并发转化批处理完成", [
                    'batch_id' => $batch->id,
                    'total_jobs' => $batch->totalJobs,
                    'failed_jobs' => $batch->failedJobs,
                    'duration' => $waitedTime . '秒'
                ]);
                
                // 构造返回结果（模拟同步执行的结果格式）
                $results = [];
                foreach ($types as $type) {
                    $results[$type] = [
                        'success' => true,
                        'type' => $type,
                        'message' => '并发转化完成',
                        'batch_id' => $batch->id,
                        'mode' => 'concurrent'
                    ];
                }
                
                return $results;
                
            } else {
                Log::warning("并发转化超时，部分任务可能仍在执行", [
                    'batch_id' => $batch->id,
                    'waited_time' => $waitedTime,
                    'progress' => $batch->progress()
                ]);
                
                // 超时情况下也返回结果
                $results = [];
                foreach ($types as $type) {
                    $results[$type] = [
                        'success' => false,
                        'type' => $type,
                        'message' => '并发转化超时',
                        'batch_id' => $batch->id,
                        'mode' => 'concurrent_timeout'
                    ];
                }
                
                return $results;
            }
            
        } catch (Exception $e) {
            Log::error("并发转化批处理失败", [
                'types' => $types,
                'error' => $e->getMessage()
            ]);
            
            // 批处理失败，回退到串行执行
            $results = [];
            foreach ($types as $type) {
                $results[$type] = [
                    'success' => false,
                    'type' => $type,
                    'error' => '并发执行失败，回退到串行: ' . $e->getMessage(),
                    'mode' => 'fallback_to_sequential'
                ];
            }
            
            return $results;
        }
    }

    /**
     * 🔧 新增：检查转化队列状态
     * 
     * @return bool 队列是否正在运行
     */
    private function checkTransformQueueStatus(): bool
    {
        try {
            // 检查是否有transform队列的工作进程在运行
            $activeJobs = DB::table('jobs')
                ->where('queue', 'transform')
                ->where('reserved_at', '!=', null)
                ->count();
                
            // 检查最近是否有transform任务被处理
            $recentProcessed = DB::table('failed_jobs')
                ->where('queue', 'transform')
                ->where('failed_at', '>=', now()->subMinutes(10))
                ->exists();
                
            // 简单检查：如果有活跃任务或最近有处理记录，认为队列正在运行
            return $activeJobs > 0 || $recentProcessed;
            
        } catch (Exception $e) {
            Log::warning("检查转化队列状态失败", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取转化统计信息
     * 
     * @param string $companyCode 公司代码
     * @return array 统计信息
     */
    public function getTransformStats(string $companyCode = 'TB'): array
    {
        try {
            return [
                'categories' => Category::where('company_code', $companyCode)->count(),
                'customers' => Customer::where('company_code', $companyCode)->count(),
                'boms' => BOM::where('company_code', $companyCode)->count(),
                'materials' => Material::where('company_code', $companyCode)->count(),
                'material_translations' => MaterialTranslation::where('company_code', $companyCode)->count(),
            ];
        } catch (Exception $e) {
            Log::error("获取转化统计失败", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 检查本地同步表的数据完整性
     * 
     * @return array 检查结果
     */
    public function checkLocalSyncTables(): array
    {
        $tables = [
            'RTAXL_T' => '分类数据表',
            'PMAB_T' => '客户主表',
            'PMAAL_T' => '客户多语言表',
            'BMBA_T' => 'BOM表',
            'IMAA_T' => '物料主表',
            'IMAAL_T' => '物料多语言表',
            'IMAF_T' => '物料工厂表',
            'BMAA_T' => '物料BOM主表'
        ];

        $result = [];
        
        foreach ($tables as $table => $description) {
            try {
                $count = DB::table($table)->count();
                $result[$table] = [
                    'description' => $description,
                    'record_count' => $count,
                    'status' => $count > 0 ? 'available' : 'empty'
                ];
            } catch (Exception $e) {
                $result[$table] = [
                    'description' => $description,
                    'record_count' => 0,
                    'status' => 'not_exist',
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success' => true,
            'tables' => $result,
            'total_tables' => count($tables),
            'available_tables' => count(array_filter($result, fn($t) => $t['status'] === 'available')),
            'check_time' => now()->format('Y-m-d H:i:s')
        ];
    }
} 