<?php

namespace App\Models;

use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BOM extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'BOM';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'parent_material_code',
        'child_material_code',
        'base_quantity',
        'child_quantity',
        'unit',
        'is_order_expand',
        'is_package',
        'is_optional',
        'is_customer_material',
        'is_agent_purchase',
        'status',
        'customer_code',
        'effective_time',
        'failure_time',
        'loss_rate',
        'version',
        'remark'
    ];

    /**
     * 获取料件列表
     *
     * 查询条件：
     * - 关联 material_translations 表获取中文描述（locale = 'zh_CN'）
     *
     * @param string $companyCode 公司编码
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @param array|null $categories 产品分类筛选
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getBOMList(
        string $companyCode = 'TB',
        int $page = 1,
        int $perPage = 20,
        ?string $search = null,
        ?string $sortField = null,
        string $sortDirection = 'asc',
        ?array $categories = null
    ) {
        $date = Carbon::tomorrow()->subSecond();

        // 字段映射关系
        $dbFieldMap = [
            'category_code' => 'materials.category_code',
            'category_name' => 'categories.category_name',
            'figure' => 'materials.figure',
            'product_name' => 'material_translations.product_name',
            'specification' => 'material_translations.specification',
            'material_code' => 'materials.material_code'
        ];
        
        // 构建查询
        $query = Material::where('materials.status', 'Y')
            ->where('materials.company_code', $companyCode) // 添加公司筛选
            ->join('material_translations', function ($join) {
                $join->on('material_translations.material_code', '=', 'materials.material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->leftJoin('categories', function ($join) {
                $join->on('categories.category_code', '=', 'materials.category_code')
                    ->where('categories.locale', '=', 'zh_CN')
                    ->on('categories.company_code', '=', 'materials.company_code');
            })
            ->whereExists(function ($subQuery) use ($date, $companyCode) {
                $subQuery->select(DB::raw(1))
                    ->from('BOM')
                    ->whereColumn('BOM.company_code', 'materials.company_code')
                    ->whereColumn('BOM.parent_material_code', 'materials.material_code')
                    ->where('BOM.company_code', $companyCode)
                    ->where('BOM.effective_time', '<=', $date)
                    ->where(function ($query) use ($date) {
                        $query->whereNull('BOM.failure_time')
                            ->orWhere('BOM.failure_time', '>=', $date);
                    });
            })
            ->select([
                'materials.figure',
                'materials.material_code',
                'material_translations.product_name',
                'material_translations.specification',
                'materials.category_code',
                'categories.category_name'
            ]);

        // 产品分类筛选
        $query->when(!empty($categories), function ($q) use ($categories) {
            $q->whereIn('materials.category_code', $categories);
        });

        // 搜索关键词处理
        $query->when($search, function ($q) use ($search) {
            $q->where(function ($q) use ($search) {
                $q->where('materials.material_code', 'like', "%{$search}%")
                    ->orWhere('material_translations.product_name', 'like', "%{$search}%")
                    ->orWhere('material_translations.specification', 'like', "%{$search}%");
            });
        });

        // 排序逻辑
        if ($sortField && isset($dbFieldMap[$sortField])) {
            $query->orderBy($dbFieldMap[$sortField], $sortDirection);
        } else {
            // 默认排序
            $query->orderBy('materials.category_code')
                ->orderBy('materials.figure')
                ->orderBy('materials.material_code');
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 获取产品分类
     *
     * 本函数通过连接两个表（materials和categories）来获取产品分类及其对应的中文标签
     * 选择 categories.category_code（分类代码）和 categories.name（中文标签）字段，并按 categories.category_code 排序
     * @param string $companyCode 公司编码
     * @return \Illuminate\Database\Eloquent\Collection|static[] 返回产品分类的集合，包括分类代码和中文标签
     */
    public static function getProductCategory($companyCode = 'TB')
    {
        $date = Carbon::tomorrow()->subSecond();

        $query = Material::where('materials.company_code', '=', $companyCode)
            ->join('categories', function ($join) {
                $join->on('categories.company_code', '=', 'materials.company_code')
                    ->on('categories.category_code', '=', 'materials.category_code')
                    ->where('categories.locale', '=', 'zh_CN');
            })
            ->whereExists(function ($query) use ($date) {
                $query->select(DB::raw(1))
                    ->from('BOM')
                    ->whereColumn('BOM.company_code', 'materials.company_code')
                    ->whereColumn('BOM.parent_material_code', 'materials.material_code')
                    ->where('BOM.effective_time', '<=', $date)
                    ->where(function ($query) use ($date) {
                        $query->whereNull('BOM.failure_time')
                            ->orWhere('BOM.failure_time', '>=', $date);
                    });
            })
            ->select('categories.category_code as value', 'categories.category_name as label')
            ->groupBy('categories.category_code', 'categories.category_name')
            ->orderBy('categories.category_code');

        // $sql = $query->toSql();
        // $bindings = $query->getBindings();
        // \Log::info("查询SQL: {$sql}", $bindings);
        
        // $result = $query->get();
        // \Log::info("查询结果数量: " . count($result));

        return $query->get();
    }

    /**
     * 根据料号获取BOM树结构
     *
     * @param string $companyCode 公司编码
     * @param string $code BOM编码
     * @param string $mode 展示模式，支持 'multi'多阶, 'leaf'尾阶
     * @param date $date 有效日期
     * @param bool $includeOptional 是否包含可选件
     * @param string|null $customerCode 客户编号，筛选特定客户的可选件
     * @return array|null BOM树结构
     */
    public static function getBOMTreeByCode($companyCode = 'TB', $code, $mode = 'multi', $date = null, $includeOptional = false, $customerCode = null)
    {
        try {
            // 如果未传入$date，则使用当前日期的23:59:59
            if ($date === null) {
                $date = Carbon::tomorrow()->subSecond();
            }

            // 检查顶级BOM是否存在
            $exists = self::where('BOM.company_code', '=', $companyCode)
                ->where('BOM.parent_material_code', $code)
                ->where('BOM.effective_time', '<=', $date)
                ->where(function ($query) use ($date) {
                    $query->whereNull('BOM.failure_time')
                        ->orWhere('BOM.failure_time', '>=', $date);
                })
                ->exists();

            if (!$exists) {
                return null;
            }

            // todo : 记录已处理的节点，防止循环引用导致无限递归
            $processedNodes = [];

            // 根据模式选择不同的查询方法
            switch ($mode) {
                case 'leaf':
                    // 尾阶模式：只获取叶子节点
                    return self::getLeafNodes($companyCode, $code, $date, $includeOptional, $customerCode);
                case 'multi':
                default:
                    // 多阶模式（默认）：递归获取所有子BOM
                    return self::getBOMTreeWithChildren($companyCode, $code, 0, $date, $includeOptional, $customerCode);
            }
        } catch (\Exception $e) {
            \Log::error('获取BOM树结构失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 递归获取BOM树结构
     *
     * @param string $companyCode 公司编码
     * @param string $code 父级BOM编码
     * @param int $level 当前层级
     * @param date $date 有效日期
     * @param bool $includeOptional 是否包含可选件
     * @param string|null $customerCode 客户编号，筛选特定客户的可选件
     * @return array BOM树结构
     */
    private static function getBOMTreeWithChildren($companyCode, $code, $level = 0, $date, $includeOptional = false, $customerCode = null)
    {
        $res = [];

        // 首次获取根节点信息
        if ($level === 0) {
            $root = MaterialTranslation::where([
                        ['company_code', '=', $companyCode],
                        ['material_code', '=', $code],
                        ['locale', '=', 'zh_CN']
                    ])
                    ->first();

            if (!$root) return [];

            $res = [
                'level' => 0,
                'material_code' => $root->material_code,
                'product_name' => $root->product_name,
                'specification' => $root->specification,
                'childs' => self::getBOMTreeWithChildren($companyCode, $code, 1, $date, $includeOptional, $customerCode)
            ];

            return $res;
        }

        // 查询子料结构
        $query = self::where([
                ['BOM.company_code', '=', $companyCode], 
                ['BOM.parent_material_code', '=', $code],
                ['BOM.effective_time', '<=', $date] // 生效日期早于等于当前日期
            ])
            ->where(function ($query) use ($date) {
                $query->whereNull('BOM.failure_time') // 失效日期为空
                    ->orWhere('BOM.failure_time', '>=', $date); // 或失效日期未到
            })
            ->join('materials', function ($join) {
                $join->on('materials.company_code', '=', 'BOM.company_code')
                    ->on('materials.material_code', '=', 'BOM.child_material_code');
            })
            ->join('material_translations', function ($join) {
                $join->on('material_translations.company_code', '=', 'BOM.company_code')
                    ->on('material_translations.material_code', '=', 'BOM.child_material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->leftJoin('customers', function ($join) {
                $join->on('customers.company_code', '=', 'BOM.company_code')
                    ->on('customers.code', '=', 'BOM.customer_code');
            });

        // 添加可选件筛选逻辑
        if (!$includeOptional) {
            // 如果不包含可选件，则只选择非可选件的记录
            $query->where('BOM.is_optional', '!=', 'Y');
        } else if ($customerCode) {
            // 如果指定了客户编号，对可选件进行客户筛选
            $query->where(function ($q) use ($customerCode) {
                $q->where('BOM.is_optional', '!=', 'Y') // 非可选件
                    ->orWhere(function ($subQ) use ($customerCode) {
                        $subQ->where('BOM.is_optional', '=', 'Y') // 可选件
                            ->where('BOM.customer_code', '=', $customerCode); // 指定客户
                    });
            });
        }

        $subNodes = $query->orderBy('BOM.is_optional', 'desc') // 按可选件排序
                ->orderBy('BOM.child_material_code', 'asc') // 按料号排序
                ->get([
                    'materials.material_code', // 料号
                    'material_translations.product_name', // 品名
                    'material_translations.specification', // 规格
                    'materials.supply_type', // 补给策略 1.采购,2.自制,3.委外,4.无
                    'BOM.base_quantity', // 主件底数
                    'BOM.child_quantity', // 组成用量
                    'BOM.unit', // 单位
                    'materials.gross_weight', // 毛重
                    'materials.net_weight', // 净重
                    'materials.length', // 长
                    'materials.width', // 宽
                    'materials.height', // 厚度
                    'materials.figure', // 图号
                    'BOM.is_optional', // 是否可选件
                    'BOM.customer_code', // 客户编号
                    'customers.full_name', // 客户名称
                    'BOM.is_order_expand', // 工单展开选项：1.不展开,2.不展开，自动开立子工单,3.展开,4.开窗询问
                    'BOM.is_agent_purchase', // 是否代买料
                    'BOM.is_customer_material', // 是否客供料
                    'materials.paint_area', // 涂装面积
                    'materials.work_hours', // 成品工时
                    'BOM.effective_time', // 生效日期
                    'BOM.failure_time', // 失效日期
                ]);

        // 处理每个子节点
        foreach ($subNodes as $subNode) {
            // 检查是否有下级
            $hasChild = false;

            // 如果料件类型为非采购件
            if (in_array($subNode->supply_type, ['2', '3', '4'])) {
                // 检查是否存在向下的BOM结构
                $hasChild = self::where([
                        ['BOM.company_code', '=', $companyCode],
                        ['BOM.parent_material_code', '=', $subNode->material_code],
                        ['BOM.effective_time', '<=', $date] // 生效日期早于等于当前日期
                    ])
                    ->where(function ($query) use ($date) {
                        $query->whereNull('BOM.failure_time') // 失效日期为空
                            ->orWhere('BOM.failure_time', '>=', $date); // 或失效日期未到
                    });

                // 应用与主查询相同的可选件筛选逻辑
                if (!$includeOptional) {
                    $hasChild->where('BOM.is_optional', '!=', 'Y');
                } else if ($customerCode) {
                    $hasChild->where(function ($q) use ($customerCode) {
                        $q->where('BOM.is_optional', '!=', 'Y')
                            ->orWhere(function ($subQ) use ($customerCode) {
                                $subQ->where('BOM.is_optional', '=', 'Y')
                                    ->where('BOM.customer_code', '=', $customerCode);
                            });
                    });
                }

                $hasChild = $hasChild->exists();
            }

            // 如果是叶子节点或没有子节点，直接添加到结果中
            if (!$hasChild) {
                $res[] = [
                    ...$subNode->toArray(),
                    'level' => $level,
                    'is_leaf' => true
                ];
                continue;
            }

            // 对于有子节点的记录，递归获取子结构
            $res[] = [
                ...$subNode->toArray(),
                'level' => $level,
                'childs' => self::getBOMTreeWithChildren($companyCode, $subNode->material_code, $level + 1, $date, $includeOptional, $customerCode)
            ];
        }

        return $res;
    }

    /**
     * 获取尾阶BOM树结构（只显示叶子节点）
     *
     * @param string $companyCode 公司编码
     * @param string $code root编码
     * @param Carbon $date 有效日期
     * @param bool $includeOptional 是否包含可选件
     * @param string|null $customerCode 客户编号，筛选特定客户的可选件
     * @return array BOM树结构
     */
    private static function getLeafNodes($companyCode, $code, $date, $includeOptional = false, $customerCode = null)
    {
        $res = [];

        $root = MaterialTranslation::where([
                ['company_code', '=', $companyCode],
                ['material_code', '=', $code],
                ['locale', '=', 'zh_CN']
            ])
            ->first();

        if (!$root) return [];

        $res = [
            'level' => 0,
            'material_code' => $root->material_code,
            'product_name' => $root->product_name,
            'specification' => $root->specification,
            'childs' => self::findLeafNodes($companyCode, $code, 1, $date, $includeOptional, $customerCode)
        ];

        // 对childs先按is_optional（是否可选件）排序，再按material_code（料号）排序
        usort($res['childs'], function ($a, $b) {
            if ($a['is_optional'] != $b['is_optional']) {
                return strcmp($a['is_optional'], $b['is_optional']);
            }
            return strcmp($a['material_code'], $b['material_code']);
        });

        //将dosage的值覆盖bmba011 的值
        foreach ($res['childs'] as $key => $value) {
            $res['childs'][$key]['child_quantity'] = $res['childs'][$key]['dosage'];
            $res['childs'][$key]['net_weight'] = $res['childs'][$key]['net_weight'] * $res['childs'][$key]['dosage'];
        }

        return $res;
    }

    /**
     * 递归查找叶子节点 (Eloquent优化版)
     *
     * @param string $companyCode 公司编码
     * @param string $code 父级BOM编码
     * @param int $currentLevel 当前层级
     * @param Carbon $date 有效日期
     * @param bool $includeOptional 是否包含可选件
     * @param string|null $customerCode 客户编号
     * @param float $pCon 用量乘数
     * @return array
     */
    private static function findLeafNodes(string $companyCode, string $code, int $currentLevel = 1, $date, bool $includeOptional = false, ?string $customerCode = null, float $pCon = 1): array
    {
        // 如果料号首字母是5，则认为已经是叶子节点，不再往下遍历
        if (strlen($code) > 0 && substr($code, 0, 1) === '5') {
            // 获取当前节点信息
            $node = self::where([
                ['BOM.company_code', '=', $companyCode], 
                ['BOM.parent_material_code', '=', $code],
                ['BOM.effective_time', '<=', $date] // 生效日期早于等于当前日期
            ])
            ->where(function ($query) use ($date) {
                $query->whereNull('BOM.failure_time')
                    ->orWhere('BOM.failure_time', '>=', $date);
            })
            ->join('material_translations', function ($join) {
                $join->on('material_translations.company_code', '=', 'BOM.company_code')
                    ->on('material_translations.material_code', '=', 'BOM.child_material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->leftJoin('customers', function ($join) {
                $join->on('customers.company_code', '=', 'BOM.company_code')
                    ->on('customers.code', '=', 'BOM.customer_code');
            })
            ->first([
                'materials.material_code', //料号
                'material_translations.product_name', //品名
                'material_translations.specification', //规格
                'materials.is_package', //是否为套包 1是
                'materials.supply_type', //补给策略 1.采购,2.自制,3.委外,4.无
                'BOM.effective_time', //生效日期
                'BOM.failure_time', //失效日期
                'BOM.unit', // 单位
                'BOM.child_quantity', // 组成用量
                DB::raw('IF(BOM.base_quantity=0,1,BOM.base_quantity) as base_quantity'), //主件底数
                DB::raw('(BOM.child_quantity / IF(BOM.base_quantity=0,1,BOM.base_quantity) ) as dosage'), //用量
                'BOM.is_optional', //是否可选件
                'BOM.customer_code', // 客户编号
                'customers.full_name', // 客户名称
                'BOM.is_order_expand', //工单展开选项 1.不展开,2.不展开，自动开立子工单,3.展开,4.开窗询问
                'BOM.is_agent_purchase', //是否代买料
                'BOM.is_customer_material', //是否客供料
            ]);

            if ($node) {
                $nodeArray = $node->toArray();
                $nodeArray['dosage'] = $nodeArray['dosage'] * $pCon;
                $nodeArray['level'] = $currentLevel;
                return [$nodeArray];
            }

            return [];
        }

        $query = self::where([
                ['BOM.company_code', '=', $companyCode], 
                ['BOM.parent_material_code', '=', $code],
                ['BOM.effective_time', '<=', $date] // 生效日期早于等于当前日期
            ])
            ->where(function ($query) use ($date) {
                $query->whereNull('BOM.failure_time')
                    ->orWhere('BOM.failure_time', '>=', $date);
            })
            ->join('materials', function ($join) {
                $join->on('materials.company_code', '=', 'BOM.company_code')
                    ->on('materials.material_code', '=', 'BOM.child_material_code');
            })
            ->join('material_translations', function ($join) {
                $join->on('material_translations.company_code', '=', 'BOM.company_code')
                    ->on('material_translations.material_code', '=', 'BOM.child_material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->leftJoin('customers', function ($join) {
                $join->on('customers.company_code', '=', 'BOM.company_code')
                    ->on('customers.code', '=', 'BOM.customer_code');
            });

        // 添加可选件筛选逻辑
        if (!$includeOptional) {
            // 如果不包含可选件，则只选择非可选件的记录
            $query->where('BOM.is_optional', '!=', 'Y');
        } else if ($customerCode) {
            // 如果指定了客户编号，对可选件进行客户筛选
            $query->where(function ($q) use ($customerCode) {
                $q->where('BOM.is_optional', '!=', 'Y') // 非可选件
                    ->orWhere(function ($subQ) use ($customerCode) {
                        $subQ->where('BOM.is_optional', '=', 'Y') // 可选件
                            ->where('BOM.customer_code', '=', $customerCode); // 指定客户
                    });
            });
        }

        $nodes = $query->orderBy('BOM.child_material_code')
            ->get([
                'materials.material_code', //料号
                'material_translations.product_name', //品名
                'material_translations.specification', //规格
                'materials.is_package', //是否为套包 1是
                'materials.supply_type', //补给策略 1.采购,2.自制,3.委外,4.无
                DB::raw('IF(BOM.base_quantity=0,1,BOM.base_quantity) as base_quantity'), //主件底数
                DB::raw('(BOM.child_quantity / IF(BOM.base_quantity=0,1,BOM.base_quantity)) as dosage'), //用量
                'BOM.child_quantity', //组成用量
                'BOM.unit', //单位
                'materials.gross_weight', // 毛重
                'materials.net_weight', // 净重
                'materials.length', // 长
                'materials.width', // 宽
                'materials.height', // 厚度
                'materials.figure', // 图号
                'BOM.is_optional', //是否可选件
                'BOM.customer_code', // 客户编号
                'customers.full_name', // 客户名称
                'BOM.is_order_expand', //工单展开选项 1.不展开,2.不展开，自动开立子工单,3.展开,4.开窗询问
                'BOM.is_agent_purchase', //是否代买料
                'BOM.is_customer_material', //是否客供料
                'materials.paint_area', // 涂装面积
                'materials.work_hours', // 成品工时
                'BOM.effective_time', //生效日期
                'BOM.failure_time' //失效日期
            ]);

        return $nodes->flatMap(function ($node) use ($companyCode, $currentLevel, $date, $includeOptional, $customerCode, $pCon) {
            $node->dosage *= $pCon;
            $node->level = $currentLevel;

            // 如果料号首字母是5，则认为已经是叶子节点，不再往下遍历
            if (strlen($node->material_code) > 0 && substr($node->material_code, 0, 1) === '5') {
                return [$node->toArray()];
            }

            if (
                $node->is_package == 1 ||
                ($node->is_order_expand != '1' && $node->is_agent_purchase == 'N' && $node->is_customer_material == 'N')
            ) {
                return self::findLeafNodes($companyCode, $node->material_code, $currentLevel, $date, $includeOptional, $customerCode, $node->dosage);
            }

            return [$node->toArray()];
        })->all();
    }


    /**
     * 获取客户列表
     *
     * @param string $companyCode 公司编码
     * @param string $code BOM编码
     * @param Carbon $date 有效日期
     * @return \Illuminate\Http\Response
     */
    public static function getCustomers($companyCode, $code, $date)
    {
        if (!$code) {
            return collect();
        }

        // 获取所有相关料号（包括下阶料号）
        $relatedCodes = self::getRelatedCodes($companyCode, $code, $date);

        // 查询所有相关客户
        $customers = Customer::where('company_code', $companyCode)
            ->whereExists(function ($subQuery) use ($relatedCodes, $date) {
                $subQuery->select(DB::raw(1))
                    ->from('BOM')
                    ->whereColumn('BOM.company_code', 'customers.company_code')
                    ->whereColumn('BOM.customer_code', 'customers.code')
                    ->whereIn('BOM.parent_material_code', $relatedCodes)
                    ->where('BOM.is_optional', '=', 'Y')
                    ->where('BOM.effective_time', '<=', $date)
                    ->where(function ($query) use ($date) {
                        $query->whereNull('BOM.failure_time')
                            ->orWhere('BOM.failure_time', '>=', $date);
                    });
            })
            ->select([
                'customers.code as code',
                'customers.full_name as name'
            ])
            ->orderBy('customers.code')
            ->distinct()
            ->get();

        return $customers;
    }

    /**
     * 递归获取所有相关料号（包括下阶料号）
     *
     * @param string $companyCode 公司编码
     * @param string $code 父阶料号
     * @param Carbon $date 有效日期
     * @param array $processedCodes 已处理料号
     * @return array 所有相关料号
     */
    private static function getRelatedCodes($companyCode, $code, $date, &$processedCodes = [])
    {
        if (in_array($code, $processedCodes)) {
            return [];
        }

        $processedCodes[] = $code;
        $relatedCodes = [$code];

        // 获取所有子料号
        $childCodes = self::where([
                ['company_code', '=', $companyCode], 
                ['parent_material_code', '=', $code],
                ['effective_time', '<=', $date] // 生效日期早于等于当前日期
            ])
            ->where(function ($query) use ($date) {
                $query->whereNull('failure_time') // 失效日期为空
                    ->orWhere('failure_time', '>=', $date); // 或失效日期未到
            })
            ->pluck('child_material_code')
            ->toArray();

        // 递归处理子料号
        foreach ($childCodes as $childCode) {
            $relatedCodes = array_merge(
                $relatedCodes,
                self::getRelatedCodes($companyCode, $childCode, $date, $processedCodes)
            );
        }

        return array_unique($relatedCodes);
    }
}