<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaterialTranslation extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'material_translations';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'material_code',
        'locale',
        'product_name',
        'specification',
        'remark'
    ];

    /**
     * 获取翻译对应的物料
     */
    public function material()
    {
        return $this->belongsTo(Material::class, 'material_code', 'material_code')
            ->where('materials.company_code', $this->company_code);
    }
}
