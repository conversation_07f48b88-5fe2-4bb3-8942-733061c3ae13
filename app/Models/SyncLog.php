<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SyncLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'table_name',
        'sync_type',
        'status',
        'start_time',
        'end_time',
        'records_processed',
        'records_inserted',
        'records_updated',
        'records_deleted',
        'error_message',
        'sync_details'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'records_processed' => 'integer',
        'records_inserted' => 'integer',
        'records_updated' => 'integer',
        'records_deleted' => 'integer',
        'sync_details' => 'json'
    ];

    /**
     * 同步状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_RUNNING = 'running';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_PARTIAL = 'partial';

    /**
     * 同步类型常量
     */
    const TYPE_FULL = 'full';
    const TYPE_INCREMENTAL = 'incremental';

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '等待中',
            self::STATUS_RUNNING => '运行中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_PARTIAL => '部分成功',
            default => '未知'
        };
    }

    /**
     * 获取同步类型标签
     */
    public function getSyncTypeLabelAttribute(): string
    {
        return match($this->sync_type) {
            self::TYPE_FULL => '全量同步',
            self::TYPE_INCREMENTAL => '增量同步',
            default => '未知'
        };
    }

    /**
     * 计算同步耗时（秒）
     */
    public function getDurationAttribute(): ?int
    {
        if ($this->start_time && $this->end_time) {
            return (int) $this->end_time->diffInSeconds($this->start_time);
        }
        return null;
    }

    /**
     * 获取最近的同步记录
     */
    public static function getLatestSync(string $tableName): ?self
    {
        return self::where('table_name', $tableName)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * 获取成功的同步记录
     */
    public static function getSuccessfulSyncs(string $tableName, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('table_name', $tableName)
            ->where('status', self::STATUS_SUCCESS)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
} 