<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Material extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'materials';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'material_code',
        'figure',
        'unit',
        'category_code',
        'gross_weight',
        'net_weight',
        'paint_area',
        'work_hours',
        'length',
        'width',
        'height',
        'supply_type',
        'is_package',
        'process_type',
        'status',
        'remark'
    ];

    /**
     * 获取物料的翻译信息（当前用户语言）
     */
    public function translation()
    {
        $locale = Auth::check() ? (Auth::user()->locale ?? 'zh_CN') : 'zh_CN';
        return $this->hasOne(MaterialTranslation::class, 'material_code', 'material_code')
            ->where('material_translations.locale', '=', $locale);
    }

    /**
     * 获取物料的所有翻译信息
     */
    public function translations()
    {
        return $this->hasMany(MaterialTranslation::class, 'material_code', 'material_code');
    }

    /**
     * 获取产品分类
     * 
     * 本函数通过连接两个表（RTAXL_T和IMAA_T）来获取产品分类及其对应的中文标签
     * 选择 distinct 的 IMAA003（分类代码）和 RTAXL003（中文标签）字段，并按 IMAA003 排序
     * 
     * @param string $companyCode 公司代码
     * @return \Illuminate\Database\Eloquent\Collection|static[] 返回产品分类的集合，包括分类代码和中文标签
     */
    public static function getProductCategory($companyCode = 'TB')
    {
        $query = self::where('materials.company_code', '=', $companyCode)
                ->join('categories', function ($join) {
                    $join->on('categories.company_code', '=', 'materials.company_code')
                        ->on('categories.category_code', '=', 'materials.category_code')
                        ->where('categories.locale', '=', 'zh_CN');
                })
                ->select('categories.category_code as value', 'categories.category_name as label')
                ->groupBy('categories.category_code', 'categories.category_name')
                ->orderBy('categories.category_code');
    
        return $query->get();
    }

    /**
     * 获取料件列表
     * 
     * @param string $companyCode 公司代码
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @param array|null $categories 产品分类筛选
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getMaterialList($companyCode = 'TB', $page = 1, $perPage = 10, $search = null, $sortField = null, $sortDirection = 'asc', $categories = null)
    {
        // 根据前端字段名映射到数据库字段名
        $dbFieldMap = [
            'category_code' => 'materials.category_code',
            'category_name' => 'categories.category_name',
            'figure'        => 'materials.figure',
            'product_name'  => 'material_translations.product_name',
            'specification' => 'material_translations.specification',
            'material_code' => 'materials.material_code',
        ];

        $query = self::where('materials.status', '=', 'Y')
            ->where('materials.company_code', '=', $companyCode)
            ->join('categories', function ($join) {
                $join->on('categories.company_code', '=', 'materials.company_code')
                    ->on('categories.category_code', '=', 'materials.category_code')
                    ->where('categories.locale', '=', 'zh_CN');
            })
            ->join('material_translations', function ($join) {
                $join->on('material_translations.company_code', '=', 'materials.company_code')
                    ->on('material_translations.material_code', '=', 'materials.material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->select(
                'materials.category_code',
                'categories.category_name',
                'materials.figure', 
                'material_translations.product_name', 
                'material_translations.specification',
                'materials.material_code', 
            );
            
            // 添加产品分类筛选条件
            if ($categories && is_array($categories) && count($categories) > 0) {
                $query->whereIn('materials.category_code', $categories);
            }
            
            // 添加搜索条件
            if ($search) {
                $query->where(function($query) use ($search) {
                    $query->where('categories.category_name', 'like', "%{$search}%")
                        ->orWhere('materials.figure', 'like', "%{$search}%")
                        ->orWhere('material_translations.product_name', 'like', "%{$search}%")
                        ->orWhere('material_translations.specification', 'like', "%{$search}%")
                        ->orWhere('materials.material_code', 'like', "%{$search}%");
                });
            }
            
            // 添加排序
            if ($sortField && isset($dbFieldMap[$sortField])) {
                // 如果是有效的排序字段，添加排序条件
                $query->orderBy($dbFieldMap[$sortField], $sortDirection);
            } else {
                // 默认排序
                $query->orderBy('categories.category_code')
                    ->orderBy('materials.figure')
                    ->orderBy('materials.material_code');
            }
            
            // 使用分页
            return $query->paginate($perPage, ['*'], 'page', $page);
    }



}