<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * 本地MySQL同步变更日志模型
 * 
 * 这个表记录从Oracle同步到MySQL时的数据变更信息
 * 用于第三方系统获取增量变更数据
 */
class SyncChangeLog extends Model
{
    use HasFactory;

    protected $table = 'sync_change_log';

    protected $fillable = [
        'table_name',
        'change_type',
        'pk_json',
        'pk_old_json',
    ];

    protected $casts = [
        'pk_json' => 'array',
        'pk_old_json' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取指定时间之后的变更记录
     * 
     * @param string|null $sinceTime 起始时间 (YYYY-MM-DD HH:mm:ss)
     * @param string|null $tableName 表名过滤
     * @param string|null $changeType 变更类型过滤 (insert, update, delete)
     * @param int $limit 返回记录数限制
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getChangesAfter($sinceTime = null, $tableName = null, $changeType = null, $limit = 1000)
    {
        $query = static::query();

        // 时间过滤
        if ($sinceTime) {
            $query->where('created_at', '>=', $sinceTime);
        }

        // 表名过滤
        if ($tableName) {
            $query->where('table_name', $tableName);
        }

        // 变更类型过滤
        if ($changeType) {
            $query->where('change_type', $changeType);
        }

        // 修复：按ID降序排列，返回最新的记录
        // 这样第三方系统能够获取到最近的变更，而不是最早的变更
        return $query->orderBy('id', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 获取变更统计信息
     * 
     * @param string|null $sinceTime 起始时间
     * @param string|null $untilTime 结束时间
     * @return array
     */
    public static function getChangeStats($sinceTime = null, $untilTime = null)
    {
        $query = static::query();

        // 时间范围过滤
        if ($sinceTime) {
            $query->where('created_at', '>=', $sinceTime);
        }
        if ($untilTime) {
            $query->where('created_at', '<=', $untilTime);
        }

        // 总变更数
        $totalChanges = $query->count();

        // 按表和变更类型统计
        $tableStats = static::query()
            ->when($sinceTime, function($q) use ($sinceTime) {
                return $q->where('created_at', '>=', $sinceTime);
            })
            ->when($untilTime, function($q) use ($untilTime) {
                return $q->where('created_at', '<=', $untilTime);
            })
            ->selectRaw('table_name, change_type, COUNT(*) as count')
            ->groupBy('table_name', 'change_type')
            ->orderBy('table_name')
            ->orderBy('change_type')
            ->get();

        // 按小时统计
        $hourlyStats = static::query()
            ->when($sinceTime, function($q) use ($sinceTime) {
                return $q->where('created_at', '>=', $sinceTime);
            })
            ->when($untilTime, function($q) use ($untilTime) {
                return $q->where('created_at', '<=', $untilTime);
            })
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H") as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        return [
            'total_changes' => $totalChanges,
            'table_stats' => $tableStats,
            'hourly_stats' => $hourlyStats,
        ];
    }
} 