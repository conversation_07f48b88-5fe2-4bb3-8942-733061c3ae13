<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\DataSyncService;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncTableJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务最大尝试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 3600; // 1小时

    /**
     * 创建新的任务实例
     */
    public function __construct(
        public string $tableName,
        public string $syncType,
        public array $config,
        public ?string $customBatchId = null,
        public ?int $syncLogId = null
    ) {}

    /**
     * 执行任务
     */
    public function handle(DataSyncService $syncService): void
    {
        // 检查批处理是否已被取消
        if ($this->batch()?->cancelled()) {
            return;
        }

        Log::info("开始异步同步表: {$this->tableName}", [
            'sync_type' => $this->syncType,
            'sync_log_id' => $this->syncLogId,
            'custom_batch_id' => $this->customBatchId,
            'laravel_batch_id' => $this->batch()?->id
        ]);

        try {
            // 🔧 架构重构后：两种调用方式都使用相同的核心业务逻辑
            if ($this->syncLogId) {
                $result = $syncService->syncTableWithExistingLog($this->syncLogId, $this->config);
            } else {
                $result = $syncService->syncTable($this->tableName, $this->syncType, $this->config);
            }
            
            Log::info("异步同步表完成: {$this->tableName}", [
                'success' => $result['success'],
                'records_processed' => $result['records_processed'] ?? 0,
                'sync_log_id' => $this->syncLogId,
                'custom_batch_id' => $this->customBatchId,
                'laravel_batch_id' => $this->batch()?->id
            ]);

        } catch (Exception $e) {
            Log::error("异步同步表失败: {$this->tableName}", [
                'error' => $e->getMessage(),
                'sync_log_id' => $this->syncLogId,
                'custom_batch_id' => $this->customBatchId,
                'laravel_batch_id' => $this->batch()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(Exception $exception): void
    {
        Log::error("同步表任务最终失败: {$this->tableName}", [
            'error' => $exception->getMessage(),
            'sync_type' => $this->syncType,
            'sync_log_id' => $this->syncLogId,
            'custom_batch_id' => $this->customBatchId,
            'attempts' => $this->attempts()
        ]);

        // 如果有传入的syncLogId，更新状态为失败
        if ($this->syncLogId) {
            $syncLog = SyncLog::find($this->syncLogId);
            if ($syncLog) {
                $syncLog->update([
                    'status' => SyncLog::STATUS_FAILED,
                    'end_time' => now(),
                    'error_message' => "队列任务最终失败: " . $exception->getMessage(),
                    'sync_details' => json_encode([
                        'custom_batch_id' => $this->customBatchId,
                        'attempts' => $this->attempts(),
                        'job_failed' => true
                    ])
                ]);
            }
        } else {
            // 如果没有传入syncLogId，创建失败记录
            SyncLog::create([
                'table_name' => $this->tableName,
                'sync_type' => $this->syncType,
                'status' => SyncLog::STATUS_FAILED,
                'start_time' => now(),
                'end_time' => now(),
                'error_message' => "队列任务失败: " . $exception->getMessage(),
                'sync_details' => json_encode([
                    'custom_batch_id' => $this->customBatchId,
                    'attempts' => $this->attempts(),
                    'job_failed' => true
                ])
            ]);
        }
    }

    /**
     * 获取任务的唯一标识
     */
    public function uniqueId(): string
    {
        return "sync_table_{$this->tableName}_{$this->syncType}";
    }

    /**
     * 任务标签，用于监控
     */
    public function tags(): array
    {
        return [
            'sync',
            'table:' . $this->tableName,
            'type:' . $this->syncType,
            'batch:' . ($this->customBatchId ?? 'single')
        ];
    }
}
