<?php

namespace App\Jobs;

use App\Services\DataTransformService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class TransformDataJob implements ShouldQueue
{
    use Queueable;

    private array $types;
    private string $companyCode;
    private array $options;

    /**
     * 任务最大执行时间（秒）
     */
    public int $timeout = 3600; // 1小时

    /**
     * 任务失败重试次数
     */
    public int $tries = 3;

    /**
     * 创建新的任务实例
     */
    public function __construct(array $types, string $companyCode = 'TB', array $options = [])
    {
        $this->types = $types;
        $this->companyCode = $companyCode;
        $this->options = $options;
        
        // 指定队列
        $this->onQueue('transform');
    }

    /**
     * 执行转化任务
     */
    public function handle(DataTransformService $transformService): void
    {
        try {
            $startTime = microtime(true);
            
            Log::info("开始执行转化任务", [
                'types' => $this->types,
                'company_code' => $this->companyCode,
                'options' => $this->options,
                'job_id' => $this->job->id ?? 'unknown'
            ]);

            // 🔧 检查是否是批处理执行
            $isBatchExecution = $this->options['group_execution'] ?? false;
            $batchId = $this->options['batch_id'] ?? null;
            
            if ($isBatchExecution && $batchId) {
                Log::info("批处理模式执行转化任务", [
                    'batch_id' => $batchId,
                    'types' => $this->types,
                    'job_id' => $this->job->id ?? 'unknown'
                ]);
            }

            // 🔧 检查任务去重（只在非批处理模式下检查）
            if (!$isBatchExecution) {
                foreach ($this->types as $type) {
                    $cacheKey = "transform_task_running_{$type}_{$this->companyCode}";
                    
                    if (Cache::has($cacheKey)) {
                        Log::info("检测到重复的转化任务，跳过执行", [
                            'type' => $type,
                            'company_code' => $this->companyCode,
                            'cache_key' => $cacheKey
                        ]);
                        continue;
                    }
                    
                    // 🔧 设置任务状态缓存（缩短到2分钟避免过度阻塞）
                    Cache::put($cacheKey, [
                        'started_at' => now()->toISOString(),
                        'job_id' => $this->job->id ?? 'unknown',
                        'batch_id' => $batchId
                    ], 120); // 2分钟
                }
            }

            $allResults = [];
            $totalProcessed = 0;
            $totalErrors = 0;

            // 🔧 优化：如果是多个类型且支持并发，使用并发转化
            if (count($this->types) > 1 && !$isBatchExecution) {
                // 使用DataTransformService的并发转化功能
                $concurrentOptions = array_merge($this->options, ['concurrent' => true]);
                $batchResult = $transformService->batchTransform($this->types, $this->companyCode, $concurrentOptions);
                
                if ($batchResult['success']) {
                    $allResults = $batchResult['results'];
                    $totalProcessed = $batchResult['summary']['total_processed'];
                    $totalErrors = $batchResult['summary']['total_errors'];
                    
                    Log::info("并发转化任务完成", [
                        'types' => $this->types,
                        'total_processed' => $totalProcessed,
                        'total_errors' => $totalErrors,
                        'duration' => $batchResult['summary']['total_duration']
                    ]);
                } else {
                    throw new Exception("并发转化失败: " . ($batchResult['error'] ?? '未知错误'));
                }
            } else {
                // 单个类型或批处理模式：逐个执行
                foreach ($this->types as $type) {
                    try {
                        Log::info("开始转化类型: {$type}", [
                            'company_code' => $this->companyCode,
                            'batch_execution' => $isBatchExecution,
                            'batch_id' => $batchId
                        ]);

                        $result = $transformService->transform($type, $this->companyCode, $this->options);
                        $allResults[$type] = $result;

                        if ($result['success']) {
                            $totalProcessed += $result['processed'] ?? 0;
                            $totalErrors += $result['errors'] ?? 0;
                            
                            Log::info("转化类型完成: {$type}", [
                                'processed' => $result['processed'] ?? 0,
                                'errors' => $result['errors'] ?? 0,
                                'duration' => $result['duration'] ?? 0
                            ]);
                        } else {
                            $totalErrors++;
                            Log::error("转化类型失败: {$type}", [
                                'error' => $result['error'] ?? '未知错误'
                            ]);
                        }

                    } catch (Exception $e) {
                        $totalErrors++;
                        $allResults[$type] = [
                            'success' => false,
                            'error' => $e->getMessage(),
                            'type' => $type
                        ];
                        
                        Log::error("转化类型异常: {$type}", [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            }

            $endTime = microtime(true);
            $totalDuration = round($endTime - $startTime, 2);

            Log::info("转化任务完成", [
                'types' => $this->types,
                'company_code' => $this->companyCode,
                'total_processed' => $totalProcessed,
                'total_errors' => $totalErrors,
                'duration' => $totalDuration,
                'batch_execution' => $isBatchExecution,
                'batch_id' => $batchId
            ]);

            // 🔧 清理任务状态缓存（只在非批处理模式下清理）
            if (!$isBatchExecution) {
                foreach ($this->types as $type) {
                    $cacheKey = "transform_task_running_{$type}_{$this->companyCode}";
                    Cache::forget($cacheKey);
                }
            }

        } catch (Exception $e) {
            Log::error("转化任务异常", [
                'types' => $this->types,
                'company_code' => $this->companyCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 🔧 异常情况下也要清理缓存
            if (!($this->options['group_execution'] ?? false)) {
                foreach ($this->types as $type) {
                    $cacheKey = "transform_task_running_{$type}_{$this->companyCode}";
                    Cache::forget($cacheKey);
                }
            }

            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(Exception $exception): void
    {
        Log::error("批量数据转化任务最终失败", [
            'types' => $this->types,
            'company_code' => $this->companyCode,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // 🔧 新增：记录最终失败状态并清理缓存
        $this->recordTaskCompletion('finally_failed', ['error' => $exception->getMessage()]);
    }

    /**
     * 🔧 新增：记录任务完成状态并清理相关缓存
     * 
     * @param string $status 完成状态
     * @param array $result 执行结果
     */
    private function recordTaskCompletion(string $status, array $result = []): void
    {
        try {
            $triggerTable = $this->options['trigger_table'] ?? null;
            
            foreach ($this->types as $transformType) {
                // 更新任务状态缓存
                $statusCacheKey = "transform_status_{$transformType}_{$this->companyCode}";
                if ($triggerTable) {
                    $statusCacheKey .= "_{$triggerTable}";
                }
                
                $statusData = [
                    'transform_type' => $transformType,
                    'company_code' => $this->companyCode,
                    'trigger_table' => $triggerTable,
                    'status' => $status,
                    'timestamp' => now()->timestamp,
                    'result' => $result
                ];
                
                Cache::put($statusCacheKey, $statusData, 300); // 缓存5分钟
                
                // 🔧 清理去重缓存，允许新的转化任务
                $dedupCacheKey = "transform_dedup_{$transformType}_{$this->companyCode}";
                if ($triggerTable) {
                    $dedupCacheKey .= "_{$triggerTable}";
                }
                Cache::forget($dedupCacheKey);
                
                Log::debug("转化任务状态已更新", [
                    'transform_type' => $transformType,
                    'status' => $status,
                    'status_cache_key' => $statusCacheKey,
                    'dedup_cache_key' => $dedupCacheKey
                ]);
            }
            
        } catch (Exception $e) {
            Log::warning("记录转化任务完成状态失败", [
                'types' => $this->types,
                'error' => $e->getMessage()
            ]);
        }
    }
} 