import '../css/app.css';
import './bootstrap';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createApp, DefineComponent, h } from 'vue';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob<DefineComponent>('./Pages/**/*.vue'),
        ),
    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

// Inertia CSRF Token 和 Session 管理
import { router } from '@inertiajs/vue3';

// 获取CSRF管理器（从bootstrap.ts导入）
const csrfManager = (window as any).csrfManager;

// 确保每次Inertia请求都包含最新的CSRF token
router.on('before', (event) => {
    // 从CSRF管理器获取最新的token
    const token = csrfManager?.getCurrentToken();
    if (token) {
        // 确保headers存在
        if (!event.detail.visit.headers) {
            event.detail.visit.headers = {};
        }
        // 设置CSRF token header
        event.detail.visit.headers['X-CSRF-TOKEN'] = token;
    }
});

// 监听Inertia请求错误，处理Session失效
router.on('error', async (event) => {
    const errors = event.detail.errors;
    
    // 检查是否是CSRF token错误
    if (errors && (errors.message?.includes('CSRF') || errors.message?.includes('419'))) {
        console.warn('🚨 Inertia检测到CSRF错误，尝试刷新token...');
        
        try {
            // 刷新CSRF token
            if (csrfManager) {
                await csrfManager.refreshToken();
                
                // 重新发送原始请求
                const newToken = csrfManager.getCurrentToken();
                if (newToken) {
                    // 重新访问当前页面
                    router.reload({
                        headers: {
                            'X-CSRF-TOKEN': newToken
                        }
                    });
                }
            }
        } catch (error) {
            console.error('❌ Inertia CSRF token刷新失败:', error);
            // 重定向到登录页
            router.visit('/login');
        }
    }
    
    // 检查是否是认证错误
    if (errors && (errors.message?.includes('Unauthenticated') || errors.message?.includes('401'))) {
        console.warn('🚨 Inertia检测到认证错误，重定向到登录页');
        router.visit('/login');
    }
});

// 监听Inertia页面加载完成事件，同步更新CSRF token
router.on('success', (event) => {
    // 检查页面数据中是否有新的CSRF token
    const pageProps = event.detail.page.props as any;
    if (pageProps && pageProps.csrf_token && csrfManager) {
        // 更新CSRF管理器中的token
        csrfManager.updateToken(pageProps.csrf_token);
    }
});

// 定期检查Session状态（每10分钟）
let sessionCheckInterval: number | null = null;

const startSessionCheck = () => {
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }
    
    sessionCheckInterval = setInterval(async () => {
        try {
            // 发送一个轻量级的检查请求
            const response = await window.axios.get('/sanctum/csrf-cookie');
            
            if (response.status === 200) {
                console.log('✅ Session状态检查正常');
            }
        } catch (error: any) {
            if (error.response?.status === 401 || error.response?.status === 419) {
                console.warn('⚠️ Session已失效，清理定时器');
                if (sessionCheckInterval) {
                    clearInterval(sessionCheckInterval);
                    sessionCheckInterval = null;
                }
                
                // 显示友好提示并重定向
                alert('您的登录已过期，请重新登录');
                window.location.href = '/login';
            }
        }
    }, 10 * 60 * 1000); // 10分钟检查一次
};

// 在用户登录后启动Session检查
if (document.querySelector('meta[name="csrf-token"]')) {
    startSessionCheck();
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', () => {
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }
});
