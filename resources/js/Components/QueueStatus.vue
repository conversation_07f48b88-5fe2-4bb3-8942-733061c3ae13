<template>
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">队列工作进程状态</h3>
                <button 
                    @click="checkStatus"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    :disabled="checking"
                >
                    <svg v-if="checking" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ checking ? '检查中...' : '刷新状态' }}
                </button>
            </div>
            
            <!-- 状态显示 -->
            <div class="space-y-4">
                <!-- 正常状态 -->
                <div v-if="status === 'running'" class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">队列工作进程正常运行</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <p>当前运行 {{ workerCount }} 个工作进程，待处理任务 {{ pendingJobs }} 个</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 需要启动 -->
                <div v-else-if="status === 'no_workers'" class="rounded-md bg-yellow-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">需要启动队列工作进程</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p class="mb-3">没有检测到队列工作进程，数据同步功能需要手动启动队列。</p>
                                
                                <div class="bg-yellow-100 rounded-md p-3">
                                    <h4 class="font-medium text-yellow-800 mb-2">启动步骤：</h4>
                                    <ol class="list-decimal list-inside space-y-1 text-sm">
                                        <li>打开终端，进入项目目录</li>
                                        <li>运行以下命令之一：</li>
                                    </ol>
                                    
                                    <div class="mt-3 space-y-2">
                                        <div>
                                            <label class="text-xs font-medium text-yellow-800">单个工作进程：</label>
                                            <div class="mt-1 flex rounded-md shadow-sm">
                                                <input 
                                                    type="text" 
                                                    readonly 
                                                    :value="commands.single_worker"
                                                    class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border-gray-300 bg-gray-50 text-sm font-mono"
                                                />
                                                <button 
                                                    @click="copyCommand(commands.single_worker)"
                                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
                                                >
                                                    复制
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="text-xs font-medium text-yellow-800">推荐方式（简单快捷）：</label>
                                            <div class="mt-1 flex rounded-md shadow-sm">
                                                <input 
                                                    type="text" 
                                                    readonly 
                                                    :value="commands.simple"
                                                    class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border-gray-300 bg-green-50 text-sm font-mono font-semibold"
                                                />
                                                <button 
                                                    @click="copyCommand(commands.simple)"
                                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-green-100 text-green-700 text-sm hover:bg-green-200 font-medium"
                                                >
                                                    📋 复制
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="text-xs font-medium text-yellow-800">后台运行：</label>
                                            <div class="mt-1 flex rounded-md shadow-sm">
                                                <input 
                                                    type="text" 
                                                    readonly 
                                                    :value="commands.background"
                                                    class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border-gray-300 bg-gray-50 text-sm font-mono"
                                                />
                                                <button 
                                                    @click="copyCommand(commands.background)"
                                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100"
                                                >
                                                    复制
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <p class="mt-3 text-xs text-yellow-600">
                                        💡 启动成功后，刷新此页面重新检查状态
                                    </p>
                                </div>
                                
                                <!-- 重要提示 -->
                                <div class="mt-4 pt-3 border-t border-yellow-200">
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="text-sm font-medium text-yellow-800">⚠️ 重要提示</h4>
                                                <p class="mt-1 text-sm text-yellow-700">
                                                    队列工作进程无法通过浏览器启动，必须在服务器上手动执行命令。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3 text-center">
                                        <p class="text-sm font-medium text-yellow-800">📋 快速启动步骤：</p>
                                        <p class="text-xs text-yellow-700 mt-1">
                                            1. 打开终端 → 2. 进入项目目录 → 3. 执行启动命令 → 4. 点击"刷新状态"
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 错误状态 -->
                <div v-else-if="status === 'error'" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">队列状态检查失败</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>{{ errorMessage }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const status = ref('checking')
const workerCount = ref(0)
const pendingJobs = ref(0)
const checking = ref(false)
const errorMessage = ref('')

const commands = {
    simple: './start_queue.sh 2',
    single_worker: 'php artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3',
    background: 'nohup php artisan queue:work --timeout=3600 --memory=512 --tries=3 --sleep=3 > /dev/null 2>&1 &'
}

const checkStatus = async () => {
    checking.value = true
    try {
        const response = await fetch('/api/dashboard/queue/status', {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || '',
            }
        })
        
        const data = await response.json()
        
        if (data.success) {
            if (data.worker_count > 0) {
                status.value = 'running'
                workerCount.value = data.worker_count
                pendingJobs.value = data.pending_jobs || 0
            } else {
                status.value = 'no_workers'
            }
        } else {
            status.value = 'error'
            errorMessage.value = data.message || '未知错误'
        }
    } catch (error) {
        status.value = 'error'
        errorMessage.value = '网络错误: ' + error.message
    } finally {
        checking.value = false
    }
}

const copyCommand = async (command) => {
    try {
        await navigator.clipboard.writeText(command)
        // 可以添加一个toast提示
        console.log('命令已复制到剪贴板')
    } catch (err) {
        console.error('复制失败:', err)
    }
}



onMounted(() => {
    checkStatus()
})
</script> 