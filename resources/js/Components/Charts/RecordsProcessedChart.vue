<script setup lang="ts">
import { computed } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Bar } from 'vue-chartjs';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface TableStatus {
  table_name: string;
  last_sync?: {
    records_processed?: number;
    records_inserted?: number;
    records_updated?: number;
  };
}

interface Props {
  tableStatus: Record<string, TableStatus>;
}

const props = defineProps<Props>();

const chartData = computed(() => {
  const tables = Object.values(props.tableStatus);
  const labels = tables.map(table => table.table_name);
  
  const processedData = tables.map(table => 
    table.last_sync?.records_processed || 0
  );
  
  const insertedData = tables.map(table => 
    table.last_sync?.records_inserted || 0
  );
  
  const updatedData = tables.map(table => 
    table.last_sync?.records_updated || 0
  );
  
  return {
    labels,
    datasets: [
      {
        label: '总处理记录',
        data: processedData,
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: '#3B82F6',
        borderWidth: 1,
      },
      {
        label: '新增记录',
        data: insertedData,
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: '#10B981',
        borderWidth: 1,
      },
      {
        label: '更新记录',
        data: updatedData,
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
        borderColor: '#F59E0B',
        borderWidth: 1,
      }
    ]
  };
});

const chartOptions: ChartOptions<'bar'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
    title: {
      display: true,
      text: '各表记录处理情况',
      font: {
        size: 16,
        weight: 'bold',
      },
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      callbacks: {
        label: (context) => {
          const value = context.parsed.y;
          return `${context.dataset.label}: ${(value || 0).toLocaleString()}`;
        }
      }
    },
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '数据表',
      },
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '记录数量',
      },
      beginAtZero: true,
      ticks: {
        callback: (value) => {
          return ((value as number) || 0).toLocaleString();
        }
      }
    },
  },
  interaction: {
    mode: 'nearest',
    axis: 'x',
    intersect: false,
  },
};
</script>

<template>
  <div class="h-80 w-full">
    <Bar
      :data="chartData"
      :options="chartOptions"
    />
  </div>
</template> 