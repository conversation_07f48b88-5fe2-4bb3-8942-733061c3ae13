<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title,
  ChartOptions,
  Plugin,
} from 'chart.js';
import { Doughnut } from 'vue-chartjs';

// 注册Chart.js组件
ChartJS.register(ArcElement, Tooltip, Legend, Title);

interface TableStatus {
  table_name: string;
  success_rate: number;
  last_sync?: {
    status: string;
  };
}

interface Props {
  tableStatus: Record<string, TableStatus>;
}

const props = defineProps<Props>();

// 计算平均成功率
const averageSuccessRate = computed(() => {
  const tables = Object.values(props.tableStatus);
  if (tables.length === 0) return 0;
  
  const total = tables.reduce((sum, table) => sum + (table.success_rate || 0), 0);
  return (total / tables.length).toFixed(1);
});

// 创建在圆心绘制文字的插件
const centerTextPlugin: Plugin<'doughnut'> = {
  id: 'centerText',
  afterDraw: (chart) => {
    const { ctx } = chart;
    const { chartArea } = chart;
    
    if (!chartArea) return;
    
    const centerX = (chartArea.left + chartArea.right) / 2;
    const centerY = (chartArea.top + chartArea.bottom) / 2;

    ctx.save();
    
    // 绘制成功率数字
    ctx.font = 'bold 28px system-ui, -apple-system, sans-serif';
    ctx.fillStyle = '#111827';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 绘制成功率百分比
    ctx.fillText(`${averageSuccessRate.value}%`, centerX, centerY - 10);
    
    // 绘制说明文字
    ctx.font = '12px system-ui, -apple-system, sans-serif';
    ctx.fillStyle = '#6B7280';
    ctx.fillText('平均成功率', centerX, centerY + 15);
    
    ctx.restore();
  },
};

const chartData = computed(() => {
  const tables = Object.values(props.tableStatus);
  const labels = tables.map(table => table.table_name);
  const successRates = tables.map(table => table.success_rate || 0);
  
  // 生成颜色
  const colors = [
    '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B',
    '#EF4444', '#06B6D4', '#84CC16', '#F97316'
  ];
  
  return {
    labels,
    datasets: [
      {
        label: '成功率 (%)',
        data: successRates,
        backgroundColor: colors.slice(0, labels.length),
        borderColor: colors.slice(0, labels.length).map(color => color + 'DD'),
        borderWidth: 2,
        hoverBorderWidth: 3,
      }
    ]
  };
});

const chartOptions: ChartOptions<'doughnut'> = {
  responsive: true,
  maintainAspectRatio: false,
  animation: {
    animateRotate: true,
    animateScale: false,
  },
  plugins: {
    legend: {
      position: 'right',
      labels: {
        usePointStyle: true,
        padding: 15,
        generateLabels: (chart) => {
          const data = chart.data;
          if (data.labels?.length && data.datasets.length) {
            return data.labels.map((label, i) => {
              const dataset = data.datasets[0];
              const value = dataset.data[i] as number;
              const backgroundColor = Array.isArray(dataset.backgroundColor) ? dataset.backgroundColor[i] : dataset.backgroundColor;
              const borderColor = Array.isArray(dataset.borderColor) ? dataset.borderColor[i] : dataset.borderColor;
              return {
                text: `${label}: ${value.toFixed(1)}%`,
                fillStyle: backgroundColor as string,
                strokeStyle: borderColor as string,
                lineWidth: dataset.borderWidth as number,
                hidden: false,
                index: i,
              };
            });
          }
          return [];
        }
      }
    },
    title: {
      display: true,
      text: '各表同步成功率',
      font: {
        size: 16,
        weight: 'bold',
      },
    },
    tooltip: {
      callbacks: {
        label: (context) => {
          const label = context.label || '';
          const value = context.parsed;
          return `${label}: ${value.toFixed(1)}%`;
        }
      }
    },
  },
  cutout: '60%',
};
</script>

<template>
  <div class="h-80 w-full">
    <Doughnut
      :data="chartData"
      :options="chartOptions"
      :plugins="[centerTextPlugin]"
    />
  </div>
</template> 