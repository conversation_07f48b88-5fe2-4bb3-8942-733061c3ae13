<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartOptions,
} from 'chart.js';
import { Line } from 'vue-chartjs';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface SyncTrend {
  date: string;
  date_label: string;
  total_syncs: number;
  successful_syncs: number;
  failed_syncs: number;
  records_processed: number;
}

interface Props {
  trends: SyncTrend[];
}

const props = defineProps<Props>();

const chartData = computed(() => {
  const labels = props.trends.map(trend => trend.date_label);
  
  return {
    labels,
    datasets: [
      {
        label: '成功同步',
        data: props.trends.map(trend => trend.successful_syncs),
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.3,
        fill: true,
      },
      {
        label: '失败同步',
        data: props.trends.map(trend => trend.failed_syncs),
        borderColor: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.3,
        fill: true,
      },
      {
        label: '总同步次数',
        data: props.trends.map(trend => trend.total_syncs),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.3,
        fill: false,
        borderDash: [5, 5],
      }
    ]
  };
});

const chartOptions: ChartOptions<'line'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
    title: {
      display: true,
      text: '最近7天同步次数统计',
      font: {
        size: 16,
        weight: 'bold',
      },
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      callbacks: {
        afterBody: (context) => {
          const dataIndex = context[0].dataIndex;
          const records = props.trends[dataIndex]?.records_processed || 0;
          return `处理记录: ${(records || 0).toLocaleString()}`;
        }
      }
    },
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '日期',
      },
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '同步次数',
      },
      beginAtZero: true,
    },
  },
  interaction: {
    mode: 'nearest',
    axis: 'x',
    intersect: false,
  },
};
</script>

<template>
  <div class="h-80 w-full">
    <Line
      :data="chartData"
      :options="chartOptions"
    />
  </div>
</template> 