<script setup lang="ts">
import { computed } from 'vue';

interface TableStatus {
  table_name: string;
  last_sync?: {
    status: string;
    start_time?: string;
    end_time?: string;
  };
  success_rate: number;
}

interface Props {
  tableStatus: Record<string, TableStatus>;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
});

// 计算系统状态
const systemStatus = computed(() => {
  const tables = Object.values(props.tableStatus);
  
  if (tables.length === 0) {
    return {
      status: 'unknown',
      label: '未知',
      color: 'gray',
      runningCount: 0,
      successCount: 0,
      failedCount: 0,
      totalCount: 0
    };
  }
  
  const runningCount = tables.filter(table => 
    table.last_sync?.status === 'running'
  ).length;
  
  const successCount = tables.filter(table => 
    table.last_sync?.status === 'success'
  ).length;
  
  const failedCount = tables.filter(table => 
    table.last_sync?.status === 'failed'
  ).length;
  
  const totalCount = tables.length;
  
  // 确定系统状态
  let status: string;
  let label: string;
  let color: string;
  
  if (runningCount > 0) {
    status = 'running';
    label = '同步中';
    color = 'blue';
  } else if (failedCount > 0) {
    status = 'warning';
    label = '部分失败';
    color = 'yellow';
  } else if (successCount === totalCount) {
    status = 'success';
    label = '正常运行';
    color = 'green';
  } else {
    status = 'unknown';
    label = '状态未知';
    color = 'gray';
  }
  
  return {
    status,
    label,
    color,
    runningCount,
    successCount,
    failedCount,
    totalCount
  };
});

// 获取状态颜色类
const getStatusClasses = computed(() => {
  const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium';
  
  switch (systemStatus.value.color) {
    case 'green':
      return `${baseClasses} bg-green-100 text-green-800`;
    case 'blue':
      return `${baseClasses} bg-blue-100 text-blue-800`;
    case 'yellow':
      return `${baseClasses} bg-yellow-100 text-yellow-800`;
    case 'red':
      return `${baseClasses} bg-red-100 text-red-800`;
    default:
      return `${baseClasses} bg-gray-100 text-gray-800`;
  }
});

// 获取指示器颜色
const getIndicatorColor = computed(() => {
  switch (systemStatus.value.color) {
    case 'green':
      return 'bg-green-400';
    case 'blue':
      return 'bg-blue-400';
    case 'yellow':
      return 'bg-yellow-400';
    case 'red':
      return 'bg-red-400';
    default:
      return 'bg-gray-400';
  }
});

// 获取动画类
const getAnimationClass = computed(() => {
  return systemStatus.value.status === 'running' ? 'animate-pulse' : '';
});
</script>

<template>
  <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">系统状态</h3>
        <div class="flex items-center space-x-2">
          <div 
            :class="[
              'w-3 h-3 rounded-full',
              getIndicatorColor,
              getAnimationClass
            ]"
          ></div>
          <span :class="getStatusClasses">
            {{ systemStatus.label }}
          </span>
        </div>
      </div>

      <!-- 状态统计 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-2xl font-bold text-gray-900">{{ systemStatus.totalCount }}</div>
          <div class="text-sm text-gray-500">总表数</div>
        </div>
        
        <div class="text-center p-3 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ systemStatus.successCount }}</div>
          <div class="text-sm text-green-500">成功</div>
        </div>
        
        <div class="text-center p-3 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{{ systemStatus.runningCount }}</div>
          <div class="text-sm text-blue-500">运行中</div>
        </div>
        
        <div class="text-center p-3 bg-red-50 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{{ systemStatus.failedCount }}</div>
          <div class="text-sm text-red-500">失败</div>
        </div>
      </div>

      <!-- 运行中的表列表 -->
      <div v-if="systemStatus.runningCount > 0" class="mt-4">
        <h4 class="text-sm font-medium text-gray-700 mb-2">正在同步的表：</h4>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="table in Object.values(tableStatus).filter(t => t.last_sync?.status === 'running')"
            :key="table.table_name"
            class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
          >
            <svg class="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
            </svg>
            {{ table.table_name }}
          </span>
        </div>
      </div>

      <!-- 移除加载状态显示，保持界面样式稳定 -->
    </div>
  </div>
</template> 