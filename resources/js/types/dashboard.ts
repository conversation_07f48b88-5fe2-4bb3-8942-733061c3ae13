// Dashboard相关的TypeScript类型定义

export interface User {
  id: number;
  name: string;
  username?: string;
  email: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SyncStats {
  total_tables: number;
  overall_success_rate: number;
  today_syncs: number;
  today_records: number;
}

export interface SyncTrend {
  date: string;
  date_label: string;
  total_syncs: number;
  successful_syncs: number;
  failed_syncs: number;
  records_processed: number;
}

export interface SyncLog {
  id: number;
  table_name: string;
  sync_type: string;
  status: string;
  start_time?: string;
  end_time?: string;
  records_processed?: number;
  records_inserted?: number;
  records_updated?: number;
  records_deleted?: number;
  error_message?: string;
}

export interface TableStatus {
  table_name: string;
  last_sync?: {
    start_time?: string;
    end_time?: string;
    status: string;
    sync_type: string;
    records_processed?: number;
    records_inserted?: number;
    records_updated?: number;
    error_message?: string;
  };
  recent_syncs: Array<{
    start_time: string;
    status: string;
    sync_type: string;
    records_processed?: number;
    duration?: number;
  }>;
  success_rate: number;
  is_enabled: boolean;
  filter_conditions: Record<string, any>;
}

export interface DashboardData {
  stats: SyncStats;
  trends: SyncTrend[];
  tableStatus: Record<string, TableStatus>;
}

export interface SyncResponse {
  success: boolean;
  message: string;
  result?: any;
  parallel?: boolean;
}

export interface SyncRequest {
  table?: string;
  tables?: string[];
  parallel?: boolean;
}

export interface TableProgress {
  table_name: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'not_running';
  progress?: {
    percentage: number;
    processed_records: number;
    total_records: number;
    message: string;
  };
  sync_log_id?: number;
  start_time?: string;
  end_time?: string;
  sync_type?: string;
  records_processed?: number;
  message?: string;
}

export interface MultiTableProgress {
  success: boolean;
  table_progress: Record<string, TableProgress>;
  batch_id?: string;
  last_updated: string;
}

// 同步状态常量
export const SYNC_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  SUCCESS: 'success',
  FAILED: 'failed'
} as const;

export type SyncStatus = typeof SYNC_STATUS[keyof typeof SYNC_STATUS];

// 同步类型常量
export const SYNC_TYPE = {
  INCREMENTAL: 'incremental',
  FULL: 'full'
} as const;

export type SyncType = typeof SYNC_TYPE[keyof typeof SYNC_TYPE]; 