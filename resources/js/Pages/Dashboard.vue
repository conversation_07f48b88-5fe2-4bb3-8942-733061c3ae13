<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Head } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import SyncTrendChart from '@/Components/Charts/SyncTrendChart.vue';
import SuccessRateChart from '@/Components/Charts/SuccessRateChart.vue';
import TableDataCountChart from '@/Components/Charts/TableDataCountChart.vue';
import SyncDetailChart from '@/Components/Charts/SyncDetailChart.vue';
import StatusIndicator from '@/Components/Charts/StatusIndicator.vue';

import { 
  type User,
  type SyncStats, 
  type SyncTrend, 
  type TableStatus,
  type TableProgress,
  type MultiTableProgress,
} from '@/types/dashboard';

// Props
interface Props {
  user: User;
}

const props = defineProps<Props>();

// 响应式数据
const stats = ref<SyncStats>({
  total_tables: 8,
  overall_success_rate: 0,
  today_syncs: 0,
  today_records: 0,
});
const trends = ref<SyncTrend[]>([]);
const tableStatus = ref<Record<string, TableStatus>>({});
const lastUpdated = ref<string>('--');
const isDataLoading = ref(false);  // 数据加载状态

// 🚫 以下状态变量已不再使用 - Dashboard界面移除同步控制面板后的遗留代码
/*
const isSyncing = ref(false);      // 同步操作状态
const selectedTables = ref<string[]>([]);
// 多表进度状态管理
const multiTableProgress = ref<Record<string, TableProgress>>({});
const isMultiTableSyncing = ref(false);
const multiTableProgressInterval = ref<number | null>(null);
*/

// 表名列表（保留用于检测运行状态）
const availableTables = [
  'BMAA_T', 'BMBA_T', 'IMAA_T', 'IMAAL_T', 
  'IMAF_T', 'RTAXL_T', 'PMAB_T', 'PMAAL_T'
];

// 🚫 以下批处理状态管理已不再使用
/*
// 添加批处理状态管理
const batchStatus = ref<{
  batchId: string | null;
  isPolling: boolean;
  pollInterval: number | null;
  progress: {
    percentage: number;
    processed: number;
    total: number;
    message: string;
    syncType: string;
    tableName?: string;
  } | null;
}>({
  batchId: null,
  isPolling: false,
  pollInterval: null,
  progress: null,
});
*/

// 自动刷新定时器
let refreshInterval: number | null = null;

// API管理器 - 封装axios调用并提供统一的错误处理
const apiManager = {
  async get(url: string): Promise<any> {
    try {
      const response = await window.axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('API GET请求失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401 || error.response?.data?.message === 'Unauthenticated.') {
        console.warn('🚨 API认证失败，可能需要重新登录');
        // 返回一个标准格式的错误响应
        return {
          success: false,
          error: 'Unauthenticated',
          message: '认证失败，请重新登录'
        };
      }

      throw error;
    }
  },

  async post(url: string, data?: any): Promise<any> {
    try {
      const response = await window.axios.post(url, data);
      return response.data;
    } catch (error: any) {
      console.error('API POST请求失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401 || error.response?.data?.message === 'Unauthenticated.') {
        console.warn('🚨 API认证失败，可能需要重新登录');
        // 返回一个标准格式的错误响应
        return {
          success: false,
          error: 'Unauthenticated',
          message: '认证失败，请重新登录'
        };
      }

      throw error;
    }
  },

  // 兼容旧代码的方法
  async getSyncProgress(): Promise<any> {
    return this.get('/api/dashboard/sync/progress');
  }
};

/**
 * 刷新数据
 */
const refreshData = async (): Promise<void> => {
  if (isDataLoading.value) return;
  
  isDataLoading.value = true;
  
  try {
    // 尝试多个可能的API端点
    let response;
    let responseData;
    
    // 直接使用正确的API路径
    response = await window.axios.get('/api/dashboard/stats');
    responseData = response.data;

    console.log('🔍 API响应数据:', responseData);

    if (!responseData.success) {
      console.error('❌ API响应失败:', responseData);
      throw new Error(responseData.error || responseData.message || 'API请求失败');
    }

    const data = responseData.data;

    // 处理API返回的按表组织的统计数据，转换为汇总统计
    if (data.stats && typeof data.stats === 'object') {
      const tableStats = data.stats;
      const tableNames = Object.keys(tableStats);

      // 计算汇总统计
      const totalTables = tableNames.length;
      let totalSyncs = 0;
      let totalSuccessfulSyncs = 0;
      let totalRecordsProcessed = 0;

      tableNames.forEach(tableName => {
        const tableStat = tableStats[tableName];
        totalSyncs += tableStat.total_syncs || 0;

        // 计算成功的同步次数
        const successCount = Math.round((tableStat.total_syncs || 0) * (tableStat.success_rate || 0) / 100);
        totalSuccessfulSyncs += successCount;

        // 从最新同步记录中获取处理的记录数
        if (tableStat.latest_sync && tableStat.latest_sync.records_processed) {
          totalRecordsProcessed += tableStat.latest_sync.records_processed;
        }
      });

      // 计算整体成功率
      const overallSuccessRate = totalSyncs > 0 ? Math.round((totalSuccessfulSyncs / totalSyncs) * 100) : 0;

      stats.value = {
        total_tables: totalTables,
        overall_success_rate: overallSuccessRate,
        today_syncs: totalSyncs,
        today_records: totalRecordsProcessed,
      };
    } else {
      // 如果数据格式不符合预期，使用默认值
      stats.value = {
        total_tables: 8,
        overall_success_rate: 0,
        today_syncs: 0,
        today_records: 0,
      };
    }

    trends.value = data.trends || [];
    tableStatus.value = data.table_status || {};
    lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
    
  } catch (error: any) {
    console.error('刷新数据失败:', error);
    console.error('错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data
    });

    // 检查是否是认证错误
    if (error.response?.status === 401 || error.response?.data?.message === 'Unauthenticated.') {
      console.warn('🚨 API认证失败，可能需要重新登录');
      // 可以选择重定向到登录页面或显示认证错误信息
      // window.location.href = '/login';
    }

    // 显示API数据格式异常警告
    console.warn('API数据格式异常，使用模拟数据');

    // 使用模拟数据作为降级方案
    useMockData();
  } finally {
    isDataLoading.value = false;
  }
};

/**
 * 轮询批处理状态
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const pollBatchStatus = async (batchId: string, syncType: string = 'incremental'): Promise<void> => {
  if (batchStatus.value.isPolling) return;
  
  batchStatus.value.isPolling = true;
  batchStatus.value.batchId = batchId;
  
  // 初始化进度状态
  batchStatus.value.progress = {
    percentage: 0,
    processed: 0,
    total: 0,
    message: `${syncType === 'full' ? '全量' : '增量'}同步已启动...`,
    syncType: syncType
  };
  
  const pollFunction = async () => {
    try {
      const response = await apiManager.get(`/api/dashboard/batch-status?batch_id=${batchId}`);

      if (!response.success) {
        throw new Error(response.error || 'API请求失败');
      }

      const result = response.data;
      
      if (result.success) {
        const { status, progress, table_results } = result;
        
        // 更新进度状态
        if (progress && batchStatus.value.progress) {
          batchStatus.value.progress.percentage = progress.percentage || 0;
          batchStatus.value.progress.processed = progress.processed_jobs || 0;
          batchStatus.value.progress.total = progress.total_jobs || 0;
          batchStatus.value.progress.message = `正在同步 ${progress.processed_jobs}/${progress.total_jobs} 个表 (${progress.percentage}%)`;
        }
        
        // 更新表状态显示
        if (table_results) {
          Object.keys(table_results).forEach(tableName => {
            const tableResult = table_results[tableName];
            if (tableStatus.value[tableName]) {
              tableStatus.value[tableName].last_sync = {
                status: tableResult.status,
                start_time: tableResult.start_time,
                end_time: tableResult.end_time,
                sync_type: syncType, // 使用传入的同步类型
                records_processed: tableResult.records_processed,
                records_inserted: tableResult.records_inserted,
                records_updated: tableResult.records_updated,
              };
            }
          });
        }
        
        // 检查是否完成
        if (status === 'completed') {
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = '✅ 所有表同步成功完成！正在刷新数据...';
            batchStatus.value.progress.percentage = 100;
          }
          
          setTimeout(async () => {
            console.log('批处理同步完成，开始刷新所有数据...');
            
            // 全面刷新Dashboard数据
            await refreshData();
            
            // 更新时间戳
            lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
            
            showMessage('所有表同步完成，数据已全面更新', 'success');
            
            setTimeout(() => {
              stopPolling();
              isSyncing.value = false;
            }, 2000);
          }, 1000);
          
        } else if (status === 'completed_with_errors') {
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = '⚠️ 同步完成，但部分表出现错误，正在刷新数据...';
            batchStatus.value.progress.percentage = 100;
          }
          
          setTimeout(async () => {
            console.log('批处理同步完成（有错误），开始刷新数据...');
            
            // 即使有错误也要刷新数据，显示最新状态
            await refreshData();
            lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
            
            showMessage('同步完成但部分表有错误，数据已更新', 'warning');
            
            setTimeout(() => {
              stopPolling();
              isSyncing.value = false;
            }, 2000);
          }, 1000);
        } else if (status === 'cancelled') {
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = '❌ 同步已取消';
          }
          setTimeout(() => stopPolling(), 2000);
        } else if (status === 'not_found') {
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = '❌ 批处理不存在';
          }
          setTimeout(() => stopPolling(), 2000);
        }
        // 如果还在运行，继续轮询
        
      } else {
        if (batchStatus.value.progress) {
          batchStatus.value.progress.message = '❌ 获取同步状态失败: ' + result.error;
        }
        setTimeout(() => stopPolling(), 3000);
      }
      
    } catch (error) {
      console.error('轮询批处理状态失败:', error);
      if (batchStatus.value.progress) {
        batchStatus.value.progress.message = '❌ 获取同步状态失败';
      }
      setTimeout(() => stopPolling(), 3000);
    }
  };
  
  // 立即执行一次
  await pollFunction();
  
  // 如果还在轮询，设置定时器
  if (batchStatus.value.isPolling) {
    batchStatus.value.pollInterval = setInterval(pollFunction, 2000); // 每2秒轮询一次，提高进度更新频率
  }
};
*/

/**
 * 停止轮询
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const stopPolling = (): void => {
  batchStatus.value.isPolling = false;
  
  if (batchStatus.value.pollInterval) {
    clearInterval(batchStatus.value.pollInterval);
    batchStatus.value.pollInterval = null;
  }
  
  // 清理状态
  batchStatus.value.batchId = null;
  batchStatus.value.progress = null;
};
*/

/**
 * 开始轮询多表同步进度
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const startMultiTableProgressPolling = (tableNames: string[], batchId?: string): void => {
  if (multiTableProgressInterval.value) {
    clearInterval(multiTableProgressInterval.value);
  }

  isMultiTableSyncing.value = true;
  
  // 🔧 修复：只为传入的表列表检查和初始化进度状态
  tableNames.forEach(tableName => {
    const existingProgress = multiTableProgress.value[tableName];
    
    if (!existingProgress) {
      // 表没有进度状态，初始化为pending状态
      console.log(`🆕 表 ${tableName} 没有进度状态，初始化为pending状态`);
      multiTableProgress.value[tableName] = {
        table_name: tableName,
        status: 'pending',
        progress: {
          percentage: 0, // pending状态显示0%
          processed_records: 0,
          total_records: 1, // 避免除以0的情况
          message: '⏳ 等待队列处理...'
        }
      };
    } else if (existingProgress.status === 'pending') {
      // 表处于pending状态，保持不变
      console.log(`⏳ 表 ${tableName} 处于pending状态，保持不变`);
    } else if (existingProgress.status === 'running') {
      // 表已经在运行中，保持现有状态但确保轮询能继续
      console.log(`🔄 表 ${tableName} 已在运行中，保持现有进度状态`);
    } else {
      // 表有其他状态（success、failed等），保持不变
      console.log(`ℹ️ 表 ${tableName} 已有状态 (${existingProgress.status})，保持不变`);
    }
  });

  const pollFunction = async () => {
    try {
      console.log(`📊 轮询多表进度: ${tableNames.join(', ')}`);
      
      const response = await apiManager.get('/api/dashboard/multi-table-progress?' + new URLSearchParams({
        tables: tableNames.join(','),
        ...(batchId && { batch_id: batchId })
      }));

      if (!response.success) {
        throw new Error(response.error || 'API请求失败');
      }

      const result: MultiTableProgress = response.data;
      console.log('📊 多表进度响应:', result);
      
      if (result.success) {
        let hasRunningTables = false;
        
        // 更新每个表的进度
        Object.keys(result.table_progress).forEach(tableName => {
          const tableProgress = result.table_progress[tableName];
          
          // 确保进度数据完整性
          if (tableProgress) {
            if (tableProgress.status === 'running') {
              hasRunningTables = true;
              console.log(`🔄 ${tableName} 正在运行，进度: ${tableProgress.progress?.percentage || 0}%`);
            }
            
            // 处理pending到running的状态转换
            if (tableProgress.status === 'running' && multiTableProgress.value[tableName]?.status === 'pending') {
              console.log(`🚀 ${tableName} 从pending转换为running状态`);
              // 从pending转换到running，更新状态和进度
              multiTableProgress.value[tableName] = {
                ...tableProgress,
                status: 'running',
                progress: {
                  percentage: Math.max(5, tableProgress.progress?.percentage || 5),
                  processed_records: tableProgress.progress?.processed_records || 0,
                  total_records: Math.max(1, tableProgress.progress?.total_records || 1),
                  message: tableProgress.progress?.message || '🚀 同步任务已启动...'
                }
              };
              hasRunningTables = true;
            }
            // 如果状态是not_running但我们认为它应该在运行或pending，继续等待一段时间
            else if (tableProgress.status === 'not_running' && 
                     (multiTableProgress.value[tableName]?.status === 'running' || 
                      multiTableProgress.value[tableName]?.status === 'pending')) {
              // 检查是否刚开始同步，给一些时间让同步任务启动
              const currentProgress = multiTableProgress.value[tableName];
              if (currentProgress && currentProgress.progress && currentProgress.progress.percentage < 20) {
                const waitMessage = currentProgress.status === 'pending' 
                  ? '⏳ 等待队列处理...' 
                  : '⏳ 正在等待同步任务启动...';
                
                console.log(`⏳ ${tableName} 暂时未找到运行任务，但可能正在启动中，继续等待...`);
                // 保持当前进度，但增加一点百分比表示我们在等待
                multiTableProgress.value[tableName] = {
                  ...currentProgress,
                  progress: {
                    ...currentProgress.progress,
                    percentage: currentProgress.status === 'pending' ? 0 : Math.min(15, currentProgress.progress.percentage + 2),
                    message: waitMessage
                  }
                };
                hasRunningTables = true; // 继续轮询
              } else {
                // 进度已经比较高了，可能同步已完成
                console.log(`✅ ${tableName} 同步可能已完成，检查完成状态`);
                checkTableCompletion(tableName);
              }
            } else {
              // 更新进度数据
              multiTableProgress.value[tableName] = {
                ...tableProgress,
                progress: tableProgress.progress ? {
                  percentage: Math.max(0, Math.min(100, tableProgress.progress.percentage || 0)),
                  processed_records: tableProgress.progress.processed_records || 0,
                  total_records: Math.max(1, tableProgress.progress.total_records || 1),
                  message: tableProgress.progress.message || '正在同步中...'
                } : multiTableProgress.value[tableName]?.progress || {
                  percentage: 10,
                  processed_records: 0,
                  total_records: 1,
                  message: '正在等待服务器响应...'
                }
              };
            }
          }
        });

        // 检查是否所有表都完成了
        const allCompleted = Object.values(result.table_progress).every(
          progress => progress.status === 'success' || progress.status === 'failed'
        );

        if (allCompleted && !hasRunningTables) {
          console.log('🎉 所有表同步完成，停止轮询');
          
          // 所有表都完成了，停止轮询
          stopMultiTableProgressPolling();
          
          // 显示完成消息
          const successCount = Object.values(result.table_progress).filter(
            progress => progress.status === 'success'
          ).length;
          const totalCount = Object.keys(result.table_progress).length;
          
          if (successCount === totalCount) {
            showMessage(`所有 ${totalCount} 个表同步成功完成！`, 'success');
          } else {
            showMessage(`${successCount}/${totalCount} 个表同步成功，${totalCount - successCount} 个表失败`, 'warning');
          }
          
          // 刷新整体数据
          setTimeout(async () => {
            await refreshData();
            lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
          }, 2000);
        } else if (hasRunningTables) {
          console.log(`⚡ 还有表在同步中，继续轮询...`);
        }
      } else {
        console.error('获取多表进度失败:', result);
        stopMultiTableProgressPolling();
      }
      
    } catch (error) {
      console.error('轮询多表进度失败:', error);
      // 不要立即停止，可能只是网络临时问题
    }
  };

  // 立即执行一次
  pollFunction();
  
  // 每3秒轮询一次（保持适当频率以及时更新进度）
  multiTableProgressInterval.value = setInterval(pollFunction, 2000); // 提高多表进度监控频率
};
*/

/**
 * 停止多表进度轮询
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const stopMultiTableProgressPolling = (): void => {
  isMultiTableSyncing.value = false;
  
  if (multiTableProgressInterval.value) {
    clearInterval(multiTableProgressInterval.value);
    multiTableProgressInterval.value = null;
  }
  
  // 清理进度状态（延迟一段时间让用户看到最终结果）
  setTimeout(() => {
    // 只清理已完成的表的进度，保留正在运行的表
    Object.keys(multiTableProgress.value).forEach(tableName => {
      const progress = multiTableProgress.value[tableName];
      if (progress && (progress.status === 'success' || progress.status === 'failed')) {
        delete multiTableProgress.value[tableName];
      }
    });
  }, 8000); // 8秒后清理完成状态
};
*/

/**
 * 检查表是否正在同步
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const isTableSyncing = (tableName: string): boolean => {
  // 检查多表进度中的状态
  const multiProgress = multiTableProgress.value[tableName];
  if (multiProgress && multiProgress.status === 'running') {
    return true;
  }
  
  // 检查tableStatus中的状态
  const tableState = tableStatus.value[tableName];
  if (tableState?.last_sync?.status === 'running') {
    return true;
  }
  
  return false;
};
*/

/**
 * 检查表是否已完成同步
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const checkTableCompletion = async (tableName: string): Promise<void> => {
  try {
    const response = await apiManager.getSyncProgress();

    if (!response.success) {
      throw new Error(response.error || 'API请求失败');
    }

    const result = response.data;
    
    if (result.success && !result.running) {
      // 同步已完成，更新状态为成功
      multiTableProgress.value[tableName] = {
        table_name: tableName,
        status: 'success',
        progress: {
          percentage: 100,
          processed_records: 0, // 会从refreshData中获取准确数据
          total_records: 1,
          message: '✅ 同步已完成'
        }
      };
      
      // 刷新数据
      await refreshData();
      showMessage(`${tableName} 表同步完成`, 'success');
      
      // 2秒后清理进度状态
      setTimeout(() => {
        if (multiTableProgress.value[tableName]?.status === 'success') {
          delete multiTableProgress.value[tableName];
        }
      }, 2000);
    }
    
  } catch (error) {
    console.error(`检查 ${tableName} 完成状态失败:`, error);
  }
};
*/

/**
 * 检测同步完成状态并刷新数据
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const checkSyncCompletion = async (tableName: string): Promise<void> => {
  try {
    const response = await apiManager.getSyncProgress();

    if (!response.success) {
      throw new Error(response.error || 'API请求失败');
    }

    const result = response.data;
    
    if (result.success && !result.running) {
      // 同步已完成，停止轮询并刷新数据
      if (batchStatus.value.progress) {
        batchStatus.value.progress.message = `✅ ${tableName} 同步已完成！正在刷新数据...`;
        batchStatus.value.progress.percentage = 100;
      }
      
      setTimeout(async () => {
        stopPolling();
        
        // 全面刷新Dashboard数据（统计信息、图表、表状态）
        console.log(`${tableName} 同步完成，开始刷新数据...`);
        await refreshData();
        
        // 强制刷新图表组件（如果有的话）
        lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
        
        showMessage(`${tableName} 表同步完成，所有数据已更新`, 'success');
        
        // 清理进度状态，稍等一会让用户看到成功消息
        setTimeout(() => {
          batchStatus.value.progress = null;
          isSyncing.value = false; // 确保同步状态重置
        }, 3000);
      }, 1000);
      
    } else if (result.success && result.running) {
      // 还在运行，继续轮询
      if (batchStatus.value.progress) {
        batchStatus.value.progress.message = `🔄 ${tableName} 正在最后阶段处理...`;
      }
      
      // 3秒后再次检查
      setTimeout(() => {
        checkSyncCompletion(tableName);
      }, 3000);
      
    } else {
      // 检查失败，停止轮询
      console.warn('检查同步完成状态失败:', result.message);
      setTimeout(() => {
        stopPolling();
        refreshData();
      }, 1000);
    }
    
  } catch (error) {
    console.error('检查同步完成状态失败:', error);
    setTimeout(() => {
      stopPolling();
      refreshData();
    }, 1000);
  }
};
*/

/**
 * 轮询大表同步进度
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const pollTableProgress = async (tableName: string): Promise<void> => {
  if (batchStatus.value.isPolling) return;
  
  batchStatus.value.isPolling = true;
  
  // 初始化进度状态
  batchStatus.value.progress = {
    percentage: 0,
    processed: 0,
    total: 0,
    message: `正在监控 ${tableName} 表同步进度...`,
    syncType: 'table_progress',
    tableName: tableName
  };
  
  const pollFunction = async () => {
    try {
      const response = await apiManager.get(`/api/dashboard/sync/progress?table_name=${tableName}`);

      if (!response.success) {
        throw new Error(response.error || 'API请求失败');
      }

      const result = response.data;
      
      if (result.success) {
        if (!result.running) {
          // 没有运行中的同步任务
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = `${tableName} 没有正在运行的同步任务`;
          }
          setTimeout(() => stopPolling(), 2000);
          return;
        }
        
        const { progress } = result;
        
        // 更新进度状态
        if (progress && batchStatus.value.progress) {
          batchStatus.value.progress.percentage = progress.percentage || 0;
          batchStatus.value.progress.processed = progress.processed_records || 0;
          batchStatus.value.progress.total = progress.total_records || 0;
          
          const duration = Math.floor(progress.duration / 60); // 转换为分钟
          let message = `${tableName}: ${progress.processed_records.toLocaleString()}/${progress.total_records.toLocaleString()} 条记录 (${progress.percentage}%)`;
          
          if (duration > 0) {
            message += ` - 已运行 ${duration} 分钟`;
          }
          
          if (progress.estimated_remaining) {
            const remainingMinutes = Math.floor(progress.estimated_remaining / 60);
            message += ` - 预计还需 ${remainingMinutes} 分钟`;
          }
          
          batchStatus.value.progress.message = message;
        }
        
        // 如果进度达到100%，说明接近完成
        if (progress.percentage >= 100) {
          if (batchStatus.value.progress) {
            batchStatus.value.progress.message = `✅ ${tableName} 同步即将完成...`;
          }
          // 开始检测同步完成状态
          setTimeout(() => {
            checkSyncCompletion(tableName);
          }, 2000);
        }
        
      } else {
        if (batchStatus.value.progress) {
          batchStatus.value.progress.message = '❌ 获取同步进度失败: ' + result.message;
        }
        setTimeout(() => stopPolling(), 3000);
      }
      
    } catch (error) {
      console.error('轮询表同步进度失败:', error);
      if (batchStatus.value.progress) {
        batchStatus.value.progress.message = '❌ 获取同步进度失败';
      }
      setTimeout(() => stopPolling(), 3000);
    }
  };
  
  // 立即执行一次
  await pollFunction();
  
  // 如果还在轮询，设置定时器
  if (batchStatus.value.isPolling) {
          batchStatus.value.pollInterval = setInterval(pollFunction, 2000); // 每2秒轮询一次，提高单表进度更新频率
  }
};
*/

/**
 * 检查是否有正在运行的表同步
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const checkRunningTables = async (): Promise<void> => {
  // 检查每个表是否有正在运行的同步任务
  for (const tableName of availableTables) {
    try {
      const response = await apiManager.get(`/api/dashboard/sync/progress?table_name=${tableName}`);

      if (response.success) {
        const result = response.data;
        if (result.success && result.running) {
          // 找到正在运行的表，启动进度监控
          console.log(`发现 ${tableName} 表正在同步，启动进度监控`);
          pollTableProgress(tableName);
          break; // 只监控一个表，避免同时监控多个
        }
      }
    } catch (error) {
      console.error(`检查 ${tableName} 同步状态失败:`, error);
    }
  }
};
*/

/**
 * 检测并恢复正在运行的同步任务进度监控
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const detectAndResumeRunningSyncs = async (): Promise<void> => {
  try {
    // 🔧 修复：如果已经有多表同步在进行，不要执行检测恢复
    if (isMultiTableSyncing.value || isSyncing.value) {
      console.log('⏭️ 跳过恢复检测：已有同步任务在进行');
      return;
    }

    // 🔧 修复：检查是否已有表在multiTableProgress中，避免覆盖现有状态
    const hasExistingProgress = Object.keys(multiTableProgress.value).length > 0;
    if (hasExistingProgress) {
      console.log('⏭️ 跳过恢复检测：已有进度状态存在');
      return;
    }

    // 使用多表进度API检查所有表的状态
    const allTableNames = availableTables.join(',');
    const response = await apiManager.get(`/api/dashboard/multi-table-progress?tables=${allTableNames}`);
    
    if (response.success && response.data.success) {
      const runningTables = Object.keys(response.data.table_progress).filter(tableName => {
        const progress = response.data.table_progress[tableName];
        return progress.status === 'running';
      });

      if (runningTables.length > 0) {
        console.log('🔄 检测到正在运行的表:', runningTables);
        
        // 为正在运行的表初始化进度状态
        runningTables.forEach(tableName => {
          const existingProgress = response.data.table_progress[tableName];
          multiTableProgress.value[tableName] = {
            table_name: tableName,
            status: 'running',
            progress: {
              percentage: existingProgress.progress?.percentage || 15,
              message: existingProgress.progress?.message || '🔄 正在恢复同步进度监控...',
              processed_records: existingProgress.progress?.processed_records || 0,
              total_records: existingProgress.progress?.total_records || 0
            }
          };
        });

        // 启动进度监控
        startMultiTableProgressPolling(runningTables);
        
        showMessage(`已恢复 ${runningTables.length} 个表的同步进度监控`, 'success');
      } else {
        console.log('🔍 没有检测到正在运行的表');
      }
    }
  } catch (error) {
    console.error('检测运行中同步任务失败:', error);
    // 静默失败，不影响正常使用
  }
};
*/

// 消息通知状态
const messageState = ref<{
  show: boolean;
  message: string;
  type: 'success' | 'error' | 'warning';
}>({
  show: false,
  message: '',
  type: 'success'
});

/**
 * 显示消息
 */
const showMessage = (message: string, type: 'success' | 'error' | 'warning' = 'success'): void => {
  console.log(`[${type.toUpperCase()}] ${message}`);
  // 这里可以集成实际的消息提示组件，如 toast、notification 等
  // 目前使用浏览器原生alert作为演示
  if (type === 'error') {
    alert(`❌ ${message}`);
  } else if (type === 'warning') {
    alert(`⚠️ ${message}`);
  } else {
    // success类型的消息不用alert打断用户，只在控制台记录
    console.info(`✅ ${message}`);
  }
};

/**
 * 显示进度提示（专门用于同步状态）
 */
const showProgressMessage = (message: string, type: 'started' | 'progress' | 'completed' | 'error'): void => {
  const icons = {
    started: '🚀',
    progress: '⏳', 
    completed: '✅',
    error: '❌'
  };
  
  const fullMessage = `${icons[type]} ${message}`;
  console.log(`[SYNC-${type.toUpperCase()}] ${fullMessage}`);
  
  // 对于错误类型，仍然使用alert
  if (type === 'error') {
    alert(fullMessage);
  }
  // 其他类型只在控制台显示，不打断用户操作
};

/**
 * 使用模拟数据（降级方案）
 */
const useMockData = () => {
  // 生成模拟数据
  stats.value = {
    total_tables: 8,
    overall_success_rate: 87.5,
    today_syncs: 12,
    today_records: 145620,
  };

  // 生成模拟趋势数据
  trends.value = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (6 - i));
    return {
      date: date.toISOString().split('T')[0],
      date_label: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
      total_syncs: Math.floor(Math.random() * 20) + 5,
      successful_syncs: Math.floor(Math.random() * 15) + 3,
      failed_syncs: Math.floor(Math.random() * 3),
      records_processed: Math.floor(Math.random() * 50000) + 10000,
    };
  });

  // 生成模拟表状态
  availableTables.forEach(tableName => {
    const isRunning = Math.random() < 0.2; // 20%概率正在运行
    const hasSuccess = Math.random() < 0.9; // 90%概率有成功记录
    
    tableStatus.value[tableName] = {
      table_name: tableName,
      last_sync: hasSuccess ? {
        start_time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
        end_time: isRunning ? undefined : new Date(Date.now() - Math.random() * 23 * 60 * 60 * 1000).toISOString(),
        status: isRunning ? 'running' : (Math.random() < 0.9 ? 'success' : 'failed'),
        sync_type: Math.random() < 0.7 ? 'incremental' : 'full',
        records_processed: Math.floor(Math.random() * 100000) + 1000,
        records_inserted: Math.floor(Math.random() * 1000),
        records_updated: Math.floor(Math.random() * 5000),
      } : undefined,
      recent_syncs: [],
      success_rate: Math.floor(Math.random() * 30) + 70,
      is_enabled: true,
      filter_conditions: {},
    };
  });

  lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
  showMessage('使用模拟数据展示（API连接失败）', 'warning');
};

/**
 * 🚫 以下函数已不再使用 - Dashboard界面移除同步控制面板后的遗留代码
 * 注释原因：Dashboard现在专注于数据展示和监控，不再提供手动同步功能
 * 保留代码以备将来可能需要恢复手动同步功能
 */

/**
 * 触发增量同步（异步模式）
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const triggerIncrementalSync = async () => {
  if (selectedTables.value.length === 0) {
    showMessage('请选择要同步的表', 'warning');
    return;
  }

  if (isSyncing.value) {
    showMessage('同步操作正在进行中，请稍候', 'warning');
    return;
  }

  isSyncing.value = true;

  try {
    const response = await apiManager.post('/api/dashboard/sync/incremental', {
      tables: selectedTables.value,
      parallel: true
    });

    if (!response.success) {
      if (response.error === 'Unauthenticated') {
        showMessage('请先登录后再操作', 'error');
        return;
      }
      throw new Error(response.error || 'API请求失败');
    }

    const result = response.data;
    
    // 检查是否自动启动了队列
    if (result.queue_status?.auto_started) {
      showProgressMessage('队列工作进程已自动启动，同步即将开始', 'started');
    } else if (result.queue_status?.additional_worker_started) {
      showProgressMessage('已启动额外队列工作进程以提升处理速度', 'started');
    }
    
    if (result.success) {
      const firstTableResult = Object.values(result.result)[0] as any;
      
      if (firstTableResult?.laravel_batch_id && selectedTables.value.length > 1) {
        // 并发同步模式：开始轮询状态
        // 不显示启动消息，通过进度条显示状态
        console.log('⚡ 并发同步已启动，正在监控进度...');
        pollBatchStatus(firstTableResult.laravel_batch_id, 'incremental');
      } else {
        // 单表或顺序同步模式
        // 不显示启动消息，通过进度条显示状态
        console.log(result.message || '同步任务已启动');
        isSyncing.value = false;
        setTimeout(refreshData, 2000);
      }
    } else {
      showMessage(result.message || '同步启动失败', 'error');
      isSyncing.value = false;
    }
    
  } catch (error) {
    console.error('同步失败:', error);
    showMessage('增量同步请求失败', 'error');
    isSyncing.value = false;
  }
};
*/

/**
 * 触发全量同步（异步模式）
 * @deprecated Dashboard界面已移除同步控制，此函数不再使用
 */
/*
const triggerFullSync = async () => {
  if (selectedTables.value.length === 0) {
    showMessage('请选择要同步的表', 'warning');
    return;
  }

  if (isSyncing.value) {
    showMessage('同步操作正在进行中，请稍候', 'warning');
    return;
  }

  isSyncing.value = true;

  try {
    const response = await apiManager.post('/api/dashboard/sync/full', {
      tables: selectedTables.value,
      parallel: true
    });

    if (!response.success) {
      if (response.error === 'Unauthenticated') {
        showMessage('请先登录后再操作', 'error');
        return;
      }
      throw new Error(response.error || 'API请求失败');
    }

    const result = response.data;
    
    // 检查是否自动启动了队列
    if (result.queue_status?.auto_started) {
      showProgressMessage('队列工作进程已自动启动，同步即将开始', 'started');
    } else if (result.queue_status?.additional_worker_started) {
      showProgressMessage('已启动额外队列工作进程以提升处理速度', 'started');
    }
    
    if (result.success) {
      const firstTableResult = Object.values(result.result)[0] as any;
      
      if (firstTableResult?.laravel_batch_id && selectedTables.value.length > 1) {
        // 并发同步模式：开始轮询状态
        // 不显示启动消息，通过进度条显示状态
        console.log('⚡ 并发全量同步已启动，正在监控进度...');
        pollBatchStatus(firstTableResult.laravel_batch_id, 'full');
      } else {
        // 单表或顺序同步模式
        // 不显示启动消息，通过进度条显示状态
        console.log(result.message || '全量同步任务已启动');
        isSyncing.value = false;
        setTimeout(refreshData, 2000);
      }
    } else {
      showMessage(result.message || '全量同步启动失败', 'error');
      isSyncing.value = false;
    }
    
  } catch (error) {
    console.error('全量同步失败:', error);
    showMessage('全量同步请求失败', 'error');
    isSyncing.value = false;
  }
};
*/

// 生命周期钩子
onMounted(() => {
  lastUpdated.value = new Date().toLocaleTimeString('zh-CN');
  refreshData();
  
  // 🚫 以下同步监控功能已不再使用 - Dashboard专注于数据展示
  /*
  // 检查是否有大表正在同步，如果有则启动进度监控
  setTimeout(() => {
    checkRunningTables();
  }, 2000);
  */
  
  // 启动自动刷新
  refreshInterval = setInterval(refreshData, 30000);
});

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
  
  // 🚫 以下轮询清理功能已不再使用
  /*
  // 清理批处理状态轮询
  stopPolling();
  */
});

/**
 * 🚫 以下生命周期钩子已不再使用 - Dashboard界面移除同步控制后的遗留代码
 */
/*
onMounted(async () => {
  // 首先获取数据
  await refreshData();
  
  // 检测并恢复正在运行的同步任务
  setTimeout(() => {
    detectAndResumeRunningSyncs();
  }, 1000); // 给refreshData一点时间完成
  
  // 设置自动刷新（每30秒）
  refreshInterval = setInterval(() => {
    // 如果没有多表同步在进行，则刷新数据
    // 多表同步时有自己的轮询机制，避免冲突
    if (!isMultiTableSyncing.value && !isSyncing.value) {
      refreshData();
    }
  }, 30000);
});
*/

/**
 * 页面卸载时清理定时器
 * @deprecated 原有的多轮询清理功能已不再使用
 */
/*
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
  
  // 停止所有轮询
  stopPolling();
  stopMultiTableProgressPolling();
});
*/
</script>

<template>
  <Head title="数据同步监控台" />

  <AuthenticatedLayout>
    <!-- 消息通知组件 -->
    <div 
      v-if="messageState.show" 
      class="fixed top-4 right-4 z-50 max-w-sm w-full"
    >
      <div 
        class="rounded-lg shadow-lg p-4 transition-all duration-300 transform"
        :class="{
          'bg-green-50 border border-green-200': messageState.type === 'success',
          'bg-red-50 border border-red-200': messageState.type === 'error',
          'bg-yellow-50 border border-yellow-200': messageState.type === 'warning'
        }"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg v-if="messageState.type === 'success'" class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <svg v-else-if="messageState.type === 'error'" class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
            <svg v-else-if="messageState.type === 'warning'" class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-3">
            <p 
              class="text-sm font-medium"
              :class="{
                'text-green-800': messageState.type === 'success',
                'text-red-800': messageState.type === 'error',
                'text-yellow-800': messageState.type === 'warning'
              }"
            >
              {{ messageState.message }}
            </p>
          </div>
          <div class="ml-auto pl-3">
            <button 
              @click="messageState.show = false"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              :class="{
                'text-green-500 hover:bg-green-100 focus:ring-green-600': messageState.type === 'success',
                'text-red-500 hover:bg-red-100 focus:ring-red-600': messageState.type === 'error',
                'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600': messageState.type === 'warning'
              }"
            >
              <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        
        <!-- 系统状态指示器 -->
        <StatusIndicator 
          :table-status="tableStatus" 
          :is-loading="isDataLoading"
        />



        <!-- 图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 同步趋势图 -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <SyncTrendChart :trends="trends" />
            </div>
          </div>

          <!-- 成功率图表 -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <SuccessRateChart :table-status="tableStatus" />
            </div>
          </div>
        </div>

        <!-- 各表数据情况 -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <TableDataCountChart />
          </div>
        </div>

        <!-- 同步详情曲线图 -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <SyncDetailChart />
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<style>
/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}
</style>
