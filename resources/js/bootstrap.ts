import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
window.axios.defaults.withCredentials = true;

// CSRF Token 管理类
class CSRFTokenManager {
    private static instance: CSRFTokenManager;
    private refreshPromise: Promise<void> | null = null;

    static getInstance(): CSRFTokenManager {
        if (!CSRFTokenManager.instance) {
            CSRFTokenManager.instance = new CSRFTokenManager();
        }
        return CSRFTokenManager.instance;
    }

    // 获取当前CSRF token
    getCurrentToken(): string | null {
        const token = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
        return token ? token.content : null;
    }

    // 更新CSRF token到meta标签和axios headers
    updateToken(newToken: string): void {
        // 更新meta标签
        const metaToken = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
        if (metaToken) {
            metaToken.content = newToken;
        }

        // 更新axios默认headers
        window.axios.defaults.headers.common['X-CSRF-TOKEN'] = newToken;
        window.axios.defaults.headers.common['X-XSRF-TOKEN'] = newToken;
    }

    // 从服务器刷新CSRF token
    async refreshToken(): Promise<void> {
        // 避免重复请求
        if (this.refreshPromise) {
            return this.refreshPromise;
        }

        this.refreshPromise = this._doRefreshToken();
        
        try {
            await this.refreshPromise;
        } finally {
            this.refreshPromise = null;
        }
    }

    private async _doRefreshToken(): Promise<void> {
        try {
            console.log('🔄 刷新CSRF Token...');
            await window.axios.get('/sanctum/csrf-cookie');
            
            // 等待一下让cookie生效
            await new Promise(resolve => setTimeout(resolve, 100));
            
            console.log('✅ CSRF Token刷新成功');
        } catch (error) {
            console.error('❌ CSRF Token刷新失败:', error);
            throw error;
        }
    }
}

// 初始化CSRF token
const csrfManager = CSRFTokenManager.getInstance();
const initialToken = csrfManager.getCurrentToken();

if (initialToken) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = initialToken;
    window.axios.defaults.headers.common['X-XSRF-TOKEN'] = initialToken;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// 添加请求拦截器 - 确保每次请求都有最新的token
window.axios.interceptors.request.use(
    (config) => {
        const currentToken = csrfManager.getCurrentToken();
        if (currentToken) {
            config.headers['X-CSRF-TOKEN'] = currentToken;
            config.headers['X-XSRF-TOKEN'] = currentToken;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 添加响应拦截器 - 处理419错误
window.axios.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        // 处理419 CSRF token mismatch错误
        if (error.response?.status === 419 && !originalRequest._retry) {
            originalRequest._retry = true;
            
            console.warn('🚨 检测到419错误，尝试刷新CSRF Token...');
            
            try {
                // 刷新CSRF token
                await csrfManager.refreshToken();
                
                // 重新发送原始请求
                const newToken = csrfManager.getCurrentToken();
                if (newToken) {
                    originalRequest.headers['X-CSRF-TOKEN'] = newToken;
                    originalRequest.headers['X-XSRF-TOKEN'] = newToken;
                }
                
                return window.axios(originalRequest);
            } catch (refreshError: any) {
                console.error('❌ CSRF Token刷新失败，可能需要重新登录');
                
                // 如果是认证错误，重定向到登录页
                if (refreshError.response?.status === 401) {
                    window.location.href = '/login';
                }
                
                return Promise.reject(error);
            }
        }

        // 处理401未认证错误
        if (error.response?.status === 401) {
            console.warn('🚨 检测到401错误，重定向到登录页');
            window.location.href = '/login';
        }

        return Promise.reject(error);
    }
);

// 导出CSRF管理器供其他模块使用
(window as any).csrfManager = csrfManager;
