-- 优化MySQL触发器：减少不必要的sync_change_log记录
-- 通过比较OLD和NEW值，只在真正发生变化时记录
-- 支持所有业务表：materials、material_translations、BOM、customers、categories
-- 注意：BOM表在数据库中的实际表名为大写'BOM'，sync_change_log中记录的表名现在也统一为大写'BOM'

-- 1. 优化materials表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_materials_after_update;

DELIMITER $$
CREATE TRIGGER trg_materials_after_update
    AFTER UPDATE ON materials
    FOR EACH ROW
BEGIN
    -- 🔧 只有在业务字段真正发生变化时才记录到sync_change_log
    -- 排除只有updated_at字段变化的情况
    IF NOT (
        (OLD.material_code <=> NEW.material_code) AND
        (OLD.company_code <=> NEW.company_code) AND
        (OLD.figure <=> NEW.figure) AND
        (OLD.unit <=> NEW.unit) AND
        (OLD.category_code <=> NEW.category_code) AND
        (OLD.gross_weight <=> NEW.gross_weight) AND
        (OLD.net_weight <=> NEW.net_weight) AND
        (OLD.paint_area <=> NEW.paint_area) AND
        (OLD.work_hours <=> NEW.work_hours) AND
        (OLD.length <=> NEW.length) AND
        (OLD.width <=> NEW.width) AND
        (OLD.height <=> NEW.height) AND
        (OLD.supply_type <=> NEW.supply_type) AND
        (OLD.is_package <=> NEW.is_package) AND
        (OLD.status <=> NEW.status)
        -- 注意：不比较created_at和updated_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            created_at,
            updated_at
        ) VALUES (
            'materials',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'material_code', NEW.material_code
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 2. 优化material_translations表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_update;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_update
    AFTER UPDATE ON material_translations
    FOR EACH ROW
BEGIN
    -- 🔧 只有在业务字段真正发生变化时才记录到sync_change_log
    IF NOT (
        (OLD.material_code <=> NEW.material_code) AND
        (OLD.company_code <=> NEW.company_code) AND
        (OLD.locale <=> NEW.locale) AND
        (OLD.product_name <=> NEW.product_name) AND
        (OLD.specification <=> NEW.specification)
        -- 注意：不比较created_at和updated_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            created_at,
            updated_at
        ) VALUES (
            'material_translations',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'material_code', NEW.material_code,
                'locale', NEW.locale
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 3. 优化BOM表的UPDATE触发器（注意：实际表名为BOM，sync_change_log中记录也为BOM）
DROP TRIGGER IF EXISTS trg_BOM_after_update;

DELIMITER $$
CREATE TRIGGER trg_BOM_after_update
    AFTER UPDATE ON BOM
    FOR EACH ROW
BEGIN
    -- 🔧 只有在业务字段真正发生变化时才记录到sync_change_log
    IF NOT (
        (OLD.company_code <=> NEW.company_code) AND
        (OLD.parent_material_code <=> NEW.parent_material_code) AND
        (OLD.child_material_code <=> NEW.child_material_code) AND
        (OLD.version <=> NEW.version) AND
        (OLD.base_quantity <=> NEW.base_quantity) AND
        (OLD.child_quantity <=> NEW.child_quantity) AND
        (OLD.unit <=> NEW.unit) AND
        (OLD.is_order_expand <=> NEW.is_order_expand) AND
        (OLD.is_optional <=> NEW.is_optional) AND
        (OLD.is_customer_material <=> NEW.is_customer_material) AND
        (OLD.is_agent_purchase <=> NEW.is_agent_purchase) AND
        (OLD.status <=> NEW.status) AND
        (OLD.customer_code <=> NEW.customer_code) AND
        (OLD.effective_time <=> NEW.effective_time) AND
        (OLD.failure_time <=> NEW.failure_time)
        -- 注意：不比较created_at和updated_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            created_at,
            updated_at
        ) VALUES (
            'BOM',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'parent_material_code', NEW.parent_material_code,
                'child_material_code', NEW.child_material_code
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 4. 新增：customers表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_customers_after_update;

DELIMITER $$
CREATE TRIGGER trg_customers_after_update
    AFTER UPDATE ON customers
    FOR EACH ROW
BEGIN
    -- 🔧 只有在业务字段真正发生变化时才记录到sync_change_log
    IF NOT (
        (OLD.company_code <=> NEW.company_code) AND
        (OLD.code <=> NEW.code) AND
        (OLD.short_name <=> NEW.short_name) AND
        (OLD.full_name <=> NEW.full_name) AND
        (OLD.currency_id <=> NEW.currency_id) AND
        (OLD.tax_type_id <=> NEW.tax_type_id) AND
        (OLD.trade_term_id <=> NEW.trade_term_id) AND
        (OLD.pricing_method_id <=> NEW.pricing_method_id) AND
        (OLD.invoice_type_id <=> NEW.invoice_type_id) AND
        (OLD.payment_term_id <=> NEW.payment_term_id) AND
        (OLD.account_receivable_type_id <=> NEW.account_receivable_type_id) AND
        (OLD.sales_type_id <=> NEW.sales_type_id) AND
        (OLD.exchange_rate_basis_id <=> NEW.exchange_rate_basis_id) AND
        (OLD.tax_number <=> NEW.tax_number) AND
        (OLD.status <=> NEW.status) AND
        (OLD.activity_level <=> NEW.activity_level) AND
        (OLD.remarks <=> NEW.remarks)
        -- 注意：不比较created_at、updated_at、deleted_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            created_at,
            updated_at
        ) VALUES (
            'customers',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'code', NEW.code
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 5. 新增：categories表的UPDATE触发器
DROP TRIGGER IF EXISTS trg_categories_after_update;

DELIMITER $$
CREATE TRIGGER trg_categories_after_update
    AFTER UPDATE ON categories
    FOR EACH ROW
BEGIN
    -- 🔧 只有在业务字段真正发生变化时才记录到sync_change_log
    IF NOT (
        (OLD.company_code <=> NEW.company_code) AND
        (OLD.category_code <=> NEW.category_code) AND
        (OLD.locale <=> NEW.locale) AND
        (OLD.category_name <=> NEW.category_name) AND
        (OLD.remark <=> NEW.remark)
        -- 注意：不比较created_at、updated_at、deleted_at字段
    ) THEN
        INSERT INTO sync_change_log (
            table_name,
            change_type,
            pk_json,
            created_at,
            updated_at
        ) VALUES (
            'categories',
            'UPDATE',
            JSON_OBJECT(
                'company_code', NEW.company_code,
                'category_code', NEW.category_code,
                'locale', NEW.locale
            ),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 6. 保持INSERT触发器不变（materials表）
DROP TRIGGER IF EXISTS trg_materials_after_insert;

DELIMITER $$
CREATE TRIGGER trg_materials_after_insert
    AFTER INSERT ON materials
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'materials',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'material_code', NEW.material_code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 7. 保持DELETE触发器不变（materials表）
DROP TRIGGER IF EXISTS trg_materials_after_delete;

DELIMITER $$
CREATE TRIGGER trg_materials_after_delete
    AFTER DELETE ON materials
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'materials',
        'DELETE',
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'material_code', OLD.material_code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 8. material_translations表的INSERT触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_insert;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_insert
    AFTER INSERT ON material_translations
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'material_translations',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'material_code', NEW.material_code,
            'locale', NEW.locale
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 9. material_translations表的DELETE触发器
DROP TRIGGER IF EXISTS trg_material_translations_after_delete;

DELIMITER $$
CREATE TRIGGER trg_material_translations_after_delete
    AFTER DELETE ON material_translations
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'material_translations',
        'DELETE',
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'material_code', OLD.material_code,
            'locale', OLD.locale
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 10. BOM表的INSERT触发器（注意：实际表名为BOM，sync_change_log中记录也为BOM）
DROP TRIGGER IF EXISTS trg_BOM_after_insert;

DELIMITER $$
CREATE TRIGGER trg_BOM_after_insert
    AFTER INSERT ON BOM
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'BOM',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'parent_material_code', NEW.parent_material_code,
            'child_material_code', NEW.child_material_code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 11. BOM表的DELETE触发器（注意：实际表名为BOM，sync_change_log中记录也为BOM）
DROP TRIGGER IF EXISTS trg_BOM_after_delete;

DELIMITER $$
CREATE TRIGGER trg_BOM_after_delete
    AFTER DELETE ON BOM
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'BOM',
        'DELETE',
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'parent_material_code', OLD.parent_material_code,
            'child_material_code', OLD.child_material_code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 12. 新增：customers表的INSERT触发器
DROP TRIGGER IF EXISTS trg_customers_after_insert;

DELIMITER $$
CREATE TRIGGER trg_customers_after_insert
    AFTER INSERT ON customers
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'customers',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'code', NEW.code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 13. 新增：customers表的DELETE触发器
DROP TRIGGER IF EXISTS trg_customers_after_delete;

DELIMITER $$
CREATE TRIGGER trg_customers_after_delete
    AFTER DELETE ON customers
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'customers',
        'DELETE',
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'code', OLD.code
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 14. 新增：categories表的INSERT触发器
DROP TRIGGER IF EXISTS trg_categories_after_insert;

DELIMITER $$
CREATE TRIGGER trg_categories_after_insert
    AFTER INSERT ON categories
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'categories',
        'INSERT',
        JSON_OBJECT(
            'company_code', NEW.company_code,
            'category_code', NEW.category_code,
            'locale', NEW.locale
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 15. 新增：categories表的DELETE触发器
DROP TRIGGER IF EXISTS trg_categories_after_delete;

DELIMITER $$
CREATE TRIGGER trg_categories_after_delete
    AFTER DELETE ON categories
    FOR EACH ROW
BEGIN
    INSERT INTO sync_change_log (
        table_name,
        change_type,
        pk_json,
        created_at,
        updated_at
    ) VALUES (
        'categories',
        'DELETE',
        JSON_OBJECT(
            'company_code', OLD.company_code,
            'category_code', OLD.category_code,
            'locale', OLD.locale
        ),
        NOW(),
        NOW()
    );
END$$
DELIMITER ;

-- 16. 创建sync_change_log表的自动清理机制
DROP PROCEDURE IF EXISTS CleanupSyncChangeLog;

DELIMITER $$
CREATE PROCEDURE CleanupSyncChangeLog()
BEGIN
    DECLARE record_count INT;
    
    -- 删除30天前的记录
    DELETE FROM sync_change_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    GET DIAGNOSTICS record_count = ROW_COUNT;
    
    -- 记录清理日志
    IF record_count > 0 THEN
        INSERT INTO sync_logs (
            table_name,
            sync_type,
            status,
            records_processed,
            start_time,
            end_time,
            message,
            created_at,
            updated_at
        ) VALUES (
            'sync_change_log',
            'cleanup',
            'success',
            record_count,
            NOW(),
            NOW(),
            CONCAT('自动清理了 ', record_count, ' 条30天前的记录'),
            NOW(),
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 17. 创建清理无效UPDATE记录的存储过程
DROP PROCEDURE IF EXISTS CleanupInvalidUpdates;

DELIMITER $$
CREATE PROCEDURE CleanupInvalidUpdates()
BEGIN
    DECLARE record_count INT;
    
    -- 清理2025-06-28 11:38:56时间点的批量UPDATE记录（这些可能是无效的全量更新）
    DELETE FROM sync_change_log 
    WHERE table_name = 'materials' 
    AND change_type = 'UPDATE' 
    AND created_at BETWEEN '2025-06-28 11:38:00' AND '2025-06-28 11:39:00';
    
    GET DIAGNOSTICS record_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO sync_logs (
        table_name,
        sync_type,
        status,
        records_processed,
        start_time,
        end_time,
        message,
        created_at,
        updated_at
    ) VALUES (
        'sync_change_log',
        'cleanup_invalid',
        'success',
        record_count,
        NOW(),
        NOW(),
        CONCAT('清理了 ', record_count, ' 条无效的materials UPDATE记录'),
        NOW(),
        NOW()
    );
    
    SELECT CONCAT('清理了 ', record_count, ' 条无效的UPDATE记录') AS result;
END$$
DELIMITER ;

-- 使用说明
/*
触发器完整覆盖说明：

1. **支持的表**：
   - materials：物料主数据
   - material_translations：物料翻译数据
   - BOM：BOM结构数据（注意：数据库表名为大写'BOM'，sync_change_log中记录现在也为大写'BOM'）
   - customers：客户数据
   - categories：物料分类数据

2. **触发器功能**：
   - INSERT触发器：记录新增操作
   - UPDATE触发器：只有真正的业务字段变化才记录
   - DELETE触发器：记录删除操作（软删除也会触发）

3. **UPDATE优化**：
   - 使用 <=> 操作符处理NULL值比较
   - 排除了只有updated_at字段变化的情况
   - 只有真正的业务数据变化才会记录到sync_change_log

4. **主键设计**：
   - materials: company_code + material_code
   - material_translations: company_code + material_code + locale
   - BOM: company_code + parent_material_code + child_material_code
   - customers: company_code + code
   - categories: company_code + category_code + locale

5. **使用方法**：
   ```sql
   -- 在生产环境执行此脚本来创建/更新所有触发器
   source optimize_mysql_triggers.sql;
   
   -- 清理历史无效数据
   CALL CleanupInvalidUpdates();
   
   -- 定期清理历史数据（可以加入cron）
   CALL CleanupSyncChangeLog();
   ```

6. **监控效果**：
   - 执行后观察sync_change_log表的增长速度
   - 应该会显著减少无意义的UPDATE记录
   - 只有真正的数据变化才会被记录
   - 第三方系统通过API可以获取所有业务表的变更情况

7. **验证方法**：
   - 对任意业务表进行INSERT/UPDATE/DELETE操作
   - 检查sync_change_log表中是否有对应记录
   - 确认主键JSON格式正确
   - 验证第三方API能正确获取变更数据
*/ 